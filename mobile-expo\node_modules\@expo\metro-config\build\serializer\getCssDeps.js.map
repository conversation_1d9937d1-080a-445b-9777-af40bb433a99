{"version": 3, "file": "getCssDeps.js", "names": ["_js", "data", "require", "_path", "_interopRequireDefault", "_css", "_hash", "obj", "__esModule", "default", "STATIC_EXPORT_DIRECTORY", "filterJsModules", "dependencies", "processModuleFilter", "projectRoot", "assets", "module", "values", "isJsModule", "getJsOutput", "type", "path", "relative", "push", "getCssSerialAssets", "cssMetadata", "getCssMetadata", "contents", "code", "filename", "join", "fileNameFromContents", "filepath", "src", "originFilename", "source", "metadata", "hmrId", "pathToHtmlSafeName", "_module$output$", "output", "css", "Error", "JSON", "stringify", "getFileName", "hashString", "basename", "replace"], "sources": ["../../src/serializer/getCssDeps.ts"], "sourcesContent": ["import type { Modu<PERSON> } from 'metro';\nimport { getJsOutput, isJsModule } from 'metro/src/DeltaBundler/Serializers/helpers/js';\nimport path from 'path';\n\nimport { pathToHtmlSafeName } from '../transform-worker/css';\nimport { hashString } from '../utils/hash';\nimport { SerialAsset } from './serializerAssets';\n\nexport type ReadOnlyDependencies<T = any> = ReadonlyMap<string, Module<T>>;\n\ntype Options = {\n  processModuleFilter: (modules: Module) => boolean;\n  assetPlugins: readonly string[];\n  platform?: string | null;\n  projectRoot: string;\n  publicPath: string;\n};\n\ntype MetroModuleCSSMetadata = {\n  code: string;\n  lineCount: number;\n  map: any[];\n};\n\n// s = static\nconst STATIC_EXPORT_DIRECTORY = '_expo/static/css';\n\nexport type JSModule = Module<{\n  data: {\n    code: string;\n    map: unknown;\n    lineCount: number;\n    css?: {\n      code: string;\n      map: unknown;\n      lineCount: number;\n    };\n  };\n  type: 'js/module';\n}>;\n\nexport function filterJsModules(\n  dependencies: ReadOnlyDependencies,\n  { processModuleFilter, projectRoot }: Pick<Options, 'projectRoot' | 'processModuleFilter'>\n) {\n  const assets: JSModule[] = [];\n\n  for (const module of dependencies.values()) {\n    if (\n      isJsModule(module) &&\n      processModuleFilter(module) &&\n      getJsOutput(module).type === 'js/module' &&\n      path.relative(projectRoot, module.path) !== 'package.json'\n    ) {\n      assets.push(module as JSModule);\n    }\n  }\n  return assets;\n}\n\nexport function getCssSerialAssets<T extends any>(\n  dependencies: ReadOnlyDependencies<T>,\n  { processModuleFilter, projectRoot }: Pick<Options, 'projectRoot' | 'processModuleFilter'>\n): SerialAsset[] {\n  const assets: SerialAsset[] = [];\n\n  for (const module of filterJsModules(dependencies, { processModuleFilter, projectRoot })) {\n    const cssMetadata = getCssMetadata(module);\n    if (cssMetadata) {\n      const contents = cssMetadata.code;\n      const filename = path.join(\n        // Consistent location\n        STATIC_EXPORT_DIRECTORY,\n        // Hashed file contents + name for caching\n        fileNameFromContents({\n          filepath: module.path,\n          src: contents,\n        }) + '.css'\n      );\n      const originFilename = path.relative(projectRoot, module.path);\n      assets.push({\n        type: 'css',\n        originFilename,\n        filename,\n        source: contents,\n        metadata: {\n          hmrId: pathToHtmlSafeName(originFilename),\n        },\n      });\n    }\n  }\n\n  return assets;\n}\n\nfunction getCssMetadata(module: JSModule): MetroModuleCSSMetadata | null {\n  const data = module.output[0]?.data;\n  if (data && typeof data === 'object' && 'css' in data) {\n    if (typeof data.css !== 'object' || !('code' in (data as any).css)) {\n      throw new Error(\n        `Unexpected CSS metadata in Metro module (${module.path}): ${JSON.stringify(data.css)}`\n      );\n    }\n    return data.css as MetroModuleCSSMetadata;\n  }\n  return null;\n}\n\nexport function fileNameFromContents({ filepath, src }: { filepath: string; src: string }): string {\n  return getFileName(filepath) + '-' + hashString(filepath + src);\n}\n\nexport function getFileName(module: string) {\n  return path.basename(module).replace(/\\.[^.]+$/, '');\n}\n"], "mappings": ";;;;;;;;;AACA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2C,SAAAG,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAmB3C;AACA,MAAMG,uBAAuB,GAAG,kBAAkB;AAgB3C,SAASC,eAAeA,CAC7BC,YAAkC,EAClC;EAAEC,mBAAmB;EAAEC;AAAkE,CAAC,EAC1F;EACA,MAAMC,MAAkB,GAAG,EAAE;EAE7B,KAAK,MAAMC,MAAM,IAAIJ,YAAY,CAACK,MAAM,EAAE,EAAE;IAC1C,IACE,IAAAC,gBAAU,EAACF,MAAM,CAAC,IAClBH,mBAAmB,CAACG,MAAM,CAAC,IAC3B,IAAAG,iBAAW,EAACH,MAAM,CAAC,CAACI,IAAI,KAAK,WAAW,IACxCC,eAAI,CAACC,QAAQ,CAACR,WAAW,EAAEE,MAAM,CAACK,IAAI,CAAC,KAAK,cAAc,EAC1D;MACAN,MAAM,CAACQ,IAAI,CAACP,MAAM,CAAa;IACjC;EACF;EACA,OAAOD,MAAM;AACf;AAEO,SAASS,kBAAkBA,CAChCZ,YAAqC,EACrC;EAAEC,mBAAmB;EAAEC;AAAkE,CAAC,EAC3E;EACf,MAAMC,MAAqB,GAAG,EAAE;EAEhC,KAAK,MAAMC,MAAM,IAAIL,eAAe,CAACC,YAAY,EAAE;IAAEC,mBAAmB;IAAEC;EAAY,CAAC,CAAC,EAAE;IACxF,MAAMW,WAAW,GAAGC,cAAc,CAACV,MAAM,CAAC;IAC1C,IAAIS,WAAW,EAAE;MACf,MAAME,QAAQ,GAAGF,WAAW,CAACG,IAAI;MACjC,MAAMC,QAAQ,GAAGR,eAAI,CAACS,IAAI;MACxB;MACApB,uBAAuB;MACvB;MACAqB,oBAAoB,CAAC;QACnBC,QAAQ,EAAEhB,MAAM,CAACK,IAAI;QACrBY,GAAG,EAAEN;MACP,CAAC,CAAC,GAAG,MAAM,CACZ;MACD,MAAMO,cAAc,GAAGb,eAAI,CAACC,QAAQ,CAACR,WAAW,EAAEE,MAAM,CAACK,IAAI,CAAC;MAC9DN,MAAM,CAACQ,IAAI,CAAC;QACVH,IAAI,EAAE,KAAK;QACXc,cAAc;QACdL,QAAQ;QACRM,MAAM,EAAER,QAAQ;QAChBS,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAAC,yBAAkB,EAACJ,cAAc;QAC1C;MACF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOnB,MAAM;AACf;AAEA,SAASW,cAAcA,CAACV,MAAgB,EAAiC;EAAA,IAAAuB,eAAA;EACvE,MAAMtC,IAAI,IAAAsC,eAAA,GAAGvB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,cAAAD,eAAA,uBAAhBA,eAAA,CAAkBtC,IAAI;EACnC,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAIA,IAAI,EAAE;IACrD,IAAI,OAAOA,IAAI,CAACwC,GAAG,KAAK,QAAQ,IAAI,EAAE,MAAM,IAAKxC,IAAI,CAASwC,GAAG,CAAC,EAAE;MAClE,MAAM,IAAIC,KAAK,CACZ,4CAA2C1B,MAAM,CAACK,IAAK,MAAKsB,IAAI,CAACC,SAAS,CAAC3C,IAAI,CAACwC,GAAG,CAAE,EAAC,CACxF;IACH;IACA,OAAOxC,IAAI,CAACwC,GAAG;EACjB;EACA,OAAO,IAAI;AACb;AAEO,SAASV,oBAAoBA,CAAC;EAAEC,QAAQ;EAAEC;AAAuC,CAAC,EAAU;EACjG,OAAOY,WAAW,CAACb,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAAc,kBAAU,EAACd,QAAQ,GAAGC,GAAG,CAAC;AACjE;AAEO,SAASY,WAAWA,CAAC7B,MAAc,EAAE;EAC1C,OAAOK,eAAI,CAAC0B,QAAQ,CAAC/B,MAAM,CAAC,CAACgC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;AACtD"}