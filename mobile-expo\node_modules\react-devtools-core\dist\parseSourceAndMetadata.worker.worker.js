(()=>{var e={785:(e,t,r)=>{"use strict";var n=r(397);Object.defineProperty(t,"__esModule",{value:!0}),t.codeFrameColumns=l,t.default=function(e,t,r,i={}){if(!a){a=!0;const e="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";n.emitWarning?n.emitWarning(e,"DeprecationWarning"):(new Error(e).name="DeprecationWarning",console.warn(new Error(e)))}return l(e,{start:{column:r=Math.max(r,0),line:t}},i)};var i=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(r(7012));function s(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return s=function(){return e},e}let a=!1;const o=/\r\n|[\n\r\u2028\u2029]/;function l(e,t,r={}){const n=(r.highlightCode||r.forceColor)&&(0,i.shouldHighlight)(r),s=(0,i.getChalk)(r),a=function(e){return{gutter:e.grey,marker:e.red.bold,message:e.red.bold}}(s),l=(e,t)=>n?e(t):t,p=e.split(o),{start:c,end:u,markerLines:d}=function(e,t,r){const n=Object.assign({column:0,line:-1},e.start),i=Object.assign({},n,e.end),{linesAbove:s=2,linesBelow:a=3}=r||{},o=n.line,l=n.column,p=i.line,c=i.column;let u=Math.max(o-(s+1),0),d=Math.min(t.length,p+a);-1===o&&(u=0),-1===p&&(d=t.length);const h=p-o,f={};if(h)for(let e=0;e<=h;e++){const r=e+o;if(l)if(0===e){const e=t[r-1].length;f[r]=[l,e-l+1]}else if(e===h)f[r]=[0,c];else{const n=t[r-e].length;f[r]=[0,n]}else f[r]=!0}else f[o]=l===c?!l||[l,0]:[l,c-l];return{start:u,end:d,markerLines:f}}(t,p,r),h=t.start&&"number"==typeof t.start.column,f=String(u).length;let m=(n?(0,i.default)(e,r):e).split(o).slice(c,u).map(((e,t)=>{const n=c+1+t,i=` ${` ${n}`.slice(-f)} |`,s=d[n],o=!d[n+1];if(s){let t="";if(Array.isArray(s)){const n=e.slice(0,Math.max(s[0]-1,0)).replace(/[^\t]/g," "),p=s[1]||1;t=["\n ",l(a.gutter,i.replace(/\d/g," "))," ",n,l(a.marker,"^").repeat(p)].join(""),o&&r.message&&(t+=" "+l(a.message,r.message))}return[l(a.marker,">"),l(a.gutter,i),e.length>0?` ${e}`:"",t].join("")}return` ${l(a.gutter,i)}${e.length>0?` ${e}`:""}`})).join("\n");return r.message&&!h&&(m=`${" ".repeat(f+1)}${r.message}\n${m}`),n?s.reset(m):m}},6163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function({node:e,parent:t,scope:r,id:i},a=!1){if(e.id)return;if(!s.isObjectProperty(t)&&!s.isObjectMethod(t,{kind:"method"})||t.computed&&!s.isLiteral(t.key)){if(s.isVariableDeclarator(t)){if(i=t.id,s.isIdentifier(i)&&!a){const t=r.parent.getBinding(i.name);if(t&&t.constant&&r.getBinding(i.name)===t)return e.id=s.cloneNode(i),void(e.id[s.NOT_LOCAL_BINDING]=!0)}}else if(s.isAssignmentExpression(t,{operator:"="}))i=t.left;else if(!i)return}else i=t.key;let o;return i&&s.isLiteral(i)?o=function(e){return s.isNullLiteral(e)?"null":s.isRegExpLiteral(e)?`_${e.pattern}_${e.flags}`:s.isTemplateLiteral(e)?e.quasis.map((e=>e.value.raw)).join(""):void 0!==e.value?e.value+"":""}(i):i&&s.isIdentifier(i)&&(o=i.name),void 0!==o?(o=s.toBindingIdentifierName(o),(i=s.identifier(o))[s.NOT_LOCAL_BINDING]=!0,function(e,t,r,i){if(e.selfReference){if(!i.hasBinding(r.name)||i.hasGlobal(r.name)){if(!s.isFunction(t))return;let e=l;t.generator&&(e=p);const a=e({FUNCTION:t,FUNCTION_ID:r,FUNCTION_KEY:i.generateUidIdentifier(r.name)}).expression,o=a.callee.body.body[0].params;for(let e=0,r=(0,n.default)(t);e<r;e++)o.push(i.generateUidIdentifier("x"));return a}i.rename(r.name)}t.id=r,i.getProgramParent().references[r.name]=!0}(function(e,t,r){const n={selfAssignment:!1,selfReference:!1,outerDeclar:r.getBindingIdentifier(t),references:[],name:t},i=r.getOwnBinding(t);return i?"param"===i.kind&&(n.selfReference=!0):(n.outerDeclar||r.hasGlobal(t))&&r.traverse(e,c,n),n}(e,o,r),e,i,r)||e):void 0};var n=o(r(2442)),i=o(r(6205)),s=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var s=n?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(r(17));function a(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return a=function(){return e},e}function o(e){return e&&e.__esModule?e:{default:e}}const l=(0,i.default)("\n  (function (FUNCTION_KEY) {\n    function FUNCTION_ID() {\n      return FUNCTION_KEY.apply(this, arguments);\n    }\n\n    FUNCTION_ID.toString = function () {\n      return FUNCTION_KEY.toString();\n    }\n\n    return FUNCTION_ID;\n  })(FUNCTION)\n"),p=(0,i.default)("\n  (function (FUNCTION_KEY) {\n    function* FUNCTION_ID() {\n      return yield* FUNCTION_KEY.apply(this, arguments);\n    }\n\n    FUNCTION_ID.toString = function () {\n      return FUNCTION_KEY.toString();\n    };\n\n    return FUNCTION_ID;\n  })(FUNCTION)\n"),c={"ReferencedIdentifier|BindingIdentifier"(e,t){e.node.name===t.name&&e.scope.getBindingIdentifier(t.name)===t.outerDeclar&&(t.selfReference=!0,e.stop())}}},2442:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){const t=e.params;for(let e=0;e<t.length;e++){const r=t[e];if(n.isAssignmentPattern(r)||n.isRestElement(r))return e}return t.length};var n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var a=n?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(r,s,a):r[s]=e[s]}return r.default=e,t&&t.set(e,r),r}(r(17));function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}},7224:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(!e.isExportDeclaration())throw new Error("Only export declarations can be split.");const t=e.isExportDefaultDeclaration(),r=e.get("declaration"),i=r.isClassDeclaration();if(t){const t=r.isFunctionDeclaration()||i,s=r.isScope()?r.scope.parent:r.scope;let a=r.node.id,o=!1;a||(o=!0,a=s.generateUidIdentifier("default"),(t||r.isFunctionExpression()||r.isClassExpression())&&(r.node.id=n.cloneNode(a)));const l=t?r:n.variableDeclaration("var",[n.variableDeclarator(n.cloneNode(a),r.node)]),p=n.exportNamedDeclaration(null,[n.exportSpecifier(n.cloneNode(a),n.identifier("default"))]);return e.insertAfter(p),e.replaceWith(l),o&&s.registerDeclaration(e),e}if(e.get("specifiers").length>0)throw new Error("It doesn't make sense to split exported specifiers.");const s=r.getOuterBindingIdentifiers(),a=Object.keys(s).map((e=>n.exportSpecifier(n.identifier(e),n.identifier(e)))),o=n.exportNamedDeclaration(null,a);return e.insertAfter(o),e.replaceWith(r.node),e};var n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var a=n?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(r,s,a):r[s]=e[s]}return r.default=e,t&&t.set(e,r),r}(r(17));function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}},9104:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isIdentifierStart=p,t.isIdentifierChar=c,t.isIdentifierName=function(e){let t=!0;for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if(55296==(64512&n)&&r+1<e.length){const t=e.charCodeAt(++r);56320==(64512&t)&&(n=65536+((1023&n)<<10)+(1023&t))}if(t){if(t=!1,!p(n))return!1}else if(!c(n))return!1}return!t};let r="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",n="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";const i=new RegExp("["+r+"]"),s=new RegExp("["+r+n+"]");r=n=null;const a=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],o=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function l(e,t){let r=65536;for(let n=0,i=t.length;n<i;n+=2){if(r+=t[n],r>e)return!1;if(r+=t[n+1],r>=e)return!0}return!1}function p(e){return e<65?36===e:e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&i.test(String.fromCharCode(e)):l(e,a)))}function c(e){return e<48?36===e:e<58||!(e<65)&&(e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&s.test(String.fromCharCode(e)):l(e,a)||l(e,o))))}},7075:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isIdentifierName",{enumerable:!0,get:function(){return n.isIdentifierName}}),Object.defineProperty(t,"isIdentifierChar",{enumerable:!0,get:function(){return n.isIdentifierChar}}),Object.defineProperty(t,"isIdentifierStart",{enumerable:!0,get:function(){return n.isIdentifierStart}}),Object.defineProperty(t,"isReservedWord",{enumerable:!0,get:function(){return i.isReservedWord}}),Object.defineProperty(t,"isStrictBindOnlyReservedWord",{enumerable:!0,get:function(){return i.isStrictBindOnlyReservedWord}}),Object.defineProperty(t,"isStrictBindReservedWord",{enumerable:!0,get:function(){return i.isStrictBindReservedWord}}),Object.defineProperty(t,"isStrictReservedWord",{enumerable:!0,get:function(){return i.isStrictReservedWord}}),Object.defineProperty(t,"isKeyword",{enumerable:!0,get:function(){return i.isKeyword}});var n=r(9104),i=r(4400)},4400:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isReservedWord=s,t.isStrictReservedWord=a,t.isStrictBindOnlyReservedWord=o,t.isStrictBindReservedWord=function(e,t){return a(e,t)||o(e)},t.isKeyword=function(e){return r.has(e)};const r=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"]),n=new Set(["implements","interface","let","package","private","protected","public","static","yield"]),i=new Set(["eval","arguments"]);function s(e,t){return t&&"await"===e||"enum"===e}function a(e,t){return s(e,t)||n.has(e)}function o(e){return i.has(e)}},7012:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shouldHighlight=c,t.getChalk=u,t.default=function(e,t={}){if(c(t)){return function(e,t){let r="";for(const{type:n,value:i}of p(t)){const t=e[n];r+=t?i.split(o).map((e=>t(e))).join("\n"):i}return r}({keyword:(r=u(t)).cyan,capitalized:r.yellow,jsxIdentifier:r.yellow,punctuator:r.yellow,number:r.magenta,string:r.green,regex:r.magenta,comment:r.grey,invalid:r.white.bgRed.bold},e)}var r;return e};var n=r(7075);const i=r(8631),s=r(1859),a=new Set(["as","async","from","get","of","set"]),o=/\r\n|[\n\r\u2028\u2029]/,l=/^[()[\]{}]$/;let p;{const e=/^[a-z][\w-]*$/i,t=function(t,r,i){if("name"===t.type){if((0,n.isKeyword)(t.value)||(0,n.isStrictReservedWord)(t.value,!0)||a.has(t.value))return"keyword";if(e.test(t.value)&&("<"===i[r-1]||"</"==i.substr(r-2,2)))return"jsxIdentifier";if(t.value[0]!==t.value[0].toLowerCase())return"capitalized"}return"punctuator"===t.type&&l.test(t.value)?"bracket":"invalid"!==t.type||"@"!==t.value&&"#"!==t.value?t.type:"punctuator"};p=function*(e){let r;for(;r=i.default.exec(e);){const n=i.matchToToken(r);yield{type:t(n,r.index,e),value:n.value}}}}function c(e){return!!s.supportsColor||e.forceColor}function u(e){return e.forceColor?new s.constructor({enabled:!0,level:1}):s}},5332:(e,t,r)=>{"use strict";e=r.nmd(e);const n=r(5536),i=(e,t)=>function(){return`[${e.apply(n,arguments)+t}m`},s=(e,t)=>function(){const r=e.apply(n,arguments);return`[${38+t};5;${r}m`},a=(e,t)=>function(){const r=e.apply(n,arguments);return`[${38+t};2;${r[0]};${r[1]};${r[2]}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.grey=t.color.gray;for(const r of Object.keys(t)){const n=t[r];for(const r of Object.keys(n)){const i=n[r];t[r]={open:`[${i[0]}m`,close:`[${i[1]}m`},n[r]=t[r],e.set(i[0],i[1])}Object.defineProperty(t,r,{value:n,enumerable:!1}),Object.defineProperty(t,"codes",{value:e,enumerable:!1})}const r=e=>e,o=(e,t,r)=>[e,t,r];t.color.close="[39m",t.bgColor.close="[49m",t.color.ansi={ansi:i(r,0)},t.color.ansi256={ansi256:s(r,0)},t.color.ansi16m={rgb:a(o,0)},t.bgColor.ansi={ansi:i(r,10)},t.bgColor.ansi256={ansi256:s(r,10)},t.bgColor.ansi16m={rgb:a(o,10)};for(let e of Object.keys(n)){if("object"!=typeof n[e])continue;const r=n[e];"ansi16"===e&&(e="ansi"),"ansi16"in r&&(t.color.ansi[e]=i(r.ansi16,0),t.bgColor.ansi[e]=i(r.ansi16,10)),"ansi256"in r&&(t.color.ansi256[e]=s(r.ansi256,0),t.bgColor.ansi256[e]=s(r.ansi256,10)),"rgb"in r&&(t.color.ansi16m[e]=a(r.rgb,0),t.bgColor.ansi16m[e]=a(r.rgb,10))}return t}})},1859:(e,t,r)=>{"use strict";var n=r(397);const i=r(1014),s=r(5332),a=r(3182).stdout,o=r(6805),l="win32"===n.platform&&!(n.env.TERM||"").toLowerCase().startsWith("xterm"),p=["ansi","ansi","ansi256","ansi16m"],c=new Set(["gray"]),u=Object.create(null);function d(e,t){t=t||{};const r=a?a.level:0;e.level=void 0===t.level?r:t.level,e.enabled="enabled"in t?t.enabled:e.level>0}function h(e){if(!this||!(this instanceof h)||this.template){const t={};return d(t,e),t.template=function(){const e=[].slice.call(arguments);return T.apply(null,[t.template].concat(e))},Object.setPrototypeOf(t,h.prototype),Object.setPrototypeOf(t.template,t),t.template.constructor=h,t.template}d(this,e)}l&&(s.blue.open="[94m");for(const e of Object.keys(s))s[e].closeRe=new RegExp(i(s[e].close),"g"),u[e]={get(){const t=s[e];return m.call(this,this._styles?this._styles.concat(t):[t],this._empty,e)}};u.visible={get(){return m.call(this,this._styles||[],!0,"visible")}},s.color.closeRe=new RegExp(i(s.color.close),"g");for(const e of Object.keys(s.color.ansi))c.has(e)||(u[e]={get(){const t=this.level;return function(){const r={open:s.color[p[t]][e].apply(null,arguments),close:s.color.close,closeRe:s.color.closeRe};return m.call(this,this._styles?this._styles.concat(r):[r],this._empty,e)}}});s.bgColor.closeRe=new RegExp(i(s.bgColor.close),"g");for(const e of Object.keys(s.bgColor.ansi))c.has(e)||(u["bg"+e[0].toUpperCase()+e.slice(1)]={get(){const t=this.level;return function(){const r={open:s.bgColor[p[t]][e].apply(null,arguments),close:s.bgColor.close,closeRe:s.bgColor.closeRe};return m.call(this,this._styles?this._styles.concat(r):[r],this._empty,e)}}});const f=Object.defineProperties((()=>{}),u);function m(e,t,r){const n=function(){return y.apply(n,arguments)};n._styles=e,n._empty=t;const i=this;return Object.defineProperty(n,"level",{enumerable:!0,get:()=>i.level,set(e){i.level=e}}),Object.defineProperty(n,"enabled",{enumerable:!0,get:()=>i.enabled,set(e){i.enabled=e}}),n.hasGrey=this.hasGrey||"gray"===r||"grey"===r,n.__proto__=f,n}function y(){const e=arguments,t=e.length;let r=String(arguments[0]);if(0===t)return"";if(t>1)for(let n=1;n<t;n++)r+=" "+e[n];if(!this.enabled||this.level<=0||!r)return this._empty?"":r;const n=s.dim.open;l&&this.hasGrey&&(s.dim.open="");for(const e of this._styles.slice().reverse())r=e.open+r.replace(e.closeRe,e.open)+e.close,r=r.replace(/\r?\n/g,`${e.close}$&${e.open}`);return s.dim.open=n,r}function T(e,t){if(!Array.isArray(t))return[].slice.call(arguments,1).join(" ");const r=[].slice.call(arguments,2),n=[t.raw[0]];for(let e=1;e<t.length;e++)n.push(String(r[e-1]).replace(/[{}\\]/g,"\\$&")),n.push(String(t.raw[e]));return o(e,n.join(""))}Object.defineProperties(h.prototype,u),e.exports=h(),e.exports.supportsColor=a,e.exports.default=e.exports},6805:e=>{"use strict";const t=/(?:\\(u[a-f\d]{4}|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,r=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,n=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,i=/\\(u[a-f\d]{4}|x[a-f\d]{2}|.)|([^\\])/gi,s=new Map([["n","\n"],["r","\r"],["t","\t"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e",""],["a",""]]);function a(e){return"u"===e[0]&&5===e.length||"x"===e[0]&&3===e.length?String.fromCharCode(parseInt(e.slice(1),16)):s.get(e)||e}function o(e,t){const r=[],s=t.trim().split(/\s*,\s*/g);let o;for(const t of s)if(isNaN(t)){if(!(o=t.match(n)))throw new Error(`Invalid Chalk template style argument: ${t} (in style '${e}')`);r.push(o[2].replace(i,((e,t,r)=>t?a(t):r)))}else r.push(Number(t));return r}function l(e){r.lastIndex=0;const t=[];let n;for(;null!==(n=r.exec(e));){const e=n[1];if(n[2]){const r=o(e,n[2]);t.push([e].concat(r))}else t.push([e])}return t}function p(e,t){const r={};for(const e of t)for(const t of e.styles)r[t[0]]=e.inverse?null:t.slice(1);let n=e;for(const e of Object.keys(r))if(Array.isArray(r[e])){if(!(e in n))throw new Error(`Unknown Chalk style: ${e}`);n=r[e].length>0?n[e].apply(n,r[e]):n[e]}return n}e.exports=(e,r)=>{const n=[],i=[];let s=[];if(r.replace(t,((t,r,o,c,u,d)=>{if(r)s.push(a(r));else if(c){const t=s.join("");s=[],i.push(0===n.length?t:p(e,n)(t)),n.push({inverse:o,styles:l(c)})}else if(u){if(0===n.length)throw new Error("Found extraneous } in Chalk template literal");i.push(p(e,n)(s.join(""))),s=[],n.pop()}else s.push(d)})),i.push(s.join("")),n.length>0){const e=`Chalk template literal is missing ${n.length} closing bracket${1===n.length?"":"s"} (\`}\`)`;throw new Error(e)}return i.join("")}},8325:(e,t,r)=>{var n=r(4730),i={};for(var s in n)n.hasOwnProperty(s)&&(i[n[s]]=s);var a=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var o in a)if(a.hasOwnProperty(o)){if(!("channels"in a[o]))throw new Error("missing channels property: "+o);if(!("labels"in a[o]))throw new Error("missing channel labels property: "+o);if(a[o].labels.length!==a[o].channels)throw new Error("channel and label counts mismatch: "+o);var l=a[o].channels,p=a[o].labels;delete a[o].channels,delete a[o].labels,Object.defineProperty(a[o],"channels",{value:l}),Object.defineProperty(a[o],"labels",{value:p})}a.rgb.hsl=function(e){var t,r,n=e[0]/255,i=e[1]/255,s=e[2]/255,a=Math.min(n,i,s),o=Math.max(n,i,s),l=o-a;return o===a?t=0:n===o?t=(i-s)/l:i===o?t=2+(s-n)/l:s===o&&(t=4+(n-i)/l),(t=Math.min(60*t,360))<0&&(t+=360),r=(a+o)/2,[t,100*(o===a?0:r<=.5?l/(o+a):l/(2-o-a)),100*r]},a.rgb.hsv=function(e){var t,r,n,i,s,a=e[0]/255,o=e[1]/255,l=e[2]/255,p=Math.max(a,o,l),c=p-Math.min(a,o,l),u=function(e){return(p-e)/6/c+.5};return 0===c?i=s=0:(s=c/p,t=u(a),r=u(o),n=u(l),a===p?i=n-r:o===p?i=1/3+t-n:l===p&&(i=2/3+r-t),i<0?i+=1:i>1&&(i-=1)),[360*i,100*s,100*p]},a.rgb.hwb=function(e){var t=e[0],r=e[1],n=e[2];return[a.rgb.hsl(e)[0],1/255*Math.min(t,Math.min(r,n))*100,100*(n=1-1/255*Math.max(t,Math.max(r,n)))]},a.rgb.cmyk=function(e){var t,r=e[0]/255,n=e[1]/255,i=e[2]/255;return[100*((1-r-(t=Math.min(1-r,1-n,1-i)))/(1-t)||0),100*((1-n-t)/(1-t)||0),100*((1-i-t)/(1-t)||0),100*t]},a.rgb.keyword=function(e){var t=i[e];if(t)return t;var r,s,a,o=1/0;for(var l in n)if(n.hasOwnProperty(l)){var p=(s=e,a=n[l],Math.pow(s[0]-a[0],2)+Math.pow(s[1]-a[1],2)+Math.pow(s[2]-a[2],2));p<o&&(o=p,r=l)}return r},a.keyword.rgb=function(e){return n[e]},a.rgb.xyz=function(e){var t=e[0]/255,r=e[1]/255,n=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)+.1805*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)),100*(.2126*t+.7152*r+.0722*n),100*(.0193*t+.1192*r+.9505*n)]},a.rgb.lab=function(e){var t=a.rgb.xyz(e),r=t[0],n=t[1],i=t[2];return n/=100,i/=108.883,r=(r/=95.047)>.008856?Math.pow(r,1/3):7.787*r+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(r-n),200*(n-(i=i>.008856?Math.pow(i,1/3):7.787*i+16/116))]},a.hsl.rgb=function(e){var t,r,n,i,s,a=e[0]/360,o=e[1]/100,l=e[2]/100;if(0===o)return[s=255*l,s,s];t=2*l-(r=l<.5?l*(1+o):l+o-l*o),i=[0,0,0];for(var p=0;p<3;p++)(n=a+1/3*-(p-1))<0&&n++,n>1&&n--,s=6*n<1?t+6*(r-t)*n:2*n<1?r:3*n<2?t+(r-t)*(2/3-n)*6:t,i[p]=255*s;return i},a.hsl.hsv=function(e){var t=e[0],r=e[1]/100,n=e[2]/100,i=r,s=Math.max(n,.01);return r*=(n*=2)<=1?n:2-n,i*=s<=1?s:2-s,[t,100*(0===n?2*i/(s+i):2*r/(n+r)),(n+r)/2*100]},a.hsv.rgb=function(e){var t=e[0]/60,r=e[1]/100,n=e[2]/100,i=Math.floor(t)%6,s=t-Math.floor(t),a=255*n*(1-r),o=255*n*(1-r*s),l=255*n*(1-r*(1-s));switch(n*=255,i){case 0:return[n,l,a];case 1:return[o,n,a];case 2:return[a,n,l];case 3:return[a,o,n];case 4:return[l,a,n];case 5:return[n,a,o]}},a.hsv.hsl=function(e){var t,r,n,i=e[0],s=e[1]/100,a=e[2]/100,o=Math.max(a,.01);return n=(2-s)*a,r=s*o,[i,100*(r=(r/=(t=(2-s)*o)<=1?t:2-t)||0),100*(n/=2)]},a.hwb.rgb=function(e){var t,r,n,i,s,a,o,l=e[0]/360,p=e[1]/100,c=e[2]/100,u=p+c;switch(u>1&&(p/=u,c/=u),n=6*l-(t=Math.floor(6*l)),0!=(1&t)&&(n=1-n),i=p+n*((r=1-c)-p),t){default:case 6:case 0:s=r,a=i,o=p;break;case 1:s=i,a=r,o=p;break;case 2:s=p,a=r,o=i;break;case 3:s=p,a=i,o=r;break;case 4:s=i,a=p,o=r;break;case 5:s=r,a=p,o=i}return[255*s,255*a,255*o]},a.cmyk.rgb=function(e){var t=e[0]/100,r=e[1]/100,n=e[2]/100,i=e[3]/100;return[255*(1-Math.min(1,t*(1-i)+i)),255*(1-Math.min(1,r*(1-i)+i)),255*(1-Math.min(1,n*(1-i)+i))]},a.xyz.rgb=function(e){var t,r,n,i=e[0]/100,s=e[1]/100,a=e[2]/100;return r=-.9689*i+1.8758*s+.0415*a,n=.0557*i+-.204*s+1.057*a,t=(t=3.2406*i+-1.5372*s+-.4986*a)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,[255*(t=Math.min(Math.max(0,t),1)),255*(r=Math.min(Math.max(0,r),1)),255*(n=Math.min(Math.max(0,n),1))]},a.xyz.lab=function(e){var t=e[0],r=e[1],n=e[2];return r/=100,n/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(t-r),200*(r-(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116))]},a.lab.xyz=function(e){var t,r,n,i=e[0];t=e[1]/500+(r=(i+16)/116),n=r-e[2]/200;var s=Math.pow(r,3),a=Math.pow(t,3),o=Math.pow(n,3);return r=s>.008856?s:(r-16/116)/7.787,t=a>.008856?a:(t-16/116)/7.787,n=o>.008856?o:(n-16/116)/7.787,[t*=95.047,r*=100,n*=108.883]},a.lab.lch=function(e){var t,r=e[0],n=e[1],i=e[2];return(t=360*Math.atan2(i,n)/2/Math.PI)<0&&(t+=360),[r,Math.sqrt(n*n+i*i),t]},a.lch.lab=function(e){var t,r=e[0],n=e[1];return t=e[2]/360*2*Math.PI,[r,n*Math.cos(t),n*Math.sin(t)]},a.rgb.ansi16=function(e){var t=e[0],r=e[1],n=e[2],i=1 in arguments?arguments[1]:a.rgb.hsv(e)[2];if(0===(i=Math.round(i/50)))return 30;var s=30+(Math.round(n/255)<<2|Math.round(r/255)<<1|Math.round(t/255));return 2===i&&(s+=60),s},a.hsv.ansi16=function(e){return a.rgb.ansi16(a.hsv.rgb(e),e[2])},a.rgb.ansi256=function(e){var t=e[0],r=e[1],n=e[2];return t===r&&r===n?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5)},a.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var r=.5*(1+~~(e>50));return[(1&t)*r*255,(t>>1&1)*r*255,(t>>2&1)*r*255]},a.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var r;return e-=16,[Math.floor(e/36)/5*255,Math.floor((r=e%36)/6)/5*255,r%6/5*255]},a.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},a.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var r=t[0];3===t[0].length&&(r=r.split("").map((function(e){return e+e})).join(""));var n=parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},a.rgb.hcg=function(e){var t,r=e[0]/255,n=e[1]/255,i=e[2]/255,s=Math.max(Math.max(r,n),i),a=Math.min(Math.min(r,n),i),o=s-a;return t=o<=0?0:s===r?(n-i)/o%6:s===n?2+(i-r)/o:4+(r-n)/o+4,t/=6,[360*(t%=1),100*o,100*(o<1?a/(1-o):0)]},a.hsl.hcg=function(e){var t,r=e[1]/100,n=e[2]/100,i=0;return(t=n<.5?2*r*n:2*r*(1-n))<1&&(i=(n-.5*t)/(1-t)),[e[0],100*t,100*i]},a.hsv.hcg=function(e){var t=e[1]/100,r=e[2]/100,n=t*r,i=0;return n<1&&(i=(r-n)/(1-n)),[e[0],100*n,100*i]},a.hcg.rgb=function(e){var t=e[0]/360,r=e[1]/100,n=e[2]/100;if(0===r)return[255*n,255*n,255*n];var i,s=[0,0,0],a=t%1*6,o=a%1,l=1-o;switch(Math.floor(a)){case 0:s[0]=1,s[1]=o,s[2]=0;break;case 1:s[0]=l,s[1]=1,s[2]=0;break;case 2:s[0]=0,s[1]=1,s[2]=o;break;case 3:s[0]=0,s[1]=l,s[2]=1;break;case 4:s[0]=o,s[1]=0,s[2]=1;break;default:s[0]=1,s[1]=0,s[2]=l}return i=(1-r)*n,[255*(r*s[0]+i),255*(r*s[1]+i),255*(r*s[2]+i)]},a.hcg.hsv=function(e){var t=e[1]/100,r=t+e[2]/100*(1-t),n=0;return r>0&&(n=t/r),[e[0],100*n,100*r]},a.hcg.hsl=function(e){var t=e[1]/100,r=e[2]/100*(1-t)+.5*t,n=0;return r>0&&r<.5?n=t/(2*r):r>=.5&&r<1&&(n=t/(2*(1-r))),[e[0],100*n,100*r]},a.hcg.hwb=function(e){var t=e[1]/100,r=t+e[2]/100*(1-t);return[e[0],100*(r-t),100*(1-r)]},a.hwb.hcg=function(e){var t=e[1]/100,r=1-e[2]/100,n=r-t,i=0;return n<1&&(i=(r-n)/(1-n)),[e[0],100*n,100*i]},a.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},a.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},a.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},a.gray.hsl=a.gray.hsv=function(e){return[0,0,e[0]]},a.gray.hwb=function(e){return[0,100,e[0]]},a.gray.cmyk=function(e){return[0,0,0,e[0]]},a.gray.lab=function(e){return[e[0],0,0]},a.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),r=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(r.length)+r},a.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},5536:(e,t,r)=>{var n=r(8325),i=r(6094),s={};Object.keys(n).forEach((function(e){s[e]={},Object.defineProperty(s[e],"channels",{value:n[e].channels}),Object.defineProperty(s[e],"labels",{value:n[e].labels});var t=i(e);Object.keys(t).forEach((function(r){var n=t[r];s[e][r]=function(e){var t=function(t){if(null==t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var r=e(t);if("object"==typeof r)for(var n=r.length,i=0;i<n;i++)r[i]=Math.round(r[i]);return r};return"conversion"in e&&(t.conversion=e.conversion),t}(n),s[e][r].raw=function(e){var t=function(t){return null==t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(n)}))})),e.exports=s},6094:(e,t,r)=>{var n=r(8325);function i(e,t){return function(r){return t(e(r))}}function s(e,t){for(var r=[t[e].parent,e],s=n[t[e].parent][e],a=t[e].parent;t[a].parent;)r.unshift(t[a].parent),s=i(n[t[a].parent][a],s),a=t[a].parent;return s.conversion=r,s}e.exports=function(e){for(var t=function(e){var t=function(){for(var e={},t=Object.keys(n),r=t.length,i=0;i<r;i++)e[t[i]]={distance:-1,parent:null};return e}(),r=[e];for(t[e].distance=0;r.length;)for(var i=r.pop(),s=Object.keys(n[i]),a=s.length,o=0;o<a;o++){var l=s[o],p=t[l];-1===p.distance&&(p.distance=t[i].distance+1,p.parent=i,r.unshift(l))}return t}(e),r={},i=Object.keys(t),a=i.length,o=0;o<a;o++){var l=i[o];null!==t[l].parent&&(r[l]=s(l,t))}return r}},4730:e=>{"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},3182:(e,t,r)=>{"use strict";var n=r(397);const i=r(2037),s=r(9341),a=n.env;let o;function l(e){const t=function(e){if(!1===o)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!e.isTTY&&!0!==o)return 0;const t=o?1:0;if("win32"===n.platform){const e=i.release().split(".");return Number(n.versions.node.split(".")[0])>=8&&Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some((e=>e in a))||"codeship"===a.CI_NAME?1:t;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){const e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:(a.TERM,t)}(e);return function(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}(t)}s("no-color")||s("no-colors")||s("color=false")?o=!1:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(o=!0),"FORCE_COLOR"in a&&(o=0===a.FORCE_COLOR.length||0!==parseInt(a.FORCE_COLOR,10)),e.exports={supportsColor:l,stdout:l(n.stdout),stderr:l(n.stderr)}},4494:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=!0,n=!0,i=!0,s=!0;class a{constructor(e,t={}){this.label=void 0,this.keyword=void 0,this.beforeExpr=void 0,this.startsExpr=void 0,this.rightAssociative=void 0,this.isLoop=void 0,this.isAssign=void 0,this.prefix=void 0,this.postfix=void 0,this.binop=void 0,this.updateContext=void 0,this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.rightAssociative=!!t.rightAssociative,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=null!=t.binop?t.binop:null,this.updateContext=null}}const o=new Map;function l(e,t={}){t.keyword=e;const r=new a(e,t);return o.set(e,r),r}function p(e,t){return new a(e,{beforeExpr:r,binop:t})}const c={num:new a("num",{startsExpr:n}),bigint:new a("bigint",{startsExpr:n}),decimal:new a("decimal",{startsExpr:n}),regexp:new a("regexp",{startsExpr:n}),string:new a("string",{startsExpr:n}),name:new a("name",{startsExpr:n}),privateName:new a("#name",{startsExpr:n}),eof:new a("eof"),bracketL:new a("[",{beforeExpr:r,startsExpr:n}),bracketHashL:new a("#[",{beforeExpr:r,startsExpr:n}),bracketBarL:new a("[|",{beforeExpr:r,startsExpr:n}),bracketR:new a("]"),bracketBarR:new a("|]"),braceL:new a("{",{beforeExpr:r,startsExpr:n}),braceBarL:new a("{|",{beforeExpr:r,startsExpr:n}),braceHashL:new a("#{",{beforeExpr:r,startsExpr:n}),braceR:new a("}"),braceBarR:new a("|}"),parenL:new a("(",{beforeExpr:r,startsExpr:n}),parenR:new a(")"),comma:new a(",",{beforeExpr:r}),semi:new a(";",{beforeExpr:r}),colon:new a(":",{beforeExpr:r}),doubleColon:new a("::",{beforeExpr:r}),dot:new a("."),question:new a("?",{beforeExpr:r}),questionDot:new a("?."),arrow:new a("=>",{beforeExpr:r}),template:new a("template"),ellipsis:new a("...",{beforeExpr:r}),backQuote:new a("`",{startsExpr:n}),dollarBraceL:new a("${",{beforeExpr:r,startsExpr:n}),at:new a("@"),hash:new a("#",{startsExpr:n}),interpreterDirective:new a("#!..."),eq:new a("=",{beforeExpr:r,isAssign:!0}),assign:new a("_=",{beforeExpr:r,isAssign:!0}),incDec:new a("++/--",{prefix:s,postfix:!0,startsExpr:n}),bang:new a("!",{beforeExpr:r,prefix:s,startsExpr:n}),tilde:new a("~",{beforeExpr:r,prefix:s,startsExpr:n}),pipeline:p("|>",0),nullishCoalescing:p("??",1),logicalOR:p("||",1),logicalAND:p("&&",2),bitwiseOR:p("|",3),bitwiseXOR:p("^",4),bitwiseAND:p("&",5),equality:p("==/!=/===/!==",6),relational:p("</>/<=/>=",7),bitShift:p("<</>>/>>>",8),plusMin:new a("+/-",{beforeExpr:r,binop:9,prefix:s,startsExpr:n}),modulo:new a("%",{beforeExpr:r,binop:10,startsExpr:n}),star:new a("*",{binop:10}),slash:p("/",10),exponent:new a("**",{beforeExpr:r,binop:11,rightAssociative:!0}),_break:l("break"),_case:l("case",{beforeExpr:r}),_catch:l("catch"),_continue:l("continue"),_debugger:l("debugger"),_default:l("default",{beforeExpr:r}),_do:l("do",{isLoop:i,beforeExpr:r}),_else:l("else",{beforeExpr:r}),_finally:l("finally"),_for:l("for",{isLoop:i}),_function:l("function",{startsExpr:n}),_if:l("if"),_return:l("return",{beforeExpr:r}),_switch:l("switch"),_throw:l("throw",{beforeExpr:r,prefix:s,startsExpr:n}),_try:l("try"),_var:l("var"),_const:l("const"),_while:l("while",{isLoop:i}),_with:l("with"),_new:l("new",{beforeExpr:r,startsExpr:n}),_this:l("this",{startsExpr:n}),_super:l("super",{startsExpr:n}),_class:l("class",{startsExpr:n}),_extends:l("extends",{beforeExpr:r}),_export:l("export"),_import:l("import",{startsExpr:n}),_null:l("null",{startsExpr:n}),_true:l("true",{startsExpr:n}),_false:l("false",{startsExpr:n}),_in:l("in",{beforeExpr:r,binop:7}),_instanceof:l("instanceof",{beforeExpr:r,binop:7}),_typeof:l("typeof",{beforeExpr:r,prefix:s,startsExpr:n}),_void:l("void",{beforeExpr:r,prefix:s,startsExpr:n}),_delete:l("delete",{beforeExpr:r,prefix:s,startsExpr:n})},u=/\r\n?|[\n\u2028\u2029]/,d=new RegExp(u.source,"g");function h(e){switch(e){case 10:case 13:case 8232:case 8233:return!0;default:return!1}}const f=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;function m(e){switch(e){case 9:case 11:case 12:case 32:case 160:case 5760:case 8192:case 8193:case 8194:case 8195:case 8196:case 8197:case 8198:case 8199:case 8200:case 8201:case 8202:case 8239:case 8287:case 12288:case 65279:return!0;default:return!1}}class y{constructor(e,t){this.line=void 0,this.column=void 0,this.line=e,this.column=t}}class T{constructor(e,t){this.start=void 0,this.end=void 0,this.filename=void 0,this.identifierName=void 0,this.start=e,this.end=t}}class b{constructor(){this.sawUnambiguousESM=!1,this.ambiguousScriptDifferentAst=!1}hasPlugin(e){return this.plugins.has(e)}getPluginOption(e,t){if(this.hasPlugin(e))return this.plugins.get(e)[t]}}function g(e){return e[e.length-1]}class S extends b{addComment(e){this.filename&&(e.loc.filename=this.filename),this.state.trailingComments.push(e),this.state.leadingComments.push(e)}adjustCommentsAfterTrailingComma(e,t,r){if(0===this.state.leadingComments.length)return;let n=null,i=t.length;for(;null===n&&i>0;)n=t[--i];if(null===n)return;for(let e=0;e<this.state.leadingComments.length;e++)this.state.leadingComments[e].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(e,1),e--);const s=[];for(let t=0;t<this.state.leadingComments.length;t++){const n=this.state.leadingComments[t];n.end<e.end?(s.push(n),r||(this.state.leadingComments.splice(t,1),t--)):(void 0===e.trailingComments&&(e.trailingComments=[]),e.trailingComments.push(n))}r&&(this.state.leadingComments=[]),s.length>0?n.trailingComments=s:void 0!==n.trailingComments&&(n.trailingComments=[])}processComment(e){if("Program"===e.type&&e.body.length>0)return;const t=this.state.commentStack;let r,n,i,s,a;if(this.state.trailingComments.length>0)this.state.trailingComments[0].start>=e.end?(i=this.state.trailingComments,this.state.trailingComments=[]):this.state.trailingComments.length=0;else if(t.length>0){const r=g(t);r.trailingComments&&r.trailingComments[0].start>=e.end&&(i=r.trailingComments,delete r.trailingComments)}for(t.length>0&&g(t).start>=e.start&&(r=t.pop());t.length>0&&g(t).start>=e.start;)n=t.pop();if(!n&&r&&(n=r),r)switch(e.type){case"ObjectExpression":this.adjustCommentsAfterTrailingComma(e,e.properties);break;case"ObjectPattern":this.adjustCommentsAfterTrailingComma(e,e.properties,!0);break;case"CallExpression":this.adjustCommentsAfterTrailingComma(e,e.arguments);break;case"ArrayExpression":this.adjustCommentsAfterTrailingComma(e,e.elements);break;case"ArrayPattern":this.adjustCommentsAfterTrailingComma(e,e.elements,!0)}else this.state.commentPreviousNode&&("ImportSpecifier"===this.state.commentPreviousNode.type&&"ImportSpecifier"!==e.type||"ExportSpecifier"===this.state.commentPreviousNode.type&&"ExportSpecifier"!==e.type)&&this.adjustCommentsAfterTrailingComma(e,[this.state.commentPreviousNode]);if(n){if(n.leadingComments)if(n!==e&&n.leadingComments.length>0&&g(n.leadingComments).end<=e.start)e.leadingComments=n.leadingComments,delete n.leadingComments;else for(s=n.leadingComments.length-2;s>=0;--s)if(n.leadingComments[s].end<=e.start){e.leadingComments=n.leadingComments.splice(0,s+1);break}}else if(this.state.leadingComments.length>0)if(g(this.state.leadingComments).end<=e.start){if(this.state.commentPreviousNode)for(a=0;a<this.state.leadingComments.length;a++)this.state.leadingComments[a].end<this.state.commentPreviousNode.end&&(this.state.leadingComments.splice(a,1),a--);this.state.leadingComments.length>0&&(e.leadingComments=this.state.leadingComments,this.state.leadingComments=[])}else{for(s=0;s<this.state.leadingComments.length&&!(this.state.leadingComments[s].end>e.start);s++);const t=this.state.leadingComments.slice(0,s);t.length&&(e.leadingComments=t),i=this.state.leadingComments.slice(s),0===i.length&&(i=null)}if(this.state.commentPreviousNode=e,i)if(i.length&&i[0].start>=e.start&&g(i).end<=e.end)e.innerComments=i;else{const t=i.findIndex((t=>t.end>=e.end));t>0?(e.innerComments=i.slice(0,t),e.trailingComments=i.slice(t)):e.trailingComments=i}t.push(e)}}const x=Object.freeze({SyntaxError:"BABEL_PARSER_SYNTAX_ERROR",SourceTypeModuleError:"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED"}),E=v({AccessorIsGenerator:"A %0ter cannot be a generator.",ArgumentsInClass:"'arguments' is only allowed in functions and class methods.",AsyncFunctionInSingleStatementContext:"Async functions can only be declared at the top level or inside a block.",AwaitBindingIdentifier:"Can not use 'await' as identifier inside an async function.",AwaitBindingIdentifierInStaticBlock:"Can not use 'await' as identifier inside a static block.",AwaitExpressionFormalParameter:"'await' is not allowed in async function parameters.",AwaitNotInAsyncContext:"'await' is only allowed within async functions and at the top levels of modules.",AwaitNotInAsyncFunction:"'await' is only allowed within async functions.",BadGetterArity:"A 'get' accesor must not have any formal parameters.",BadSetterArity:"A 'set' accesor must have exactly one formal parameter.",BadSetterRestParameter:"A 'set' accesor function argument must not be a rest parameter.",ConstructorClassField:"Classes may not have a field named 'constructor'.",ConstructorClassPrivateField:"Classes may not have a private field named '#constructor'.",ConstructorIsAccessor:"Class constructor may not be an accessor.",ConstructorIsAsync:"Constructor can't be an async function.",ConstructorIsGenerator:"Constructor can't be a generator.",DeclarationMissingInitializer:"'%0' require an initialization value.",DecoratorBeforeExport:"Decorators must be placed *before* the 'export' keyword. You can set the 'decoratorsBeforeExport' option to false to use the 'export @decorator class {}' syntax.",DecoratorConstructor:"Decorators can't be used with a constructor. Did you mean '@dec class { ... }'?",DecoratorExportClass:"Using the export keyword between a decorator and a class is not allowed. Please use `export @dec class` instead.",DecoratorSemicolon:"Decorators must not be followed by a semicolon.",DecoratorStaticBlock:"Decorators can't be used with a static block.",DeletePrivateField:"Deleting a private field is not allowed.",DestructureNamedImport:"ES2015 named imports do not destructure. Use another statement for destructuring after the import.",DuplicateConstructor:"Duplicate constructor in the same class.",DuplicateDefaultExport:"Only one default export allowed per module.",DuplicateExport:"`%0` has already been exported. Exported identifiers must be unique.",DuplicateProto:"Redefinition of __proto__ property.",DuplicateRegExpFlags:"Duplicate regular expression flag.",ElementAfterRest:"Rest element must be last element.",EscapedCharNotAnIdentifier:"Invalid Unicode escape.",ExportBindingIsString:"A string literal cannot be used as an exported binding without `from`.\n- Did you mean `export { '%0' as '%1' } from 'some-module'`?",ExportDefaultFromAsIdentifier:"'from' is not allowed as an identifier after 'export default'.",ForInOfLoopInitializer:"'%0' loop variable declaration may not have an initializer.",ForOfAsync:"The left-hand side of a for-of loop may not be 'async'.",ForOfLet:"The left-hand side of a for-of loop may not start with 'let'.",GeneratorInSingleStatementContext:"Generators can only be declared at the top level or inside a block.",IllegalBreakContinue:"Unsyntactic %0.",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list.",IllegalReturn:"'return' outside of function.",ImportBindingIsString:'A string literal cannot be used as an imported binding.\n- Did you mean `import { "%0" as foo }`?',ImportCallArgumentTrailingComma:"Trailing comma is disallowed inside import(...) arguments.",ImportCallArity:"`import()` requires exactly %0.",ImportCallNotNewExpression:"Cannot use new with import(...).",ImportCallSpreadArgument:"`...` is not allowed in `import()`.",InvalidBigIntLiteral:"Invalid BigIntLiteral.",InvalidCodePoint:"Code point out of bounds.",InvalidDecimal:"Invalid decimal.",InvalidDigit:"Expected number in radix %0.",InvalidEscapeSequence:"Bad character escape sequence.",InvalidEscapeSequenceTemplate:"Invalid escape sequence in template.",InvalidEscapedReservedWord:"Escape sequence in keyword %0.",InvalidIdentifier:"Invalid identifier %0.",InvalidLhs:"Invalid left-hand side in %0.",InvalidLhsBinding:"Binding invalid left-hand side in %0.",InvalidNumber:"Invalid number.",InvalidOrMissingExponent:"Floating-point numbers require a valid exponent after the 'e'.",InvalidOrUnexpectedToken:"Unexpected character '%0'.",InvalidParenthesizedAssignment:"Invalid parenthesized assignment pattern.",InvalidPrivateFieldResolution:"Private name #%0 is not defined.",InvalidPropertyBindingPattern:"Binding member expression.",InvalidRecordProperty:"Only properties and spread elements are allowed in record definitions.",InvalidRestAssignmentPattern:"Invalid rest operator's argument.",LabelRedeclaration:"Label '%0' is already declared.",LetInLexicalBinding:"'let' is not allowed to be used as a name in 'let' or 'const' declarations.",LineTerminatorBeforeArrow:"No line break is allowed before '=>'.",MalformedRegExpFlags:"Invalid regular expression flag.",MissingClassName:"A class name is required.",MissingEqInAssignment:"Only '=' operator can be used for specifying default value.",MissingSemicolon:"Missing semicolon.",MissingUnicodeEscape:"Expecting Unicode escape sequence \\uXXXX.",MixingCoalesceWithLogical:"Nullish coalescing operator(??) requires parens when mixing with logical operators.",ModuleAttributeDifferentFromType:"The only accepted module attribute is `type`.",ModuleAttributeInvalidValue:"Only string literals are allowed as module attribute values.",ModuleAttributesWithDuplicateKeys:'Duplicate key "%0" is not allowed in module attributes.',ModuleExportNameHasLoneSurrogate:"An export name cannot include a lone surrogate, found '\\u%0'.",ModuleExportUndefined:"Export '%0' is not defined.",MultipleDefaultsInSwitch:"Multiple default clauses.",NewlineAfterThrow:"Illegal newline after throw.",NoCatchOrFinally:"Missing catch or finally clause.",NumberIdentifier:"Identifier directly after number.",NumericSeparatorInEscapeSequence:"Numeric separators are not allowed inside unicode escape sequences or hex escape sequences.",ObsoleteAwaitStar:"'await*' has been removed from the async functions proposal. Use Promise.all() instead.",OptionalChainingNoNew:"Constructors in/after an Optional Chain are not allowed.",OptionalChainingNoTemplate:"Tagged Template Literals are not allowed in optionalChain.",OverrideOnConstructor:"'override' modifier cannot appear on a constructor declaration.",ParamDupe:"Argument name clash.",PatternHasAccessor:"Object pattern can't contain getter or setter.",PatternHasMethod:"Object pattern can't contain methods.",PipelineBodyNoArrow:'Unexpected arrow "=>" after pipeline body; arrow function in pipeline body must be parenthesized.',PipelineBodySequenceExpression:"Pipeline body may not be a comma-separated sequence expression.",PipelineHeadSequenceExpression:"Pipeline head should not be a comma-separated sequence expression.",PipelineTopicUnused:"Pipeline is in topic style but does not use topic reference.",PrimaryTopicNotAllowed:"Topic reference was used in a lexical context without topic binding.",PrimaryTopicRequiresSmartPipeline:"Primary Topic Reference found but pipelineOperator not passed 'smart' for 'proposal' option.",PrivateInExpectedIn:"Private names are only allowed in property accesses (`obj.#%0`) or in `in` expressions (`#%0 in obj`).",PrivateNameRedeclaration:"Duplicate private name #%0.",RecordExpressionBarIncorrectEndSyntaxType:"Record expressions ending with '|}' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionBarIncorrectStartSyntaxType:"Record expressions starting with '{|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",RecordExpressionHashIncorrectStartSyntaxType:"Record expressions starting with '#{' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",RecordNoProto:"'__proto__' is not allowed in Record expressions.",RestTrailingComma:"Unexpected trailing comma after rest element.",SloppyFunction:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement.",StaticPrototype:"Classes may not have static property named prototype.",StrictDelete:"Deleting local variable in strict mode.",StrictEvalArguments:"Assigning to '%0' in strict mode.",StrictEvalArgumentsBinding:"Binding '%0' in strict mode.",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block.",StrictNumericEscape:"The only valid numeric escape in strict mode is '\\0'.",StrictOctalLiteral:"Legacy octal literals are not allowed in strict mode.",StrictWith:"'with' in strict mode.",SuperNotAllowed:"`super()` is only valid inside a class constructor of a subclass. Maybe a typo in the method name ('constructor') or not extending another class?",SuperPrivateField:"Private fields can't be accessed on super.",TrailingDecorator:"Decorators must be attached to a class element.",TupleExpressionBarIncorrectEndSyntaxType:"Tuple expressions ending with '|]' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionBarIncorrectStartSyntaxType:"Tuple expressions starting with '[|' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'bar'.",TupleExpressionHashIncorrectStartSyntaxType:"Tuple expressions starting with '#[' are only allowed when the 'syntaxType' option of the 'recordAndTuple' plugin is set to 'hash'.",UnexpectedArgumentPlaceholder:"Unexpected argument placeholder.",UnexpectedAwaitAfterPipelineBody:'Unexpected "await" after pipeline body; await must have parentheses in minimal proposal.',UnexpectedDigitAfterHash:"Unexpected digit after hash token.",UnexpectedImportExport:"'import' and 'export' may only appear at the top level.",UnexpectedKeyword:"Unexpected keyword '%0'.",UnexpectedLeadingDecorator:"Leading decorators must be attached to a class declaration.",UnexpectedLexicalDeclaration:"Lexical declaration cannot appear in a single-statement context.",UnexpectedNewTarget:"`new.target` can only be used in functions or class properties.",UnexpectedNumericSeparator:"A numeric separator is only allowed between two digits.",UnexpectedPrivateField:"Private names can only be used as the name of a class element (i.e. class C { #p = 42; #m() {} } )\n or a property of member expression (i.e. this.#p).",UnexpectedReservedWord:"Unexpected reserved word '%0'.",UnexpectedSuper:"'super' is only allowed in object methods and classes.",UnexpectedToken:"Unexpected token '%0'.",UnexpectedTokenUnaryExponentiation:"Illegal expression. Wrap left hand side or entire exponentiation in parentheses.",UnsupportedBind:"Binding should be performed on object property.",UnsupportedDecoratorExport:"A decorated export must export a class declaration.",UnsupportedDefaultExport:"Only expressions, functions or classes are allowed as the `default` export.",UnsupportedImport:"`import` can only be used in `import()` or `import.meta`.",UnsupportedMetaProperty:"The only valid meta property for %0 is %0.%1.",UnsupportedParameterDecorator:"Decorators cannot be used to decorate parameters.",UnsupportedPropertyDecorator:"Decorators cannot be used to decorate object literal properties.",UnsupportedSuper:"'super' can only be used with function calls (i.e. super()) or in property accesses (i.e. super.prop or super[prop]).",UnterminatedComment:"Unterminated comment.",UnterminatedRegExp:"Unterminated regular expression.",UnterminatedString:"Unterminated string constant.",UnterminatedTemplate:"Unterminated template.",VarRedeclaration:"Identifier '%0' has already been declared.",YieldBindingIdentifier:"Can not use 'yield' as identifier inside a generator.",YieldInParameter:"Yield expression is not allowed in formal parameters.",ZeroDigitNumericSeparator:"Numeric separator can not be used after leading 0."},x.SyntaxError),P=v({ImportMetaOutsideModule:"import.meta may appear only with 'sourceType: \"module\"'",ImportOutsideModule:"'import' and 'export' may appear only with 'sourceType: \"module\"'"},x.SourceTypeModuleError);function v(e,t){const r={};return Object.keys(e).forEach((n=>{r[n]=Object.freeze({code:t,reasonCode:n,template:e[n]})})),Object.freeze(r)}class A extends S{getLocationForPosition(e){let t;return t=e===this.state.start?this.state.startLoc:e===this.state.lastTokStart?this.state.lastTokStartLoc:e===this.state.end?this.state.endLoc:e===this.state.lastTokEnd?this.state.lastTokEndLoc:function(e,t){let r,n=1,i=0;for(d.lastIndex=0;(r=d.exec(e))&&r.index<t;)n++,i=d.lastIndex;return new y(n,t-i)}(this.input,e),t}raise(e,{code:t,reasonCode:r,template:n},...i){return this.raiseWithData(e,{code:t,reasonCode:r},n,...i)}raiseOverwrite(e,{code:t,template:r},...n){const i=this.getLocationForPosition(e),s=r.replace(/%(\d+)/g,((e,t)=>n[t]))+` (${i.line}:${i.column})`;if(this.options.errorRecovery){const t=this.state.errors;for(let r=t.length-1;r>=0;r--){const n=t[r];if(n.pos===e)return Object.assign(n,{message:s});if(n.pos<e)break}}return this._raise({code:t,loc:i,pos:e},s)}raiseWithData(e,t,r,...n){const i=this.getLocationForPosition(e),s=r.replace(/%(\d+)/g,((e,t)=>n[t]))+` (${i.line}:${i.column})`;return this._raise(Object.assign({loc:i,pos:e},t),s)}_raise(e,t){const r=new SyntaxError(t);if(Object.assign(r,e),this.options.errorRecovery)return this.isLookahead||this.state.errors.push(r),r;throw r}}class w{constructor(e,t,r,n){this.token=void 0,this.isExpr=void 0,this.preserveSpace=void 0,this.override=void 0,this.token=e,this.isExpr=!!t,this.preserveSpace=!!r,this.override=n}}const O={braceStatement:new w("{",!1),braceExpression:new w("{",!0),recordExpression:new w("#{",!0),templateQuasi:new w("${",!1),parenStatement:new w("(",!1),parenExpression:new w("(",!0),template:new w("`",!0,!0,(e=>e.readTmplToken())),functionExpression:new w("function",!0),functionStatement:new w("function",!1)};c.parenR.updateContext=c.braceR.updateContext=function(){if(1===this.state.context.length)return void(this.state.exprAllowed=!0);let e=this.state.context.pop();e===O.braceStatement&&"function"===this.curContext().token&&(e=this.state.context.pop()),this.state.exprAllowed=!e.isExpr},c.name.updateContext=function(e){let t=!1;e!==c.dot&&("of"!==this.state.value||this.state.exprAllowed||e===c._function||e===c._class||(t=!0)),this.state.exprAllowed=t},c.braceL.updateContext=function(e){this.state.context.push(this.braceIsBlock(e)?O.braceStatement:O.braceExpression),this.state.exprAllowed=!0},c.dollarBraceL.updateContext=function(){this.state.context.push(O.templateQuasi),this.state.exprAllowed=!0},c.parenL.updateContext=function(e){const t=e===c._if||e===c._for||e===c._with||e===c._while;this.state.context.push(t?O.parenStatement:O.parenExpression),this.state.exprAllowed=!0},c.incDec.updateContext=function(){},c._function.updateContext=c._class.updateContext=function(e){!e.beforeExpr||e===c.semi||e===c._else||e===c._return&&this.hasPrecedingLineBreak()||(e===c.colon||e===c.braceL)&&this.curContext()===O.b_stat?this.state.context.push(O.functionStatement):this.state.context.push(O.functionExpression),this.state.exprAllowed=!1},c.backQuote.updateContext=function(){this.curContext()===O.template?this.state.context.pop():this.state.context.push(O.template),this.state.exprAllowed=!1},c.braceHashL.updateContext=function(){this.state.context.push(O.recordExpression),this.state.exprAllowed=!0};let I="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",N="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";const C=new RegExp("["+I+"]"),k=new RegExp("["+I+N+"]");I=N=null;const D=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],_=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function L(e,t){let r=65536;for(let n=0,i=t.length;n<i;n+=2){if(r+=t[n],r>e)return!1;if(r+=t[n+1],r>=e)return!0}return!1}function M(e){return e<65?36===e:e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&C.test(String.fromCharCode(e)):L(e,D)))}function j(e){return e<48?36===e:e<58||!(e<65)&&(e<=90||(e<97?95===e:e<=122||(e<=65535?e>=170&&k.test(String.fromCharCode(e)):L(e,D)||L(e,_))))}const F=new Set(["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"]),B=new Set(["implements","interface","let","package","private","protected","public","static","yield"]),R=new Set(["eval","arguments"]);function U(e,t){return t&&"await"===e||"enum"===e}function V(e,t){return U(e,t)||B.has(e)}function K(e){return R.has(e)}function q(e,t){return V(e,t)||K(e)}function Y(e){return F.has(e)}const W=/^in(stanceof)?$/;class X{constructor(e){this.flags=void 0,this.var=[],this.lexical=[],this.functions=[],this.flags=e}}class J{constructor(e,t){this.scopeStack=[],this.undefinedExports=new Map,this.undefinedPrivateNames=new Map,this.raise=e,this.inModule=t}get inFunction(){return(2&this.currentVarScope().flags)>0}get allowSuper(){return(16&this.currentThisScope().flags)>0}get allowDirectSuper(){return(32&this.currentThisScope().flags)>0}get inClass(){return(64&this.currentThisScope().flags)>0}get inStaticBlock(){return(128&this.currentThisScope().flags)>0}get inNonArrowFunction(){return(2&this.currentThisScope().flags)>0}get treatFunctionsAsVar(){return this.treatFunctionsAsVarInScope(this.currentScope())}createScope(e){return new X(e)}enter(e){this.scopeStack.push(this.createScope(e))}exit(){this.scopeStack.pop()}treatFunctionsAsVarInScope(e){return!!(2&e.flags||!this.inModule&&1&e.flags)}declareName(e,t,r){let n=this.currentScope();if(8&t||16&t)this.checkRedeclarationInScope(n,e,t,r),16&t?n.functions.push(e):n.lexical.push(e),8&t&&this.maybeExportDefined(n,e);else if(4&t)for(let i=this.scopeStack.length-1;i>=0&&(n=this.scopeStack[i],this.checkRedeclarationInScope(n,e,t,r),n.var.push(e),this.maybeExportDefined(n,e),!(259&n.flags));--i);this.inModule&&1&n.flags&&this.undefinedExports.delete(e)}maybeExportDefined(e,t){this.inModule&&1&e.flags&&this.undefinedExports.delete(t)}checkRedeclarationInScope(e,t,r,n){this.isRedeclaredInScope(e,t,r)&&this.raise(n,E.VarRedeclaration,t)}isRedeclaredInScope(e,t,r){return!!(1&r)&&(8&r?e.lexical.indexOf(t)>-1||e.functions.indexOf(t)>-1||e.var.indexOf(t)>-1:16&r?e.lexical.indexOf(t)>-1||!this.treatFunctionsAsVarInScope(e)&&e.var.indexOf(t)>-1:e.lexical.indexOf(t)>-1&&!(8&e.flags&&e.lexical[0]===t)||!this.treatFunctionsAsVarInScope(e)&&e.functions.indexOf(t)>-1)}checkLocalExport(e){-1===this.scopeStack[0].lexical.indexOf(e.name)&&-1===this.scopeStack[0].var.indexOf(e.name)&&-1===this.scopeStack[0].functions.indexOf(e.name)&&this.undefinedExports.set(e.name,e.start)}currentScope(){return this.scopeStack[this.scopeStack.length-1]}currentVarScope(){for(let e=this.scopeStack.length-1;;e--){const t=this.scopeStack[e];if(259&t.flags)return t}}currentThisScope(){for(let e=this.scopeStack.length-1;;e--){const t=this.scopeStack[e];if((259&t.flags||64&t.flags)&&!(4&t.flags))return t}}}class H extends X{constructor(...e){super(...e),this.declareFunctions=[]}}class G extends J{createScope(e){return new H(e)}declareName(e,t,r){const n=this.currentScope();if(2048&t)return this.checkRedeclarationInScope(n,e,t,r),this.maybeExportDefined(n,e),void n.declareFunctions.push(e);super.declareName(...arguments)}isRedeclaredInScope(e,t,r){return!!super.isRedeclaredInScope(...arguments)||!!(2048&r)&&!e.declareFunctions.includes(t)&&(e.lexical.includes(t)||e.functions.includes(t))}checkLocalExport(e){-1===this.scopeStack[0].declareFunctions.indexOf(e.name)&&super.checkLocalExport(e)}}const z=new Set(["_","any","bool","boolean","empty","extends","false","interface","mixed","null","number","static","string","true","typeof","void"]),$=v({AmbiguousConditionalArrow:"Ambiguous expression: wrap the arrow functions in parentheses to disambiguate.",AmbiguousDeclareModuleKind:"Found both `declare module.exports` and `declare export` in the same module. Modules can only have 1 since they are either an ES module or they are a CommonJS module.",AssignReservedType:"Cannot overwrite reserved type %0.",DeclareClassElement:"The `declare` modifier can only appear on class fields.",DeclareClassFieldInitializer:"Initializers are not allowed in fields with the `declare` modifier.",DuplicateDeclareModuleExports:"Duplicate `declare module.exports` statement.",EnumBooleanMemberNotInitialized:"Boolean enum members need to be initialized. Use either `%0 = true,` or `%0 = false,` in enum `%1`.",EnumDuplicateMemberName:"Enum member names need to be unique, but the name `%0` has already been used before in enum `%1`.",EnumInconsistentMemberValues:"Enum `%0` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.",EnumInvalidExplicitType:"Enum type `%1` is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `%0`.",EnumInvalidExplicitTypeUnknownSupplied:"Supplied enum type is not valid. Use one of `boolean`, `number`, `string`, or `symbol` in enum `%0`.",EnumInvalidMemberInitializerPrimaryType:"Enum `%0` has type `%2`, so the initializer of `%1` needs to be a %2 literal.",EnumInvalidMemberInitializerSymbolType:"Symbol enum members cannot be initialized. Use `%1,` in enum `%0`.",EnumInvalidMemberInitializerUnknownType:"The enum member initializer for `%1` needs to be a literal (either a boolean, number, or string) in enum `%0`.",EnumInvalidMemberName:"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `%0`, consider using `%1`, in enum `%2`.",EnumNumberMemberNotInitialized:"Number enum members need to be initialized, e.g. `%1 = 1` in enum `%0`.",EnumStringMemberInconsistentlyInitailized:"String enum members need to consistently either all use initializers, or use no initializers, in enum `%0`.",GetterMayNotHaveThisParam:"A getter cannot have a `this` parameter.",ImportTypeShorthandOnlyInPureImport:"The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. It cannot be used with `import type` or `import typeof` statements.",InexactInsideExact:"Explicit inexact syntax cannot appear inside an explicit exact object type.",InexactInsideNonObject:"Explicit inexact syntax cannot appear in class or interface definitions.",InexactVariance:"Explicit inexact syntax cannot have variance.",InvalidNonTypeImportInDeclareModule:"Imports within a `declare module` body must always be `import type` or `import typeof`.",MissingTypeParamDefault:"Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",NestedDeclareModule:"`declare module` cannot be used inside another `declare module`.",NestedFlowComment:"Cannot have a flow comment inside another flow comment.",OptionalBindingPattern:"A binding pattern parameter cannot be optional in an implementation signature.",SetterMayNotHaveThisParam:"A setter cannot have a `this` parameter.",SpreadVariance:"Spread properties cannot have variance.",ThisParamAnnotationRequired:"A type annotation is required for the `this` parameter.",ThisParamBannedInConstructor:"Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",ThisParamMayNotBeOptional:"The `this` parameter cannot be optional.",ThisParamMustBeFirst:"The `this` parameter must be the first function parameter.",ThisParamNoDefault:"The `this` parameter may not have a default value.",TypeBeforeInitializer:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeCastInPattern:"The type cast expression is expected to be wrapped with parenthesis.",UnexpectedExplicitInexactInObject:"Explicit inexact syntax must appear at the end of an inexact object.",UnexpectedReservedType:"Unexpected reserved type %0.",UnexpectedReservedUnderscore:"`_` is only allowed as a type argument to call or new.",UnexpectedSpaceBetweenModuloChecks:"Spaces between `%` and `checks` are not allowed here.",UnexpectedSpreadType:"Spread operator cannot appear in class or interface definitions.",UnexpectedSubtractionOperand:'Unexpected token, expected "number" or "bigint".',UnexpectedTokenAfterTypeParameter:"Expected an arrow function after this type parameter declaration.",UnexpectedTypeParameterBeforeAsyncArrowFunction:"Type parameters must come after the async keyword, e.g. instead of `<T> async () => {}`, use `async <T>() => {}`.",UnsupportedDeclareExportKind:"`declare export %0` is not supported. Use `%1` instead.",UnsupportedStatementInDeclareModule:"Only declares and type imports are allowed inside declare module.",UnterminatedFlowComment:"Unterminated flow-comment."},x.SyntaxError);function Q(e){return"type"===e.importKind||"typeof"===e.importKind}function Z(e){return(e.type===c.name||!!e.type.keyword)&&"from"!==e.value}const ee={const:"declare export var",let:"declare export var",type:"export type",interface:"export interface"},te=/\*?\s*@((?:no)?flow)\b/,re={quot:'"',amp:"&",apos:"'",lt:"<",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"},ne=/^[\da-fA-F]+$/,ie=/^\d+$/,se=v({AttributeIsEmpty:"JSX attributes must only be assigned a non-empty expression.",MissingClosingTagElement:"Expected corresponding JSX closing tag for <%0>.",MissingClosingTagFragment:"Expected corresponding JSX closing tag for <>.",UnexpectedSequenceExpression:"Sequence expressions cannot be directly nested inside JSX. Did you mean to wrap it in parentheses (...)?",UnsupportedJsxValue:"JSX value should be either an expression or a quoted JSX text.",UnterminatedJsxContent:"Unterminated JSX contents.",UnwrappedAdjacentJSXElements:"Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>?"},x.SyntaxError);function ae(e){return!!e&&("JSXOpeningFragment"===e.type||"JSXClosingFragment"===e.type)}function oe(e){if("JSXIdentifier"===e.type)return e.name;if("JSXNamespacedName"===e.type)return e.namespace.name+":"+e.name.name;if("JSXMemberExpression"===e.type)return oe(e.object)+"."+oe(e.property);throw new Error("Node had unexpected type: "+e.type)}O.j_oTag=new w("<tag",!1),O.j_cTag=new w("</tag",!1),O.j_expr=new w("<tag>...</tag>",!0,!0),c.jsxName=new a("jsxName"),c.jsxText=new a("jsxText",{beforeExpr:!0}),c.jsxTagStart=new a("jsxTagStart",{startsExpr:!0}),c.jsxTagEnd=new a("jsxTagEnd"),c.jsxTagStart.updateContext=function(){this.state.context.push(O.j_expr),this.state.context.push(O.j_oTag),this.state.exprAllowed=!1},c.jsxTagEnd.updateContext=function(e){const t=this.state.context.pop();t===O.j_oTag&&e===c.slash||t===O.j_cTag?(this.state.context.pop(),this.state.exprAllowed=this.curContext()===O.j_expr):this.state.exprAllowed=!0};class le extends X{constructor(...e){super(...e),this.types=[],this.enums=[],this.constEnums=[],this.classes=[],this.exportOnlyBindings=[]}}class pe extends J{createScope(e){return new le(e)}declareName(e,t,r){const n=this.currentScope();if(1024&t)return this.maybeExportDefined(n,e),void n.exportOnlyBindings.push(e);super.declareName(...arguments),2&t&&(1&t||(this.checkRedeclarationInScope(n,e,t,r),this.maybeExportDefined(n,e)),n.types.push(e)),256&t&&n.enums.push(e),512&t&&n.constEnums.push(e),128&t&&n.classes.push(e)}isRedeclaredInScope(e,t,r){return e.enums.indexOf(t)>-1?!(256&r)||!!(512&r)!=e.constEnums.indexOf(t)>-1:128&r&&e.classes.indexOf(t)>-1?e.lexical.indexOf(t)>-1&&!!(1&r):!!(2&r&&e.types.indexOf(t)>-1)||super.isRedeclaredInScope(...arguments)}checkLocalExport(e){-1===this.scopeStack[0].types.indexOf(e.name)&&-1===this.scopeStack[0].exportOnlyBindings.indexOf(e.name)&&super.checkLocalExport(e)}}class ce{constructor(){this.stacks=[]}enter(e){this.stacks.push(e)}exit(){this.stacks.pop()}currentFlags(){return this.stacks[this.stacks.length-1]}get hasAwait(){return(2&this.currentFlags())>0}get hasYield(){return(1&this.currentFlags())>0}get hasReturn(){return(4&this.currentFlags())>0}get hasIn(){return(8&this.currentFlags())>0}}function ue(e,t){return(e?2:0)|(t?1:0)}function de(e){if(null==e)throw new Error(`Unexpected ${e} value.`);return e}function he(e){if(!e)throw new Error("Assert fail")}const fe=v({AbstractMethodHasImplementation:"Method '%0' cannot have an implementation because it is marked abstract.",AccesorCannotDeclareThisParameter:"'get' and 'set' accessors cannot declare 'this' parameters.",AccesorCannotHaveTypeParameters:"An accessor cannot have type parameters.",ClassMethodHasDeclare:"Class methods cannot have the 'declare' modifier.",ClassMethodHasReadonly:"Class methods cannot have the 'readonly' modifier.",ConstructorHasTypeParameters:"Type parameters cannot appear on a constructor declaration.",DeclareAccessor:"'declare' is not allowed in %0ters.",DeclareClassFieldHasInitializer:"Initializers are not allowed in ambient contexts.",DeclareFunctionHasImplementation:"An implementation cannot be declared in ambient contexts.",DuplicateAccessibilityModifier:"Accessibility modifier already seen.",DuplicateModifier:"Duplicate modifier: '%0'.",EmptyHeritageClauseType:"'%0' list cannot be empty.",EmptyTypeArguments:"Type argument list cannot be empty.",EmptyTypeParameters:"Type parameter list cannot be empty.",ExpectedAmbientAfterExportDeclare:"'export declare' must be followed by an ambient declaration.",ImportAliasHasImportType:"An import alias can not use 'import type'.",IncompatibleModifiers:"'%0' modifier cannot be used with '%1' modifier.",IndexSignatureHasAbstract:"Index signatures cannot have the 'abstract' modifier.",IndexSignatureHasAccessibility:"Index signatures cannot have an accessibility modifier ('%0').",IndexSignatureHasDeclare:"Index signatures cannot have the 'declare' modifier.",IndexSignatureHasOverride:"'override' modifier cannot appear on an index signature.",IndexSignatureHasStatic:"Index signatures cannot have the 'static' modifier.",InvalidModifierOnTypeMember:"'%0' modifier cannot appear on a type member.",InvalidModifiersOrder:"'%0' modifier must precede '%1' modifier.",InvalidTupleMemberLabel:"Tuple members must be labeled with a simple identifier.",MixedLabeledAndUnlabeledElements:"Tuple members must all have names or all not have names.",NonAbstractClassHasAbstractMethod:"Abstract methods can only appear within an abstract class.",NonClassMethodPropertyHasAbstractModifer:"'abstract' modifier can only appear on a class, method, or property declaration.",OptionalTypeBeforeRequired:"A required element cannot follow an optional element.",OverrideNotInSubClass:"This member cannot have an 'override' modifier because its containing class does not extend another class.",PatternIsOptional:"A binding pattern parameter cannot be optional in an implementation signature.",PrivateElementHasAbstract:"Private elements cannot have the 'abstract' modifier.",PrivateElementHasAccessibility:"Private elements cannot have an accessibility modifier ('%0').",ReadonlyForMethodSignature:"'readonly' modifier can only appear on a property declaration or index signature.",SetAccesorCannotHaveOptionalParameter:"A 'set' accessor cannot have an optional parameter.",SetAccesorCannotHaveRestParameter:"A 'set' accessor cannot have rest parameter.",SetAccesorCannotHaveReturnType:"A 'set' accessor cannot have a return type annotation.",StaticBlockCannotHaveModifier:"Static class blocks cannot have any modifier.",TypeAnnotationAfterAssign:"Type annotations must come before default assignments, e.g. instead of `age = 25: number` use `age: number = 25`.",TypeImportCannotSpecifyDefaultAndNamed:"A type-only import can specify a default import or named bindings, but not both.",UnexpectedParameterModifier:"A parameter property is only allowed in a constructor implementation.",UnexpectedReadonly:"'readonly' type modifier is only permitted on array and tuple literal types.",UnexpectedTypeAnnotation:"Did not expect a type annotation here.",UnexpectedTypeCastInParameter:"Unexpected type cast in parameter position.",UnsupportedImportTypeArgument:"Argument in a type import must be a string literal.",UnsupportedParameterPropertyKind:"A parameter property may not be declared using a binding pattern.",UnsupportedSignatureParameterKind:"Name in a signature must be an Identifier, ObjectPattern or ArrayPattern, instead got %0."},x.SyntaxError);function me(e){return"private"===e||"public"===e||"protected"===e}c.placeholder=new a("%%",{startsExpr:!0});const ye=v({ClassNameIsRequired:"A class name is required."},x.SyntaxError);function Te(e,t){return e.some((e=>Array.isArray(e)?e[0]===t:e===t))}function be(e,t,r){const n=e.find((e=>Array.isArray(e)?e[0]===t:e===t));return n&&Array.isArray(n)?n[1][r]:null}const ge=["minimal","smart","fsharp"],Se=["hash","bar"],xe={estree:e=>class extends e{estreeParseRegExpLiteral({pattern:e,flags:t}){let r=null;try{r=new RegExp(e,t)}catch(e){}const n=this.estreeParseLiteral(r);return n.regex={pattern:e,flags:t},n}estreeParseBigIntLiteral(e){let t;try{t=BigInt(e)}catch(e){t=null}const r=this.estreeParseLiteral(t);return r.bigint=String(r.value||e),r}estreeParseDecimalLiteral(e){const t=this.estreeParseLiteral(null);return t.decimal=String(t.value||e),t}estreeParseLiteral(e){return this.parseLiteral(e,"Literal")}directiveToStmt(e){const t=e.value,r=this.startNodeAt(e.start,e.loc.start),n=this.startNodeAt(t.start,t.loc.start);return n.value=t.extra.expressionValue,n.raw=t.extra.raw,r.expression=this.finishNodeAt(n,"Literal",t.end,t.loc.end),r.directive=t.extra.raw.slice(1,-1),this.finishNodeAt(r,"ExpressionStatement",e.end,e.loc.end)}initFunction(e,t){super.initFunction(e,t),e.expression=!1}checkDeclaration(e){null!=e&&this.isObjectProperty(e)?this.checkDeclaration(e.value):super.checkDeclaration(e)}getObjectOrClassMethodParams(e){return e.value.params}isValidDirective(e){var t;return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&!(null!=(t=e.expression.extra)&&t.parenthesized)}stmtToDirective(e){const t=super.stmtToDirective(e),r=e.expression.value;return this.addExtra(t.value,"expressionValue",r),t}parseBlockBody(e,...t){super.parseBlockBody(e,...t);const r=e.directives.map((e=>this.directiveToStmt(e)));e.body=r.concat(e.body),delete e.directives}pushClassMethod(e,t,r,n,i,s){this.parseMethod(t,r,n,i,s,"ClassMethod",!0),t.typeParameters&&(t.value.typeParameters=t.typeParameters,delete t.typeParameters),e.body.push(t)}parseExprAtom(e){switch(this.state.type){case c.num:case c.string:return this.estreeParseLiteral(this.state.value);case c.regexp:return this.estreeParseRegExpLiteral(this.state.value);case c.bigint:return this.estreeParseBigIntLiteral(this.state.value);case c.decimal:return this.estreeParseDecimalLiteral(this.state.value);case c._null:return this.estreeParseLiteral(null);case c._true:return this.estreeParseLiteral(!0);case c._false:return this.estreeParseLiteral(!1);default:return super.parseExprAtom(e)}}parseMaybePrivateName(...e){const t=super.parseMaybePrivateName(...e);return"PrivateName"===t.type&&this.getPluginOption("estree","classFeatures")?this.convertPrivateNameToPrivateIdentifier(t):t}convertPrivateNameToPrivateIdentifier(e){const t=super.getPrivateNameSV(e);return delete e.id,e.name=t,e.type="PrivateIdentifier",e}isPrivateName(e){return this.getPluginOption("estree","classFeatures")?"PrivateIdentifier"===e.type:super.isPrivateName(e)}getPrivateNameSV(e){return this.getPluginOption("estree","classFeatures")?e.name:super.getPrivateNameSV(e)}parseLiteral(e,t,r,n){const i=super.parseLiteral(e,t,r,n);return i.raw=i.extra.raw,delete i.extra,i}parseFunctionBody(e,t,r=!1){super.parseFunctionBody(e,t,r),e.expression="BlockStatement"!==e.body.type}parseMethod(e,t,r,n,i,s,a=!1){let o=this.startNode();return o.kind=e.kind,o=super.parseMethod(o,t,r,n,i,s,a),o.type="FunctionExpression",delete o.kind,e.value=o,"ClassPrivateMethod"===s&&(e.computed=!1),s="MethodDefinition",this.finishNode(e,s)}parseClassProperty(...e){const t=super.parseClassProperty(...e);return this.getPluginOption("estree","classFeatures")&&(t.type="PropertyDefinition"),t}parseClassPrivateProperty(...e){const t=super.parseClassPrivateProperty(...e);return this.getPluginOption("estree","classFeatures")&&(t.type="PropertyDefinition",t.computed=!1),t}parseObjectMethod(e,t,r,n,i){const s=super.parseObjectMethod(e,t,r,n,i);return s&&(s.type="Property","method"===s.kind&&(s.kind="init"),s.shorthand=!1),s}parseObjectProperty(e,t,r,n,i){const s=super.parseObjectProperty(e,t,r,n,i);return s&&(s.kind="init",s.type="Property"),s}toAssignable(e,t=!1){return null!=e&&this.isObjectProperty(e)?(this.toAssignable(e.value,t),e):super.toAssignable(e,t)}toAssignableObjectExpressionProp(e,...t){"get"===e.kind||"set"===e.kind?this.raise(e.key.start,E.PatternHasAccessor):e.method?this.raise(e.key.start,E.PatternHasMethod):super.toAssignableObjectExpressionProp(e,...t)}finishCallExpression(e,t){var r;(super.finishCallExpression(e,t),"Import"===e.callee.type)&&(e.type="ImportExpression",e.source=e.arguments[0],this.hasPlugin("importAssertions")&&(e.attributes=null!=(r=e.arguments[1])?r:null),delete e.arguments,delete e.callee);return e}toReferencedArguments(e){"ImportExpression"!==e.type&&super.toReferencedArguments(e)}parseExport(e){switch(super.parseExport(e),e.type){case"ExportAllDeclaration":e.exported=null;break;case"ExportNamedDeclaration":1===e.specifiers.length&&"ExportNamespaceSpecifier"===e.specifiers[0].type&&(e.type="ExportAllDeclaration",e.exported=e.specifiers[0].exported,delete e.specifiers)}return e}parseSubscript(e,t,r,n,i){const s=super.parseSubscript(e,t,r,n,i);if(i.optionalChainMember){if("OptionalMemberExpression"!==s.type&&"OptionalCallExpression"!==s.type||(s.type=s.type.substring(8)),i.stop){const e=this.startNodeAtNode(s);return e.expression=s,this.finishNode(e,"ChainExpression")}}else"MemberExpression"!==s.type&&"CallExpression"!==s.type||(s.optional=!1);return s}hasPropertyAsPrivateName(e){return"ChainExpression"===e.type&&(e=e.expression),super.hasPropertyAsPrivateName(e)}isOptionalChain(e){return"ChainExpression"===e.type}isObjectProperty(e){return"Property"===e.type&&"init"===e.kind&&!e.method}isObjectMethod(e){return e.method||"get"===e.kind||"set"===e.kind}},jsx:e=>class extends e{jsxReadToken(){let e="",t=this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,se.UnterminatedJsxContent);const r=this.input.charCodeAt(this.state.pos);switch(r){case 60:case 123:return this.state.pos===this.state.start?60===r&&this.state.exprAllowed?(++this.state.pos,this.finishToken(c.jsxTagStart)):super.getTokenFromCode(r):(e+=this.input.slice(t,this.state.pos),this.finishToken(c.jsxText,e));case 38:e+=this.input.slice(t,this.state.pos),e+=this.jsxReadEntity(),t=this.state.pos;break;default:h(r)?(e+=this.input.slice(t,this.state.pos),e+=this.jsxReadNewLine(!0),t=this.state.pos):++this.state.pos}}}jsxReadNewLine(e){const t=this.input.charCodeAt(this.state.pos);let r;return++this.state.pos,13===t&&10===this.input.charCodeAt(this.state.pos)?(++this.state.pos,r=e?"\n":"\r\n"):r=String.fromCharCode(t),++this.state.curLine,this.state.lineStart=this.state.pos,r}jsxReadString(e){let t="",r=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,E.UnterminatedString);const n=this.input.charCodeAt(this.state.pos);if(n===e)break;38===n?(t+=this.input.slice(r,this.state.pos),t+=this.jsxReadEntity(),r=this.state.pos):h(n)?(t+=this.input.slice(r,this.state.pos),t+=this.jsxReadNewLine(!1),r=this.state.pos):++this.state.pos}return t+=this.input.slice(r,this.state.pos++),this.finishToken(c.string,t)}jsxReadEntity(){let e,t="",r=0,n=this.input[this.state.pos];const i=++this.state.pos;for(;this.state.pos<this.length&&r++<10;){if(n=this.input[this.state.pos++],";"===n){"#"===t[0]?"x"===t[1]?(t=t.substr(2),ne.test(t)&&(e=String.fromCodePoint(parseInt(t,16)))):(t=t.substr(1),ie.test(t)&&(e=String.fromCodePoint(parseInt(t,10)))):e=re[t];break}t+=n}return e||(this.state.pos=i,"&")}jsxReadWord(){let e;const t=this.state.pos;do{e=this.input.charCodeAt(++this.state.pos)}while(j(e)||45===e);return this.finishToken(c.jsxName,this.input.slice(t,this.state.pos))}jsxParseIdentifier(){const e=this.startNode();return this.match(c.jsxName)?e.name=this.state.value:this.state.type.keyword?e.name=this.state.type.keyword:this.unexpected(),this.next(),this.finishNode(e,"JSXIdentifier")}jsxParseNamespacedName(){const e=this.state.start,t=this.state.startLoc,r=this.jsxParseIdentifier();if(!this.eat(c.colon))return r;const n=this.startNodeAt(e,t);return n.namespace=r,n.name=this.jsxParseIdentifier(),this.finishNode(n,"JSXNamespacedName")}jsxParseElementName(){const e=this.state.start,t=this.state.startLoc;let r=this.jsxParseNamespacedName();if("JSXNamespacedName"===r.type)return r;for(;this.eat(c.dot);){const n=this.startNodeAt(e,t);n.object=r,n.property=this.jsxParseIdentifier(),r=this.finishNode(n,"JSXMemberExpression")}return r}jsxParseAttributeValue(){let e;switch(this.state.type){case c.braceL:return e=this.startNode(),this.next(),e=this.jsxParseExpressionContainer(e),"JSXEmptyExpression"===e.expression.type&&this.raise(e.start,se.AttributeIsEmpty),e;case c.jsxTagStart:case c.string:return this.parseExprAtom();default:throw this.raise(this.state.start,se.UnsupportedJsxValue)}}jsxParseEmptyExpression(){const e=this.startNodeAt(this.state.lastTokEnd,this.state.lastTokEndLoc);return this.finishNodeAt(e,"JSXEmptyExpression",this.state.start,this.state.startLoc)}jsxParseSpreadChild(e){return this.next(),e.expression=this.parseExpression(),this.expect(c.braceR),this.finishNode(e,"JSXSpreadChild")}jsxParseExpressionContainer(e){if(this.match(c.braceR))e.expression=this.jsxParseEmptyExpression();else{const t=this.parseExpression();e.expression=t}return this.expect(c.braceR),this.finishNode(e,"JSXExpressionContainer")}jsxParseAttribute(){const e=this.startNode();return this.eat(c.braceL)?(this.expect(c.ellipsis),e.argument=this.parseMaybeAssignAllowIn(),this.expect(c.braceR),this.finishNode(e,"JSXSpreadAttribute")):(e.name=this.jsxParseNamespacedName(),e.value=this.eat(c.eq)?this.jsxParseAttributeValue():null,this.finishNode(e,"JSXAttribute"))}jsxParseOpeningElementAt(e,t){const r=this.startNodeAt(e,t);return this.match(c.jsxTagEnd)?(this.expect(c.jsxTagEnd),this.finishNode(r,"JSXOpeningFragment")):(r.name=this.jsxParseElementName(),this.jsxParseOpeningElementAfterName(r))}jsxParseOpeningElementAfterName(e){const t=[];for(;!this.match(c.slash)&&!this.match(c.jsxTagEnd);)t.push(this.jsxParseAttribute());return e.attributes=t,e.selfClosing=this.eat(c.slash),this.expect(c.jsxTagEnd),this.finishNode(e,"JSXOpeningElement")}jsxParseClosingElementAt(e,t){const r=this.startNodeAt(e,t);return this.match(c.jsxTagEnd)?(this.expect(c.jsxTagEnd),this.finishNode(r,"JSXClosingFragment")):(r.name=this.jsxParseElementName(),this.expect(c.jsxTagEnd),this.finishNode(r,"JSXClosingElement"))}jsxParseElementAt(e,t){const r=this.startNodeAt(e,t),n=[],i=this.jsxParseOpeningElementAt(e,t);let s=null;if(!i.selfClosing){e:for(;;)switch(this.state.type){case c.jsxTagStart:if(e=this.state.start,t=this.state.startLoc,this.next(),this.eat(c.slash)){s=this.jsxParseClosingElementAt(e,t);break e}n.push(this.jsxParseElementAt(e,t));break;case c.jsxText:n.push(this.parseExprAtom());break;case c.braceL:{const e=this.startNode();this.next(),this.match(c.ellipsis)?n.push(this.jsxParseSpreadChild(e)):n.push(this.jsxParseExpressionContainer(e));break}default:throw this.unexpected()}ae(i)&&!ae(s)?this.raise(s.start,se.MissingClosingTagFragment):!ae(i)&&ae(s)?this.raise(s.start,se.MissingClosingTagElement,oe(i.name)):ae(i)||ae(s)||oe(s.name)!==oe(i.name)&&this.raise(s.start,se.MissingClosingTagElement,oe(i.name))}if(ae(i)?(r.openingFragment=i,r.closingFragment=s):(r.openingElement=i,r.closingElement=s),r.children=n,this.isRelational("<"))throw this.raise(this.state.start,se.UnwrappedAdjacentJSXElements);return ae(i)?this.finishNode(r,"JSXFragment"):this.finishNode(r,"JSXElement")}jsxParseElement(){const e=this.state.start,t=this.state.startLoc;return this.next(),this.jsxParseElementAt(e,t)}parseExprAtom(e){return this.match(c.jsxText)?this.parseLiteral(this.state.value,"JSXText"):this.match(c.jsxTagStart)?this.jsxParseElement():this.isRelational("<")&&33!==this.input.charCodeAt(this.state.pos)?(this.finishToken(c.jsxTagStart),this.jsxParseElement()):super.parseExprAtom(e)}getTokenFromCode(e){if(this.state.inPropertyName)return super.getTokenFromCode(e);const t=this.curContext();if(t===O.j_expr)return this.jsxReadToken();if(t===O.j_oTag||t===O.j_cTag){if(M(e))return this.jsxReadWord();if(62===e)return++this.state.pos,this.finishToken(c.jsxTagEnd);if((34===e||39===e)&&t===O.j_oTag)return this.jsxReadString(e)}return 60===e&&this.state.exprAllowed&&33!==this.input.charCodeAt(this.state.pos+1)?(++this.state.pos,this.finishToken(c.jsxTagStart)):super.getTokenFromCode(e)}updateContext(e){if(this.match(c.braceL)){const t=this.curContext();t===O.j_oTag?this.state.context.push(O.braceExpression):t===O.j_expr?this.state.context.push(O.templateQuasi):super.updateContext(e),this.state.exprAllowed=!0}else{if(!this.match(c.slash)||e!==c.jsxTagStart)return super.updateContext(e);this.state.context.length-=2,this.state.context.push(O.j_cTag),this.state.exprAllowed=!1}}},flow:e=>class extends e{constructor(...e){super(...e),this.flowPragma=void 0}getScopeHandler(){return G}shouldParseTypes(){return this.getPluginOption("flow","all")||"flow"===this.flowPragma}shouldParseEnums(){return!!this.getPluginOption("flow","enums")}finishToken(e,t){return e!==c.string&&e!==c.semi&&e!==c.interpreterDirective&&void 0===this.flowPragma&&(this.flowPragma=null),super.finishToken(e,t)}addComment(e){if(void 0===this.flowPragma){const t=te.exec(e.value);if(t)if("flow"===t[1])this.flowPragma="flow";else{if("noflow"!==t[1])throw new Error("Unexpected flow pragma");this.flowPragma="noflow"}}return super.addComment(e)}flowParseTypeInitialiser(e){const t=this.state.inType;this.state.inType=!0,this.expect(e||c.colon);const r=this.flowParseType();return this.state.inType=t,r}flowParsePredicate(){const e=this.startNode(),t=this.state.start;return this.next(),this.expectContextual("checks"),this.state.lastTokStart>t+1&&this.raise(t,$.UnexpectedSpaceBetweenModuloChecks),this.eat(c.parenL)?(e.value=this.parseExpression(),this.expect(c.parenR),this.finishNode(e,"DeclaredPredicate")):this.finishNode(e,"InferredPredicate")}flowParseTypeAndPredicateInitialiser(){const e=this.state.inType;this.state.inType=!0,this.expect(c.colon);let t=null,r=null;return this.match(c.modulo)?(this.state.inType=e,r=this.flowParsePredicate()):(t=this.flowParseType(),this.state.inType=e,this.match(c.modulo)&&(r=this.flowParsePredicate())),[t,r]}flowParseDeclareClass(e){return this.next(),this.flowParseInterfaceish(e,!0),this.finishNode(e,"DeclareClass")}flowParseDeclareFunction(e){this.next();const t=e.id=this.parseIdentifier(),r=this.startNode(),n=this.startNode();this.isRelational("<")?r.typeParameters=this.flowParseTypeParameterDeclaration():r.typeParameters=null,this.expect(c.parenL);const i=this.flowParseFunctionTypeParams();return r.params=i.params,r.rest=i.rest,r.this=i._this,this.expect(c.parenR),[r.returnType,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),n.typeAnnotation=this.finishNode(r,"FunctionTypeAnnotation"),t.typeAnnotation=this.finishNode(n,"TypeAnnotation"),this.resetEndLocation(t),this.semicolon(),this.scope.declareName(e.id.name,2048,e.id.start),this.finishNode(e,"DeclareFunction")}flowParseDeclare(e,t){if(this.match(c._class))return this.flowParseDeclareClass(e);if(this.match(c._function))return this.flowParseDeclareFunction(e);if(this.match(c._var))return this.flowParseDeclareVariable(e);if(this.eatContextual("module"))return this.match(c.dot)?this.flowParseDeclareModuleExports(e):(t&&this.raise(this.state.lastTokStart,$.NestedDeclareModule),this.flowParseDeclareModule(e));if(this.isContextual("type"))return this.flowParseDeclareTypeAlias(e);if(this.isContextual("opaque"))return this.flowParseDeclareOpaqueType(e);if(this.isContextual("interface"))return this.flowParseDeclareInterface(e);if(this.match(c._export))return this.flowParseDeclareExportDeclaration(e,t);throw this.unexpected()}flowParseDeclareVariable(e){return this.next(),e.id=this.flowParseTypeAnnotatableIdentifier(!0),this.scope.declareName(e.id.name,5,e.id.start),this.semicolon(),this.finishNode(e,"DeclareVariable")}flowParseDeclareModule(e){this.scope.enter(0),this.match(c.string)?e.id=this.parseExprAtom():e.id=this.parseIdentifier();const t=e.body=this.startNode(),r=t.body=[];for(this.expect(c.braceL);!this.match(c.braceR);){let e=this.startNode();this.match(c._import)?(this.next(),this.isContextual("type")||this.match(c._typeof)||this.raise(this.state.lastTokStart,$.InvalidNonTypeImportInDeclareModule),this.parseImport(e)):(this.expectContextual("declare",$.UnsupportedStatementInDeclareModule),e=this.flowParseDeclare(e,!0)),r.push(e)}this.scope.exit(),this.expect(c.braceR),this.finishNode(t,"BlockStatement");let n=null,i=!1;return r.forEach((e=>{!function(e){return"DeclareExportAllDeclaration"===e.type||"DeclareExportDeclaration"===e.type&&(!e.declaration||"TypeAlias"!==e.declaration.type&&"InterfaceDeclaration"!==e.declaration.type)}(e)?"DeclareModuleExports"===e.type&&(i&&this.raise(e.start,$.DuplicateDeclareModuleExports),"ES"===n&&this.raise(e.start,$.AmbiguousDeclareModuleKind),n="CommonJS",i=!0):("CommonJS"===n&&this.raise(e.start,$.AmbiguousDeclareModuleKind),n="ES")})),e.kind=n||"CommonJS",this.finishNode(e,"DeclareModule")}flowParseDeclareExportDeclaration(e,t){if(this.expect(c._export),this.eat(c._default))return this.match(c._function)||this.match(c._class)?e.declaration=this.flowParseDeclare(this.startNode()):(e.declaration=this.flowParseType(),this.semicolon()),e.default=!0,this.finishNode(e,"DeclareExportDeclaration");if(this.match(c._const)||this.isLet()||(this.isContextual("type")||this.isContextual("interface"))&&!t){const e=this.state.value,t=ee[e];throw this.raise(this.state.start,$.UnsupportedDeclareExportKind,e,t)}if(this.match(c._var)||this.match(c._function)||this.match(c._class)||this.isContextual("opaque"))return e.declaration=this.flowParseDeclare(this.startNode()),e.default=!1,this.finishNode(e,"DeclareExportDeclaration");if(this.match(c.star)||this.match(c.braceL)||this.isContextual("interface")||this.isContextual("type")||this.isContextual("opaque"))return"ExportNamedDeclaration"===(e=this.parseExport(e)).type&&(e.type="ExportDeclaration",e.default=!1,delete e.exportKind),e.type="Declare"+e.type,e;throw this.unexpected()}flowParseDeclareModuleExports(e){return this.next(),this.expectContextual("exports"),e.typeAnnotation=this.flowParseTypeAnnotation(),this.semicolon(),this.finishNode(e,"DeclareModuleExports")}flowParseDeclareTypeAlias(e){return this.next(),this.flowParseTypeAlias(e),e.type="DeclareTypeAlias",e}flowParseDeclareOpaqueType(e){return this.next(),this.flowParseOpaqueType(e,!0),e.type="DeclareOpaqueType",e}flowParseDeclareInterface(e){return this.next(),this.flowParseInterfaceish(e),this.finishNode(e,"DeclareInterface")}flowParseInterfaceish(e,t=!1){if(e.id=this.flowParseRestrictedIdentifier(!t,!0),this.scope.declareName(e.id.name,t?17:9,e.id.start),this.isRelational("<")?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.extends=[],e.implements=[],e.mixins=[],this.eat(c._extends))do{e.extends.push(this.flowParseInterfaceExtends())}while(!t&&this.eat(c.comma));if(this.isContextual("mixins")){this.next();do{e.mixins.push(this.flowParseInterfaceExtends())}while(this.eat(c.comma))}if(this.isContextual("implements")){this.next();do{e.implements.push(this.flowParseInterfaceExtends())}while(this.eat(c.comma))}e.body=this.flowParseObjectType({allowStatic:t,allowExact:!1,allowSpread:!1,allowProto:t,allowInexact:!1})}flowParseInterfaceExtends(){const e=this.startNode();return e.id=this.flowParseQualifiedTypeIdentifier(),this.isRelational("<")?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,this.finishNode(e,"InterfaceExtends")}flowParseInterface(e){return this.flowParseInterfaceish(e),this.finishNode(e,"InterfaceDeclaration")}checkNotUnderscore(e){"_"===e&&this.raise(this.state.start,$.UnexpectedReservedUnderscore)}checkReservedType(e,t,r){z.has(e)&&this.raise(t,r?$.AssignReservedType:$.UnexpectedReservedType,e)}flowParseRestrictedIdentifier(e,t){return this.checkReservedType(this.state.value,this.state.start,t),this.parseIdentifier(e)}flowParseTypeAlias(e){return e.id=this.flowParseRestrictedIdentifier(!1,!0),this.scope.declareName(e.id.name,9,e.id.start),this.isRelational("<")?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.right=this.flowParseTypeInitialiser(c.eq),this.semicolon(),this.finishNode(e,"TypeAlias")}flowParseOpaqueType(e,t){return this.expectContextual("type"),e.id=this.flowParseRestrictedIdentifier(!0,!0),this.scope.declareName(e.id.name,9,e.id.start),this.isRelational("<")?e.typeParameters=this.flowParseTypeParameterDeclaration():e.typeParameters=null,e.supertype=null,this.match(c.colon)&&(e.supertype=this.flowParseTypeInitialiser(c.colon)),e.impltype=null,t||(e.impltype=this.flowParseTypeInitialiser(c.eq)),this.semicolon(),this.finishNode(e,"OpaqueType")}flowParseTypeParameter(e=!1){const t=this.state.start,r=this.startNode(),n=this.flowParseVariance(),i=this.flowParseTypeAnnotatableIdentifier();return r.name=i.name,r.variance=n,r.bound=i.typeAnnotation,this.match(c.eq)?(this.eat(c.eq),r.default=this.flowParseType()):e&&this.raise(t,$.MissingTypeParamDefault),this.finishNode(r,"TypeParameter")}flowParseTypeParameterDeclaration(){const e=this.state.inType,t=this.startNode();t.params=[],this.state.inType=!0,this.isRelational("<")||this.match(c.jsxTagStart)?this.next():this.unexpected();let r=!1;do{const e=this.flowParseTypeParameter(r);t.params.push(e),e.default&&(r=!0),this.isRelational(">")||this.expect(c.comma)}while(!this.isRelational(">"));return this.expectRelational(">"),this.state.inType=e,this.finishNode(t,"TypeParameterDeclaration")}flowParseTypeParameterInstantiation(){const e=this.startNode(),t=this.state.inType;e.params=[],this.state.inType=!0,this.expectRelational("<");const r=this.state.noAnonFunctionType;for(this.state.noAnonFunctionType=!1;!this.isRelational(">");)e.params.push(this.flowParseType()),this.isRelational(">")||this.expect(c.comma);return this.state.noAnonFunctionType=r,this.expectRelational(">"),this.state.inType=t,this.finishNode(e,"TypeParameterInstantiation")}flowParseTypeParameterInstantiationCallOrNew(){const e=this.startNode(),t=this.state.inType;for(e.params=[],this.state.inType=!0,this.expectRelational("<");!this.isRelational(">");)e.params.push(this.flowParseTypeOrImplicitInstantiation()),this.isRelational(">")||this.expect(c.comma);return this.expectRelational(">"),this.state.inType=t,this.finishNode(e,"TypeParameterInstantiation")}flowParseInterfaceType(){const e=this.startNode();if(this.expectContextual("interface"),e.extends=[],this.eat(c._extends))do{e.extends.push(this.flowParseInterfaceExtends())}while(this.eat(c.comma));return e.body=this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!1,allowProto:!1,allowInexact:!1}),this.finishNode(e,"InterfaceTypeAnnotation")}flowParseObjectPropertyKey(){return this.match(c.num)||this.match(c.string)?this.parseExprAtom():this.parseIdentifier(!0)}flowParseObjectTypeIndexer(e,t,r){return e.static=t,this.lookahead().type===c.colon?(e.id=this.flowParseObjectPropertyKey(),e.key=this.flowParseTypeInitialiser()):(e.id=null,e.key=this.flowParseType()),this.expect(c.bracketR),e.value=this.flowParseTypeInitialiser(),e.variance=r,this.finishNode(e,"ObjectTypeIndexer")}flowParseObjectTypeInternalSlot(e,t){return e.static=t,e.id=this.flowParseObjectPropertyKey(),this.expect(c.bracketR),this.expect(c.bracketR),this.isRelational("<")||this.match(c.parenL)?(e.method=!0,e.optional=!1,e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.start,e.loc.start))):(e.method=!1,this.eat(c.question)&&(e.optional=!0),e.value=this.flowParseTypeInitialiser()),this.finishNode(e,"ObjectTypeInternalSlot")}flowParseObjectTypeMethodish(e){for(e.params=[],e.rest=null,e.typeParameters=null,e.this=null,this.isRelational("<")&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),this.expect(c.parenL),this.match(c._this)&&(e.this=this.flowParseFunctionTypeParam(!0),e.this.name=null,this.match(c.parenR)||this.expect(c.comma));!this.match(c.parenR)&&!this.match(c.ellipsis);)e.params.push(this.flowParseFunctionTypeParam(!1)),this.match(c.parenR)||this.expect(c.comma);return this.eat(c.ellipsis)&&(e.rest=this.flowParseFunctionTypeParam(!1)),this.expect(c.parenR),e.returnType=this.flowParseTypeInitialiser(),this.finishNode(e,"FunctionTypeAnnotation")}flowParseObjectTypeCallProperty(e,t){const r=this.startNode();return e.static=t,e.value=this.flowParseObjectTypeMethodish(r),this.finishNode(e,"ObjectTypeCallProperty")}flowParseObjectType({allowStatic:e,allowExact:t,allowSpread:r,allowProto:n,allowInexact:i}){const s=this.state.inType;this.state.inType=!0;const a=this.startNode();let o,l;a.callProperties=[],a.properties=[],a.indexers=[],a.internalSlots=[];let p=!1;for(t&&this.match(c.braceBarL)?(this.expect(c.braceBarL),o=c.braceBarR,l=!0):(this.expect(c.braceL),o=c.braceR,l=!1),a.exact=l;!this.match(o);){let t=!1,s=null,o=null;const u=this.startNode();if(n&&this.isContextual("proto")){const t=this.lookahead();t.type!==c.colon&&t.type!==c.question&&(this.next(),s=this.state.start,e=!1)}if(e&&this.isContextual("static")){const e=this.lookahead();e.type!==c.colon&&e.type!==c.question&&(this.next(),t=!0)}const d=this.flowParseVariance();if(this.eat(c.bracketL))null!=s&&this.unexpected(s),this.eat(c.bracketL)?(d&&this.unexpected(d.start),a.internalSlots.push(this.flowParseObjectTypeInternalSlot(u,t))):a.indexers.push(this.flowParseObjectTypeIndexer(u,t,d));else if(this.match(c.parenL)||this.isRelational("<"))null!=s&&this.unexpected(s),d&&this.unexpected(d.start),a.callProperties.push(this.flowParseObjectTypeCallProperty(u,t));else{let e="init";if(this.isContextual("get")||this.isContextual("set")){const t=this.lookahead();t.type!==c.name&&t.type!==c.string&&t.type!==c.num||(e=this.state.value,this.next())}const n=this.flowParseObjectTypeProperty(u,t,s,d,e,r,null!=i?i:!l);null===n?(p=!0,o=this.state.lastTokStart):a.properties.push(n)}this.flowObjectTypeSemicolon(),!o||this.match(c.braceR)||this.match(c.braceBarR)||this.raise(o,$.UnexpectedExplicitInexactInObject)}this.expect(o),r&&(a.inexact=p);const u=this.finishNode(a,"ObjectTypeAnnotation");return this.state.inType=s,u}flowParseObjectTypeProperty(e,t,r,n,i,s,a){if(this.eat(c.ellipsis))return this.match(c.comma)||this.match(c.semi)||this.match(c.braceR)||this.match(c.braceBarR)?(s?a||this.raise(this.state.lastTokStart,$.InexactInsideExact):this.raise(this.state.lastTokStart,$.InexactInsideNonObject),n&&this.raise(n.start,$.InexactVariance),null):(s||this.raise(this.state.lastTokStart,$.UnexpectedSpreadType),null!=r&&this.unexpected(r),n&&this.raise(n.start,$.SpreadVariance),e.argument=this.flowParseType(),this.finishNode(e,"ObjectTypeSpreadProperty"));{e.key=this.flowParseObjectPropertyKey(),e.static=t,e.proto=null!=r,e.kind=i;let a=!1;return this.isRelational("<")||this.match(c.parenL)?(e.method=!0,null!=r&&this.unexpected(r),n&&this.unexpected(n.start),e.value=this.flowParseObjectTypeMethodish(this.startNodeAt(e.start,e.loc.start)),"get"!==i&&"set"!==i||this.flowCheckGetterSetterParams(e),!s&&"constructor"===e.key.name&&e.value.this&&this.raise(e.value.this.start,$.ThisParamBannedInConstructor)):("init"!==i&&this.unexpected(),e.method=!1,this.eat(c.question)&&(a=!0),e.value=this.flowParseTypeInitialiser(),e.variance=n),e.optional=a,this.finishNode(e,"ObjectTypeProperty")}}flowCheckGetterSetterParams(e){const t="get"===e.kind?0:1,r=e.start,n=e.value.params.length+(e.value.rest?1:0);e.value.this&&this.raise(e.value.this.start,"get"===e.kind?$.GetterMayNotHaveThisParam:$.SetterMayNotHaveThisParam),n!==t&&("get"===e.kind?this.raise(r,E.BadGetterArity):this.raise(r,E.BadSetterArity)),"set"===e.kind&&e.value.rest&&this.raise(r,E.BadSetterRestParameter)}flowObjectTypeSemicolon(){this.eat(c.semi)||this.eat(c.comma)||this.match(c.braceR)||this.match(c.braceBarR)||this.unexpected()}flowParseQualifiedTypeIdentifier(e,t,r){e=e||this.state.start,t=t||this.state.startLoc;let n=r||this.flowParseRestrictedIdentifier(!0);for(;this.eat(c.dot);){const r=this.startNodeAt(e,t);r.qualification=n,r.id=this.flowParseRestrictedIdentifier(!0),n=this.finishNode(r,"QualifiedTypeIdentifier")}return n}flowParseGenericType(e,t,r){const n=this.startNodeAt(e,t);return n.typeParameters=null,n.id=this.flowParseQualifiedTypeIdentifier(e,t,r),this.isRelational("<")&&(n.typeParameters=this.flowParseTypeParameterInstantiation()),this.finishNode(n,"GenericTypeAnnotation")}flowParseTypeofType(){const e=this.startNode();return this.expect(c._typeof),e.argument=this.flowParsePrimaryType(),this.finishNode(e,"TypeofTypeAnnotation")}flowParseTupleType(){const e=this.startNode();for(e.types=[],this.expect(c.bracketL);this.state.pos<this.length&&!this.match(c.bracketR)&&(e.types.push(this.flowParseType()),!this.match(c.bracketR));)this.expect(c.comma);return this.expect(c.bracketR),this.finishNode(e,"TupleTypeAnnotation")}flowParseFunctionTypeParam(e){let t=null,r=!1,n=null;const i=this.startNode(),s=this.lookahead(),a=this.state.type===c._this;return s.type===c.colon||s.type===c.question?(a&&!e&&this.raise(i.start,$.ThisParamMustBeFirst),t=this.parseIdentifier(a),this.eat(c.question)&&(r=!0,a&&this.raise(i.start,$.ThisParamMayNotBeOptional)),n=this.flowParseTypeInitialiser()):n=this.flowParseType(),i.name=t,i.optional=r,i.typeAnnotation=n,this.finishNode(i,"FunctionTypeParam")}reinterpretTypeAsFunctionTypeParam(e){const t=this.startNodeAt(e.start,e.loc.start);return t.name=null,t.optional=!1,t.typeAnnotation=e,this.finishNode(t,"FunctionTypeParam")}flowParseFunctionTypeParams(e=[]){let t=null,r=null;for(this.match(c._this)&&(r=this.flowParseFunctionTypeParam(!0),r.name=null,this.match(c.parenR)||this.expect(c.comma));!this.match(c.parenR)&&!this.match(c.ellipsis);)e.push(this.flowParseFunctionTypeParam(!1)),this.match(c.parenR)||this.expect(c.comma);return this.eat(c.ellipsis)&&(t=this.flowParseFunctionTypeParam(!1)),{params:e,rest:t,_this:r}}flowIdentToTypeAnnotation(e,t,r,n){switch(n.name){case"any":return this.finishNode(r,"AnyTypeAnnotation");case"bool":case"boolean":return this.finishNode(r,"BooleanTypeAnnotation");case"mixed":return this.finishNode(r,"MixedTypeAnnotation");case"empty":return this.finishNode(r,"EmptyTypeAnnotation");case"number":return this.finishNode(r,"NumberTypeAnnotation");case"string":return this.finishNode(r,"StringTypeAnnotation");case"symbol":return this.finishNode(r,"SymbolTypeAnnotation");default:return this.checkNotUnderscore(n.name),this.flowParseGenericType(e,t,n)}}flowParsePrimaryType(){const e=this.state.start,t=this.state.startLoc,r=this.startNode();let n,i,s=!1;const a=this.state.noAnonFunctionType;switch(this.state.type){case c.name:return this.isContextual("interface")?this.flowParseInterfaceType():this.flowIdentToTypeAnnotation(e,t,r,this.parseIdentifier());case c.braceL:return this.flowParseObjectType({allowStatic:!1,allowExact:!1,allowSpread:!0,allowProto:!1,allowInexact:!0});case c.braceBarL:return this.flowParseObjectType({allowStatic:!1,allowExact:!0,allowSpread:!0,allowProto:!1,allowInexact:!1});case c.bracketL:return this.state.noAnonFunctionType=!1,i=this.flowParseTupleType(),this.state.noAnonFunctionType=a,i;case c.relational:if("<"===this.state.value)return r.typeParameters=this.flowParseTypeParameterDeclaration(),this.expect(c.parenL),n=this.flowParseFunctionTypeParams(),r.params=n.params,r.rest=n.rest,r.this=n._this,this.expect(c.parenR),this.expect(c.arrow),r.returnType=this.flowParseType(),this.finishNode(r,"FunctionTypeAnnotation");break;case c.parenL:if(this.next(),!this.match(c.parenR)&&!this.match(c.ellipsis))if(this.match(c.name)||this.match(c._this)){const e=this.lookahead().type;s=e!==c.question&&e!==c.colon}else s=!0;if(s){if(this.state.noAnonFunctionType=!1,i=this.flowParseType(),this.state.noAnonFunctionType=a,this.state.noAnonFunctionType||!(this.match(c.comma)||this.match(c.parenR)&&this.lookahead().type===c.arrow))return this.expect(c.parenR),i;this.eat(c.comma)}return n=i?this.flowParseFunctionTypeParams([this.reinterpretTypeAsFunctionTypeParam(i)]):this.flowParseFunctionTypeParams(),r.params=n.params,r.rest=n.rest,r.this=n._this,this.expect(c.parenR),this.expect(c.arrow),r.returnType=this.flowParseType(),r.typeParameters=null,this.finishNode(r,"FunctionTypeAnnotation");case c.string:return this.parseLiteral(this.state.value,"StringLiteralTypeAnnotation");case c._true:case c._false:return r.value=this.match(c._true),this.next(),this.finishNode(r,"BooleanLiteralTypeAnnotation");case c.plusMin:if("-"===this.state.value){if(this.next(),this.match(c.num))return this.parseLiteral(-this.state.value,"NumberLiteralTypeAnnotation",r.start,r.loc.start);if(this.match(c.bigint))return this.parseLiteral(-this.state.value,"BigIntLiteralTypeAnnotation",r.start,r.loc.start);throw this.raise(this.state.start,$.UnexpectedSubtractionOperand)}throw this.unexpected();case c.num:return this.parseLiteral(this.state.value,"NumberLiteralTypeAnnotation");case c.bigint:return this.parseLiteral(this.state.value,"BigIntLiteralTypeAnnotation");case c._void:return this.next(),this.finishNode(r,"VoidTypeAnnotation");case c._null:return this.next(),this.finishNode(r,"NullLiteralTypeAnnotation");case c._this:return this.next(),this.finishNode(r,"ThisTypeAnnotation");case c.star:return this.next(),this.finishNode(r,"ExistsTypeAnnotation");default:if("typeof"===this.state.type.keyword)return this.flowParseTypeofType();if(this.state.type.keyword){const e=this.state.type.label;return this.next(),super.createIdentifier(r,e)}}throw this.unexpected()}flowParsePostfixType(){const e=this.state.start,t=this.state.startLoc;let r=this.flowParsePrimaryType(),n=!1;for(;(this.match(c.bracketL)||this.match(c.questionDot))&&!this.canInsertSemicolon();){const i=this.startNodeAt(e,t),s=this.eat(c.questionDot);n=n||s,this.expect(c.bracketL),!s&&this.match(c.bracketR)?(i.elementType=r,this.next(),r=this.finishNode(i,"ArrayTypeAnnotation")):(i.objectType=r,i.indexType=this.flowParseType(),this.expect(c.bracketR),n?(i.optional=s,r=this.finishNode(i,"OptionalIndexedAccessType")):r=this.finishNode(i,"IndexedAccessType"))}return r}flowParsePrefixType(){const e=this.startNode();return this.eat(c.question)?(e.typeAnnotation=this.flowParsePrefixType(),this.finishNode(e,"NullableTypeAnnotation")):this.flowParsePostfixType()}flowParseAnonFunctionWithoutParens(){const e=this.flowParsePrefixType();if(!this.state.noAnonFunctionType&&this.eat(c.arrow)){const t=this.startNodeAt(e.start,e.loc.start);return t.params=[this.reinterpretTypeAsFunctionTypeParam(e)],t.rest=null,t.this=null,t.returnType=this.flowParseType(),t.typeParameters=null,this.finishNode(t,"FunctionTypeAnnotation")}return e}flowParseIntersectionType(){const e=this.startNode();this.eat(c.bitwiseAND);const t=this.flowParseAnonFunctionWithoutParens();for(e.types=[t];this.eat(c.bitwiseAND);)e.types.push(this.flowParseAnonFunctionWithoutParens());return 1===e.types.length?t:this.finishNode(e,"IntersectionTypeAnnotation")}flowParseUnionType(){const e=this.startNode();this.eat(c.bitwiseOR);const t=this.flowParseIntersectionType();for(e.types=[t];this.eat(c.bitwiseOR);)e.types.push(this.flowParseIntersectionType());return 1===e.types.length?t:this.finishNode(e,"UnionTypeAnnotation")}flowParseType(){const e=this.state.inType;this.state.inType=!0;const t=this.flowParseUnionType();return this.state.inType=e,this.state.exprAllowed=this.state.exprAllowed||this.state.noAnonFunctionType,t}flowParseTypeOrImplicitInstantiation(){if(this.state.type===c.name&&"_"===this.state.value){const e=this.state.start,t=this.state.startLoc,r=this.parseIdentifier();return this.flowParseGenericType(e,t,r)}return this.flowParseType()}flowParseTypeAnnotation(){const e=this.startNode();return e.typeAnnotation=this.flowParseTypeInitialiser(),this.finishNode(e,"TypeAnnotation")}flowParseTypeAnnotatableIdentifier(e){const t=e?this.parseIdentifier():this.flowParseRestrictedIdentifier();return this.match(c.colon)&&(t.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(t)),t}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.end,e.typeAnnotation.loc.end),e.expression}flowParseVariance(){let e=null;return this.match(c.plusMin)&&(e=this.startNode(),"+"===this.state.value?e.kind="plus":e.kind="minus",this.next(),this.finishNode(e,"Variance")),e}parseFunctionBody(e,t,r=!1){return t?this.forwardNoArrowParamsConversionAt(e,(()=>super.parseFunctionBody(e,!0,r))):super.parseFunctionBody(e,!1,r)}parseFunctionBodyAndFinish(e,t,r=!1){if(this.match(c.colon)){const t=this.startNode();[t.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),e.returnType=t.typeAnnotation?this.finishNode(t,"TypeAnnotation"):null}super.parseFunctionBodyAndFinish(e,t,r)}parseStatement(e,t){if(this.state.strict&&this.match(c.name)&&"interface"===this.state.value){const e=this.lookahead();if(e.type===c.name||Y(e.value)){const e=this.startNode();return this.next(),this.flowParseInterface(e)}}else if(this.shouldParseEnums()&&this.isContextual("enum")){const e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}const r=super.parseStatement(e,t);return void 0!==this.flowPragma||this.isValidDirective(r)||(this.flowPragma=null),r}parseExpressionStatement(e,t){if("Identifier"===t.type)if("declare"===t.name){if(this.match(c._class)||this.match(c.name)||this.match(c._function)||this.match(c._var)||this.match(c._export))return this.flowParseDeclare(e)}else if(this.match(c.name)){if("interface"===t.name)return this.flowParseInterface(e);if("type"===t.name)return this.flowParseTypeAlias(e);if("opaque"===t.name)return this.flowParseOpaqueType(e,!1)}return super.parseExpressionStatement(e,t)}shouldParseExportDeclaration(){return this.isContextual("type")||this.isContextual("interface")||this.isContextual("opaque")||this.shouldParseEnums()&&this.isContextual("enum")||super.shouldParseExportDeclaration()}isExportDefaultSpecifier(){return(!this.match(c.name)||!("type"===this.state.value||"interface"===this.state.value||"opaque"===this.state.value||this.shouldParseEnums()&&"enum"===this.state.value))&&super.isExportDefaultSpecifier()}parseExportDefaultExpression(){if(this.shouldParseEnums()&&this.isContextual("enum")){const e=this.startNode();return this.next(),this.flowParseEnumDeclaration(e)}return super.parseExportDefaultExpression()}parseConditional(e,t,r,n){if(!this.match(c.question))return e;if(n){const i=this.tryParse((()=>super.parseConditional(e,t,r)));return i.node?(i.error&&(this.state=i.failState),i.node):(n.start=i.error.pos||this.state.start,e)}this.expect(c.question);const i=this.state.clone(),s=this.state.noArrowAt,a=this.startNodeAt(t,r);let{consequent:o,failed:l}=this.tryParseConditionalConsequent(),[p,u]=this.getArrowLikeExpressions(o);if(l||u.length>0){const e=[...s];if(u.length>0){this.state=i,this.state.noArrowAt=e;for(let t=0;t<u.length;t++)e.push(u[t].start);({consequent:o,failed:l}=this.tryParseConditionalConsequent()),[p,u]=this.getArrowLikeExpressions(o)}l&&p.length>1&&this.raise(i.start,$.AmbiguousConditionalArrow),l&&1===p.length&&(this.state=i,this.state.noArrowAt=e.concat(p[0].start),({consequent:o,failed:l}=this.tryParseConditionalConsequent()))}return this.getArrowLikeExpressions(o,!0),this.state.noArrowAt=s,this.expect(c.colon),a.test=e,a.consequent=o,a.alternate=this.forwardNoArrowParamsConversionAt(a,(()=>this.parseMaybeAssign(void 0,void 0,void 0))),this.finishNode(a,"ConditionalExpression")}tryParseConditionalConsequent(){this.state.noArrowParamsConversionAt.push(this.state.start);const e=this.parseMaybeAssignAllowIn(),t=!this.match(c.colon);return this.state.noArrowParamsConversionAt.pop(),{consequent:e,failed:t}}getArrowLikeExpressions(e,t){const r=[e],n=[];for(;0!==r.length;){const e=r.pop();"ArrowFunctionExpression"===e.type?(e.typeParameters||!e.returnType?this.finishArrowValidation(e):n.push(e),r.push(e.body)):"ConditionalExpression"===e.type&&(r.push(e.consequent),r.push(e.alternate))}return t?(n.forEach((e=>this.finishArrowValidation(e))),[n,[]]):function(e,t){const r=[],n=[];for(let i=0;i<e.length;i++)(t(e[i])?r:n).push(e[i]);return[r,n]}(n,(e=>e.params.every((e=>this.isAssignable(e,!0)))))}finishArrowValidation(e){var t;this.toAssignableList(e.params,null==(t=e.extra)?void 0:t.trailingComma,!1),this.scope.enter(6),super.checkParams(e,!1,!0),this.scope.exit()}forwardNoArrowParamsConversionAt(e,t){let r;return-1!==this.state.noArrowParamsConversionAt.indexOf(e.start)?(this.state.noArrowParamsConversionAt.push(this.state.start),r=t(),this.state.noArrowParamsConversionAt.pop()):r=t(),r}parseParenItem(e,t,r){if(e=super.parseParenItem(e,t,r),this.eat(c.question)&&(e.optional=!0,this.resetEndLocation(e)),this.match(c.colon)){const n=this.startNodeAt(t,r);return n.expression=e,n.typeAnnotation=this.flowParseTypeAnnotation(),this.finishNode(n,"TypeCastExpression")}return e}assertModuleNodeAllowed(e){"ImportDeclaration"===e.type&&("type"===e.importKind||"typeof"===e.importKind)||"ExportNamedDeclaration"===e.type&&"type"===e.exportKind||"ExportAllDeclaration"===e.type&&"type"===e.exportKind||super.assertModuleNodeAllowed(e)}parseExport(e){const t=super.parseExport(e);return"ExportNamedDeclaration"!==t.type&&"ExportAllDeclaration"!==t.type||(t.exportKind=t.exportKind||"value"),t}parseExportDeclaration(e){if(this.isContextual("type")){e.exportKind="type";const t=this.startNode();return this.next(),this.match(c.braceL)?(e.specifiers=this.parseExportSpecifiers(),this.parseExportFrom(e),null):this.flowParseTypeAlias(t)}if(this.isContextual("opaque")){e.exportKind="type";const t=this.startNode();return this.next(),this.flowParseOpaqueType(t,!1)}if(this.isContextual("interface")){e.exportKind="type";const t=this.startNode();return this.next(),this.flowParseInterface(t)}if(this.shouldParseEnums()&&this.isContextual("enum")){e.exportKind="value";const t=this.startNode();return this.next(),this.flowParseEnumDeclaration(t)}return super.parseExportDeclaration(e)}eatExportStar(e){return!!super.eatExportStar(...arguments)||!(!this.isContextual("type")||this.lookahead().type!==c.star)&&(e.exportKind="type",this.next(),this.next(),!0)}maybeParseExportNamespaceSpecifier(e){const t=this.state.start,r=super.maybeParseExportNamespaceSpecifier(e);return r&&"type"===e.exportKind&&this.unexpected(t),r}parseClassId(e,t,r){super.parseClassId(e,t,r),this.isRelational("<")&&(e.typeParameters=this.flowParseTypeParameterDeclaration())}parseClassMember(e,t,r){const n=this.state.start;if(this.isContextual("declare")){if(this.parseClassMemberFromModifier(e,t))return;t.declare=!0}super.parseClassMember(e,t,r),t.declare&&("ClassProperty"!==t.type&&"ClassPrivateProperty"!==t.type&&"PropertyDefinition"!==t.type?this.raise(n,$.DeclareClassElement):t.value&&this.raise(t.value.start,$.DeclareClassFieldInitializer))}isIterator(e){return"iterator"===e||"asyncIterator"===e}readIterator(){const e=super.readWord1(),t="@@"+e;this.isIterator(e)&&this.state.inType||this.raise(this.state.pos,E.InvalidIdentifier,t),this.finishToken(c.name,t)}getTokenFromCode(e){const t=this.input.charCodeAt(this.state.pos+1);return 123===e&&124===t?this.finishOp(c.braceBarL,2):!this.state.inType||62!==e&&60!==e?this.state.inType&&63===e?46===t?this.finishOp(c.questionDot,2):this.finishOp(c.question,1):function(e,t){return 64===e&&64===t}(e,t)?(this.state.pos+=2,this.readIterator()):super.getTokenFromCode(e):this.finishOp(c.relational,1)}isAssignable(e,t){switch(e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":return!0;case"ObjectExpression":{const t=e.properties.length-1;return e.properties.every(((e,r)=>"ObjectMethod"!==e.type&&(r===t||"SpreadElement"===e.type)&&this.isAssignable(e)))}case"ObjectProperty":return this.isAssignable(e.value);case"SpreadElement":return this.isAssignable(e.argument);case"ArrayExpression":return e.elements.every((e=>this.isAssignable(e)));case"AssignmentExpression":return"="===e.operator;case"ParenthesizedExpression":case"TypeCastExpression":return this.isAssignable(e.expression);case"MemberExpression":case"OptionalMemberExpression":return!t;default:return!1}}toAssignable(e,t=!1){return"TypeCastExpression"===e.type?super.toAssignable(this.typeCastToParameter(e),t):super.toAssignable(e,t)}toAssignableList(e,t,r){for(let t=0;t<e.length;t++){const r=e[t];"TypeCastExpression"===(null==r?void 0:r.type)&&(e[t]=this.typeCastToParameter(r))}return super.toAssignableList(e,t,r)}toReferencedList(e,t){for(let n=0;n<e.length;n++){var r;const i=e[n];!i||"TypeCastExpression"!==i.type||null!=(r=i.extra)&&r.parenthesized||!(e.length>1)&&t||this.raise(i.typeAnnotation.start,$.TypeCastInPattern)}return e}parseArrayLike(e,t,r,n){const i=super.parseArrayLike(e,t,r,n);return t&&!this.state.maybeInArrowParameters&&this.toReferencedList(i.elements),i}checkLVal(e,...t){if("TypeCastExpression"!==e.type)return super.checkLVal(e,...t)}parseClassProperty(e){return this.match(c.colon)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassProperty(e)}parseClassPrivateProperty(e){return this.match(c.colon)&&(e.typeAnnotation=this.flowParseTypeAnnotation()),super.parseClassPrivateProperty(e)}isClassMethod(){return this.isRelational("<")||super.isClassMethod()}isClassProperty(){return this.match(c.colon)||super.isClassProperty()}isNonstaticConstructor(e){return!this.match(c.colon)&&super.isNonstaticConstructor(e)}pushClassMethod(e,t,r,n,i,s){if(t.variance&&this.unexpected(t.variance.start),delete t.variance,this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassMethod(e,t,r,n,i,s),t.params&&i){const e=t.params;e.length>0&&this.isThisParam(e[0])&&this.raise(t.start,$.ThisParamBannedInConstructor)}else if("MethodDefinition"===t.type&&i&&t.value.params){const e=t.value.params;e.length>0&&this.isThisParam(e[0])&&this.raise(t.start,$.ThisParamBannedInConstructor)}}pushClassPrivateMethod(e,t,r,n){t.variance&&this.unexpected(t.variance.start),delete t.variance,this.isRelational("<")&&(t.typeParameters=this.flowParseTypeParameterDeclaration()),super.pushClassPrivateMethod(e,t,r,n)}parseClassSuper(e){if(super.parseClassSuper(e),e.superClass&&this.isRelational("<")&&(e.superTypeParameters=this.flowParseTypeParameterInstantiation()),this.isContextual("implements")){this.next();const t=e.implements=[];do{const e=this.startNode();e.id=this.flowParseRestrictedIdentifier(!0),this.isRelational("<")?e.typeParameters=this.flowParseTypeParameterInstantiation():e.typeParameters=null,t.push(this.finishNode(e,"ClassImplements"))}while(this.eat(c.comma))}}checkGetterSetterParams(e){super.checkGetterSetterParams(e);const t=this.getObjectOrClassMethodParams(e);if(t.length>0){const r=t[0];this.isThisParam(r)&&"get"===e.kind?this.raise(r.start,$.GetterMayNotHaveThisParam):this.isThisParam(r)&&this.raise(r.start,$.SetterMayNotHaveThisParam)}}parsePropertyName(e,t){const r=this.flowParseVariance(),n=super.parsePropertyName(e,t);return e.variance=r,n}parseObjPropValue(e,t,r,n,i,s,a,o){let l;e.variance&&this.unexpected(e.variance.start),delete e.variance,this.isRelational("<")&&!a&&(l=this.flowParseTypeParameterDeclaration(),this.match(c.parenL)||this.unexpected()),super.parseObjPropValue(e,t,r,n,i,s,a,o),l&&((e.value||e).typeParameters=l)}parseAssignableListItemTypes(e){return this.eat(c.question)&&("Identifier"!==e.type&&this.raise(e.start,$.OptionalBindingPattern),this.isThisParam(e)&&this.raise(e.start,$.ThisParamMayNotBeOptional),e.optional=!0),this.match(c.colon)?e.typeAnnotation=this.flowParseTypeAnnotation():this.isThisParam(e)&&this.raise(e.start,$.ThisParamAnnotationRequired),this.match(c.eq)&&this.isThisParam(e)&&this.raise(e.start,$.ThisParamNoDefault),this.resetEndLocation(e),e}parseMaybeDefault(e,t,r){const n=super.parseMaybeDefault(e,t,r);return"AssignmentPattern"===n.type&&n.typeAnnotation&&n.right.start<n.typeAnnotation.start&&this.raise(n.typeAnnotation.start,$.TypeBeforeInitializer),n}shouldParseDefaultImport(e){return Q(e)?Z(this.state):super.shouldParseDefaultImport(e)}parseImportSpecifierLocal(e,t,r,n){t.local=Q(e)?this.flowParseRestrictedIdentifier(!0,!0):this.parseIdentifier(),this.checkLVal(t.local,n,9),e.specifiers.push(this.finishNode(t,r))}maybeParseDefaultImportSpecifier(e){e.importKind="value";let t=null;if(this.match(c._typeof)?t="typeof":this.isContextual("type")&&(t="type"),t){const r=this.lookahead();"type"===t&&r.type===c.star&&this.unexpected(r.start),(Z(r)||r.type===c.braceL||r.type===c.star)&&(this.next(),e.importKind=t)}return super.maybeParseDefaultImportSpecifier(e)}parseImportSpecifier(e){const t=this.startNode(),r=this.state.start,n=this.parseModuleExportName();let i=null;"Identifier"===n.type&&("type"===n.name?i="type":"typeof"===n.name&&(i="typeof"));let s=!1;if(this.isContextual("as")&&!this.isLookaheadContextual("as")){const e=this.parseIdentifier(!0);null===i||this.match(c.name)||this.state.type.keyword?(t.imported=n,t.importKind=null,t.local=this.parseIdentifier()):(t.imported=e,t.importKind=i,t.local=e.__clone())}else if(null!==i&&(this.match(c.name)||this.state.type.keyword))t.imported=this.parseIdentifier(!0),t.importKind=i,this.eatContextual("as")?t.local=this.parseIdentifier():(s=!0,t.local=t.imported.__clone());else{if("StringLiteral"===n.type)throw this.raise(t.start,E.ImportBindingIsString,n.value);s=!0,t.imported=n,t.importKind=null,t.local=t.imported.__clone()}const a=Q(e),o=Q(t);a&&o&&this.raise(r,$.ImportTypeShorthandOnlyInPureImport),(a||o)&&this.checkReservedType(t.local.name,t.local.start,!0),!s||a||o||this.checkReservedWord(t.local.name,t.start,!0,!0),this.checkLVal(t.local,"import specifier",9),e.specifiers.push(this.finishNode(t,"ImportSpecifier"))}parseBindingAtom(){return this.state.type===c._this?this.parseIdentifier(!0):super.parseBindingAtom()}parseFunctionParams(e,t){const r=e.kind;"get"!==r&&"set"!==r&&this.isRelational("<")&&(e.typeParameters=this.flowParseTypeParameterDeclaration()),super.parseFunctionParams(e,t)}parseVarId(e,t){super.parseVarId(e,t),this.match(c.colon)&&(e.id.typeAnnotation=this.flowParseTypeAnnotation(),this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,t){if(this.match(c.colon)){const t=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0,e.returnType=this.flowParseTypeAnnotation(),this.state.noAnonFunctionType=t}return super.parseAsyncArrowFromCallExpression(e,t)}shouldParseAsyncArrow(){return this.match(c.colon)||super.shouldParseAsyncArrow()}parseMaybeAssign(e,t,r){var n;let i,s=null;if(this.hasPlugin("jsx")&&(this.match(c.jsxTagStart)||this.isRelational("<"))){if(s=this.state.clone(),i=this.tryParse((()=>super.parseMaybeAssign(e,t,r)),s),!i.error)return i.node;const{context:n}=this.state;n[n.length-1]===O.j_oTag?n.length-=2:n[n.length-1]===O.j_expr&&(n.length-=1)}if(null!=(n=i)&&n.error||this.isRelational("<")){var a,o;let n;s=s||this.state.clone();const l=this.tryParse((i=>{var s;n=this.flowParseTypeParameterDeclaration();const a=this.forwardNoArrowParamsConversionAt(n,(()=>{const i=super.parseMaybeAssign(e,t,r);return this.resetStartLocationFromNode(i,n),i}));"ArrowFunctionExpression"!==a.type&&null!=(s=a.extra)&&s.parenthesized&&i();const o=this.maybeUnwrapTypeCastExpression(a);return o.typeParameters=n,this.resetStartLocationFromNode(o,n),a}),s);let p=null;if(l.node&&"ArrowFunctionExpression"===this.maybeUnwrapTypeCastExpression(l.node).type){if(!l.error&&!l.aborted)return l.node.async&&this.raise(n.start,$.UnexpectedTypeParameterBeforeAsyncArrowFunction),l.node;p=l.node}if(null!=(a=i)&&a.node)return this.state=i.failState,i.node;if(p)return this.state=l.failState,p;if(null!=(o=i)&&o.thrown)throw i.error;if(l.thrown)throw l.error;throw this.raise(n.start,$.UnexpectedTokenAfterTypeParameter)}return super.parseMaybeAssign(e,t,r)}parseArrow(e){if(this.match(c.colon)){const t=this.tryParse((()=>{const t=this.state.noAnonFunctionType;this.state.noAnonFunctionType=!0;const r=this.startNode();return[r.typeAnnotation,e.predicate]=this.flowParseTypeAndPredicateInitialiser(),this.state.noAnonFunctionType=t,this.canInsertSemicolon()&&this.unexpected(),this.match(c.arrow)||this.unexpected(),r}));if(t.thrown)return null;t.error&&(this.state=t.failState),e.returnType=t.node.typeAnnotation?this.finishNode(t.node,"TypeAnnotation"):null}return super.parseArrow(e)}shouldParseArrow(){return this.match(c.colon)||super.shouldParseArrow()}setArrowFunctionParameters(e,t){-1!==this.state.noArrowParamsConversionAt.indexOf(e.start)?e.params=t:super.setArrowFunctionParameters(e,t)}checkParams(e,t,r){if(!r||-1===this.state.noArrowParamsConversionAt.indexOf(e.start)){for(let t=0;t<e.params.length;t++)this.isThisParam(e.params[t])&&t>0&&this.raise(e.params[t].start,$.ThisParamMustBeFirst);return super.checkParams(...arguments)}}parseParenAndDistinguishExpression(e){return super.parseParenAndDistinguishExpression(e&&-1===this.state.noArrowAt.indexOf(this.state.start))}parseSubscripts(e,t,r,n){if("Identifier"===e.type&&"async"===e.name&&-1!==this.state.noArrowAt.indexOf(t)){this.next();const n=this.startNodeAt(t,r);n.callee=e,n.arguments=this.parseCallExpressionArguments(c.parenR,!1),e=this.finishNode(n,"CallExpression")}else if("Identifier"===e.type&&"async"===e.name&&this.isRelational("<")){const i=this.state.clone(),s=this.tryParse((e=>this.parseAsyncArrowWithTypeParameters(t,r)||e()),i);if(!s.error&&!s.aborted)return s.node;const a=this.tryParse((()=>super.parseSubscripts(e,t,r,n)),i);if(a.node&&!a.error)return a.node;if(s.node)return this.state=s.failState,s.node;if(a.node)return this.state=a.failState,a.node;throw s.error||a.error}return super.parseSubscripts(e,t,r,n)}parseSubscript(e,t,r,n,i){if(this.match(c.questionDot)&&this.isLookaheadToken_lt()){if(i.optionalChainMember=!0,n)return i.stop=!0,e;this.next();const s=this.startNodeAt(t,r);return s.callee=e,s.typeArguments=this.flowParseTypeParameterInstantiation(),this.expect(c.parenL),s.arguments=this.parseCallExpressionArguments(c.parenR,!1),s.optional=!0,this.finishCallExpression(s,!0)}if(!n&&this.shouldParseTypes()&&this.isRelational("<")){const n=this.startNodeAt(t,r);n.callee=e;const s=this.tryParse((()=>(n.typeArguments=this.flowParseTypeParameterInstantiationCallOrNew(),this.expect(c.parenL),n.arguments=this.parseCallExpressionArguments(c.parenR,!1),i.optionalChainMember&&(n.optional=!1),this.finishCallExpression(n,i.optionalChainMember))));if(s.node)return s.error&&(this.state=s.failState),s.node}return super.parseSubscript(e,t,r,n,i)}parseNewArguments(e){let t=null;this.shouldParseTypes()&&this.isRelational("<")&&(t=this.tryParse((()=>this.flowParseTypeParameterInstantiationCallOrNew())).node),e.typeArguments=t,super.parseNewArguments(e)}parseAsyncArrowWithTypeParameters(e,t){const r=this.startNodeAt(e,t);if(this.parseFunctionParams(r),this.parseArrow(r))return this.parseArrowExpression(r,void 0,!0)}readToken_mult_modulo(e){const t=this.input.charCodeAt(this.state.pos+1);if(42===e&&47===t&&this.state.hasFlowComment)return this.state.hasFlowComment=!1,this.state.pos+=2,void this.nextToken();super.readToken_mult_modulo(e)}readToken_pipe_amp(e){const t=this.input.charCodeAt(this.state.pos+1);124!==e||125!==t?super.readToken_pipe_amp(e):this.finishOp(c.braceBarR,2)}parseTopLevel(e,t){const r=super.parseTopLevel(e,t);return this.state.hasFlowComment&&this.raise(this.state.pos,$.UnterminatedFlowComment),r}skipBlockComment(){if(this.hasPlugin("flowComments")&&this.skipFlowComment())return this.state.hasFlowComment&&this.unexpected(null,$.NestedFlowComment),this.hasFlowCommentCompletion(),this.state.pos+=this.skipFlowComment(),void(this.state.hasFlowComment=!0);if(this.state.hasFlowComment){const e=this.input.indexOf("*-/",this.state.pos+=2);if(-1===e)throw this.raise(this.state.pos-2,E.UnterminatedComment);this.state.pos=e+3}else super.skipBlockComment()}skipFlowComment(){const{pos:e}=this.state;let t=2;for(;[32,9].includes(this.input.charCodeAt(e+t));)t++;const r=this.input.charCodeAt(t+e),n=this.input.charCodeAt(t+e+1);return 58===r&&58===n?t+2:"flow-include"===this.input.slice(t+e,t+e+12)?t+12:58===r&&58!==n&&t}hasFlowCommentCompletion(){if(-1===this.input.indexOf("*/",this.state.pos))throw this.raise(this.state.pos,E.UnterminatedComment)}flowEnumErrorBooleanMemberNotInitialized(e,{enumName:t,memberName:r}){this.raise(e,$.EnumBooleanMemberNotInitialized,r,t)}flowEnumErrorInvalidMemberName(e,{enumName:t,memberName:r}){const n=r[0].toUpperCase()+r.slice(1);this.raise(e,$.EnumInvalidMemberName,r,n,t)}flowEnumErrorDuplicateMemberName(e,{enumName:t,memberName:r}){this.raise(e,$.EnumDuplicateMemberName,r,t)}flowEnumErrorInconsistentMemberValues(e,{enumName:t}){this.raise(e,$.EnumInconsistentMemberValues,t)}flowEnumErrorInvalidExplicitType(e,{enumName:t,suppliedType:r}){return this.raise(e,null===r?$.EnumInvalidExplicitTypeUnknownSupplied:$.EnumInvalidExplicitType,t,r)}flowEnumErrorInvalidMemberInitializer(e,{enumName:t,explicitType:r,memberName:n}){let i=null;switch(r){case"boolean":case"number":case"string":i=$.EnumInvalidMemberInitializerPrimaryType;break;case"symbol":i=$.EnumInvalidMemberInitializerSymbolType;break;default:i=$.EnumInvalidMemberInitializerUnknownType}return this.raise(e,i,t,n,r)}flowEnumErrorNumberMemberNotInitialized(e,{enumName:t,memberName:r}){this.raise(e,$.EnumNumberMemberNotInitialized,t,r)}flowEnumErrorStringMemberInconsistentlyInitailized(e,{enumName:t}){this.raise(e,$.EnumStringMemberInconsistentlyInitailized,t)}flowEnumMemberInit(){const e=this.state.start,t=()=>this.match(c.comma)||this.match(c.braceR);switch(this.state.type){case c.num:{const r=this.parseLiteral(this.state.value,"NumericLiteral");return t()?{type:"number",pos:r.start,value:r}:{type:"invalid",pos:e}}case c.string:{const r=this.parseLiteral(this.state.value,"StringLiteral");return t()?{type:"string",pos:r.start,value:r}:{type:"invalid",pos:e}}case c._true:case c._false:{const r=this.parseBooleanLiteral();return t()?{type:"boolean",pos:r.start,value:r}:{type:"invalid",pos:e}}default:return{type:"invalid",pos:e}}}flowEnumMemberRaw(){const e=this.state.start;return{id:this.parseIdentifier(!0),init:this.eat(c.eq)?this.flowEnumMemberInit():{type:"none",pos:e}}}flowEnumCheckExplicitTypeMismatch(e,t,r){const{explicitType:n}=t;null!==n&&n!==r&&this.flowEnumErrorInvalidMemberInitializer(e,t)}flowEnumMembers({enumName:e,explicitType:t}){const r=new Set,n={booleanMembers:[],numberMembers:[],stringMembers:[],defaultedMembers:[]};let i=!1;for(;!this.match(c.braceR);){if(this.eat(c.ellipsis)){i=!0;break}const s=this.startNode(),{id:a,init:o}=this.flowEnumMemberRaw(),l=a.name;if(""===l)continue;/^[a-z]/.test(l)&&this.flowEnumErrorInvalidMemberName(a.start,{enumName:e,memberName:l}),r.has(l)&&this.flowEnumErrorDuplicateMemberName(a.start,{enumName:e,memberName:l}),r.add(l);const p={enumName:e,explicitType:t,memberName:l};switch(s.id=a,o.type){case"boolean":this.flowEnumCheckExplicitTypeMismatch(o.pos,p,"boolean"),s.init=o.value,n.booleanMembers.push(this.finishNode(s,"EnumBooleanMember"));break;case"number":this.flowEnumCheckExplicitTypeMismatch(o.pos,p,"number"),s.init=o.value,n.numberMembers.push(this.finishNode(s,"EnumNumberMember"));break;case"string":this.flowEnumCheckExplicitTypeMismatch(o.pos,p,"string"),s.init=o.value,n.stringMembers.push(this.finishNode(s,"EnumStringMember"));break;case"invalid":throw this.flowEnumErrorInvalidMemberInitializer(o.pos,p);case"none":switch(t){case"boolean":this.flowEnumErrorBooleanMemberNotInitialized(o.pos,p);break;case"number":this.flowEnumErrorNumberMemberNotInitialized(o.pos,p);break;default:n.defaultedMembers.push(this.finishNode(s,"EnumDefaultedMember"))}}this.match(c.braceR)||this.expect(c.comma)}return{members:n,hasUnknownMembers:i}}flowEnumStringMembers(e,t,{enumName:r}){if(0===e.length)return t;if(0===t.length)return e;if(t.length>e.length){for(const t of e)this.flowEnumErrorStringMemberInconsistentlyInitailized(t.start,{enumName:r});return t}for(const e of t)this.flowEnumErrorStringMemberInconsistentlyInitailized(e.start,{enumName:r});return e}flowEnumParseExplicitType({enumName:e}){if(this.eatContextual("of")){if(!this.match(c.name))throw this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:e,suppliedType:null});const{value:t}=this.state;return this.next(),"boolean"!==t&&"number"!==t&&"string"!==t&&"symbol"!==t&&this.flowEnumErrorInvalidExplicitType(this.state.start,{enumName:e,suppliedType:t}),t}return null}flowEnumBody(e,{enumName:t,nameLoc:r}){const n=this.flowEnumParseExplicitType({enumName:t});this.expect(c.braceL);const{members:i,hasUnknownMembers:s}=this.flowEnumMembers({enumName:t,explicitType:n});switch(e.hasUnknownMembers=s,n){case"boolean":return e.explicitType=!0,e.members=i.booleanMembers,this.expect(c.braceR),this.finishNode(e,"EnumBooleanBody");case"number":return e.explicitType=!0,e.members=i.numberMembers,this.expect(c.braceR),this.finishNode(e,"EnumNumberBody");case"string":return e.explicitType=!0,e.members=this.flowEnumStringMembers(i.stringMembers,i.defaultedMembers,{enumName:t}),this.expect(c.braceR),this.finishNode(e,"EnumStringBody");case"symbol":return e.members=i.defaultedMembers,this.expect(c.braceR),this.finishNode(e,"EnumSymbolBody");default:{const n=()=>(e.members=[],this.expect(c.braceR),this.finishNode(e,"EnumStringBody"));e.explicitType=!1;const s=i.booleanMembers.length,a=i.numberMembers.length,o=i.stringMembers.length,l=i.defaultedMembers.length;if(s||a||o||l){if(s||a){if(!a&&!o&&s>=l){for(const e of i.defaultedMembers)this.flowEnumErrorBooleanMemberNotInitialized(e.start,{enumName:t,memberName:e.id.name});return e.members=i.booleanMembers,this.expect(c.braceR),this.finishNode(e,"EnumBooleanBody")}if(!s&&!o&&a>=l){for(const e of i.defaultedMembers)this.flowEnumErrorNumberMemberNotInitialized(e.start,{enumName:t,memberName:e.id.name});return e.members=i.numberMembers,this.expect(c.braceR),this.finishNode(e,"EnumNumberBody")}return this.flowEnumErrorInconsistentMemberValues(r,{enumName:t}),n()}return e.members=this.flowEnumStringMembers(i.stringMembers,i.defaultedMembers,{enumName:t}),this.expect(c.braceR),this.finishNode(e,"EnumStringBody")}return n()}}}flowParseEnumDeclaration(e){const t=this.parseIdentifier();return e.id=t,e.body=this.flowEnumBody(this.startNode(),{enumName:t.name,nameLoc:t.start}),this.finishNode(e,"EnumDeclaration")}updateContext(e){this.match(c.name)&&"of"===this.state.value&&e===c.name&&"interface"===this.input.slice(this.state.lastTokStart,this.state.lastTokEnd)?this.state.exprAllowed=!1:super.updateContext(e)}isLookaheadToken_lt(){const e=this.nextTokenStart();if(60===this.input.charCodeAt(e)){const t=this.input.charCodeAt(e+1);return 60!==t&&61!==t}return!1}maybeUnwrapTypeCastExpression(e){return"TypeCastExpression"===e.type?e.expression:e}},typescript:e=>class extends e{getScopeHandler(){return pe}tsIsIdentifier(){return this.match(c.name)}tsTokenCanFollowModifier(){return(this.match(c.bracketL)||this.match(c.braceL)||this.match(c.star)||this.match(c.ellipsis)||this.match(c.privateName)||this.isLiteralPropertyName())&&!this.hasPrecedingLineBreak()}tsNextTokenCanFollowModifier(){return this.next(),this.tsTokenCanFollowModifier()}tsParseModifier(e){if(!this.match(c.name))return;const t=this.state.value;return-1!==e.indexOf(t)&&this.tsTryParse(this.tsNextTokenCanFollowModifier.bind(this))?t:void 0}tsParseModifiers(e,t,r,n){const i=(t,r,n,i)=>{r===n&&e[i]&&this.raise(t,fe.InvalidModifiersOrder,n,i)},s=(t,r,n,i)=>{(e[n]&&r===i||e[i]&&r===n)&&this.raise(t,fe.IncompatibleModifiers,n,i)};for(;;){const a=this.state.start,o=this.tsParseModifier(t.concat(null!=r?r:[]));if(!o)break;me(o)?e.accessibility?this.raise(a,fe.DuplicateAccessibilityModifier):(i(a,o,o,"override"),i(a,o,o,"static"),e.accessibility=o):(Object.hasOwnProperty.call(e,o)?this.raise(a,fe.DuplicateModifier,o):(i(a,o,"static","readonly"),i(a,o,"static","override"),i(a,o,"override","readonly"),i(a,o,"abstract","override"),s(a,o,"declare","override"),s(a,o,"static","abstract")),e[o]=!0),null!=r&&r.includes(o)&&this.raise(a,n,o)}}tsIsListTerminator(e){switch(e){case"EnumMembers":case"TypeMembers":return this.match(c.braceR);case"HeritageClauseElement":return this.match(c.braceL);case"TupleElementTypes":return this.match(c.bracketR);case"TypeParametersOrArguments":return this.isRelational(">")}throw new Error("Unreachable")}tsParseList(e,t){const r=[];for(;!this.tsIsListTerminator(e);)r.push(t());return r}tsParseDelimitedList(e,t){return de(this.tsParseDelimitedListWorker(e,t,!0))}tsParseDelimitedListWorker(e,t,r){const n=[];for(;!this.tsIsListTerminator(e);){const i=t();if(null==i)return;if(n.push(i),!this.eat(c.comma)){if(this.tsIsListTerminator(e))break;return void(r&&this.expect(c.comma))}}return n}tsParseBracketedList(e,t,r,n){n||(r?this.expect(c.bracketL):this.expectRelational("<"));const i=this.tsParseDelimitedList(e,t);return r?this.expect(c.bracketR):this.expectRelational(">"),i}tsParseImportType(){const e=this.startNode();return this.expect(c._import),this.expect(c.parenL),this.match(c.string)||this.raise(this.state.start,fe.UnsupportedImportTypeArgument),e.argument=this.parseExprAtom(),this.expect(c.parenR),this.eat(c.dot)&&(e.qualifier=this.tsParseEntityName(!0)),this.isRelational("<")&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSImportType")}tsParseEntityName(e){let t=this.parseIdentifier();for(;this.eat(c.dot);){const r=this.startNodeAtNode(t);r.left=t,r.right=this.parseIdentifier(e),t=this.finishNode(r,"TSQualifiedName")}return t}tsParseTypeReference(){const e=this.startNode();return e.typeName=this.tsParseEntityName(!1),!this.hasPrecedingLineBreak()&&this.isRelational("<")&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSTypeReference")}tsParseThisTypePredicate(e){this.next();const t=this.startNodeAtNode(e);return t.parameterName=e,t.typeAnnotation=this.tsParseTypeAnnotation(!1),t.asserts=!1,this.finishNode(t,"TSTypePredicate")}tsParseThisTypeNode(){const e=this.startNode();return this.next(),this.finishNode(e,"TSThisType")}tsParseTypeQuery(){const e=this.startNode();return this.expect(c._typeof),this.match(c._import)?e.exprName=this.tsParseImportType():e.exprName=this.tsParseEntityName(!0),this.finishNode(e,"TSTypeQuery")}tsParseTypeParameter(){const e=this.startNode();return e.name=this.parseIdentifierName(e.start),e.constraint=this.tsEatThenParseType(c._extends),e.default=this.tsEatThenParseType(c.eq),this.finishNode(e,"TSTypeParameter")}tsTryParseTypeParameters(){if(this.isRelational("<"))return this.tsParseTypeParameters()}tsParseTypeParameters(){const e=this.startNode();return this.isRelational("<")||this.match(c.jsxTagStart)?this.next():this.unexpected(),e.params=this.tsParseBracketedList("TypeParametersOrArguments",this.tsParseTypeParameter.bind(this),!1,!0),0===e.params.length&&this.raise(e.start,fe.EmptyTypeParameters),this.finishNode(e,"TSTypeParameterDeclaration")}tsTryNextParseConstantContext(){return this.lookahead().type===c._const?(this.next(),this.tsParseTypeReference()):null}tsFillSignature(e,t){const r=e===c.arrow;t.typeParameters=this.tsTryParseTypeParameters(),this.expect(c.parenL),t.parameters=this.tsParseBindingListForSignature(),(r||this.match(e))&&(t.typeAnnotation=this.tsParseTypeOrTypePredicateAnnotation(e))}tsParseBindingListForSignature(){return this.parseBindingList(c.parenR,41).map((e=>("Identifier"!==e.type&&"RestElement"!==e.type&&"ObjectPattern"!==e.type&&"ArrayPattern"!==e.type&&this.raise(e.start,fe.UnsupportedSignatureParameterKind,e.type),e)))}tsParseTypeMemberSemicolon(){this.eat(c.comma)||this.isLineTerminator()||this.expect(c.semi)}tsParseSignatureMember(e,t){return this.tsFillSignature(c.colon,t),this.tsParseTypeMemberSemicolon(),this.finishNode(t,e)}tsIsUnambiguouslyIndexSignature(){return this.next(),this.eat(c.name)&&this.match(c.colon)}tsTryParseIndexSignature(e){if(!this.match(c.bracketL)||!this.tsLookAhead(this.tsIsUnambiguouslyIndexSignature.bind(this)))return;this.expect(c.bracketL);const t=this.parseIdentifier();t.typeAnnotation=this.tsParseTypeAnnotation(),this.resetEndLocation(t),this.expect(c.bracketR),e.parameters=[t];const r=this.tsTryParseTypeAnnotation();return r&&(e.typeAnnotation=r),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSIndexSignature")}tsParsePropertyOrMethodSignature(e,t){this.eat(c.question)&&(e.optional=!0);const r=e;if(this.match(c.parenL)||this.isRelational("<")){t&&this.raise(e.start,fe.ReadonlyForMethodSignature);const n=r;if(n.kind&&this.isRelational("<")&&this.raise(this.state.pos,fe.AccesorCannotHaveTypeParameters),this.tsFillSignature(c.colon,n),this.tsParseTypeMemberSemicolon(),"get"===n.kind)n.parameters.length>0&&(this.raise(this.state.pos,E.BadGetterArity),this.isThisParam(n.parameters[0])&&this.raise(this.state.pos,fe.AccesorCannotDeclareThisParameter));else if("set"===n.kind){if(1!==n.parameters.length)this.raise(this.state.pos,E.BadSetterArity);else{const e=n.parameters[0];this.isThisParam(e)&&this.raise(this.state.pos,fe.AccesorCannotDeclareThisParameter),"Identifier"===e.type&&e.optional&&this.raise(this.state.pos,fe.SetAccesorCannotHaveOptionalParameter),"RestElement"===e.type&&this.raise(this.state.pos,fe.SetAccesorCannotHaveRestParameter)}n.typeAnnotation&&this.raise(n.typeAnnotation.start,fe.SetAccesorCannotHaveReturnType)}else n.kind="method";return this.finishNode(n,"TSMethodSignature")}{const e=r;t&&(e.readonly=!0);const n=this.tsTryParseTypeAnnotation();return n&&(e.typeAnnotation=n),this.tsParseTypeMemberSemicolon(),this.finishNode(e,"TSPropertySignature")}}tsParseTypeMember(){const e=this.startNode();if(this.match(c.parenL)||this.isRelational("<"))return this.tsParseSignatureMember("TSCallSignatureDeclaration",e);if(this.match(c._new)){const t=this.startNode();return this.next(),this.match(c.parenL)||this.isRelational("<")?this.tsParseSignatureMember("TSConstructSignatureDeclaration",e):(e.key=this.createIdentifier(t,"new"),this.tsParsePropertyOrMethodSignature(e,!1))}this.tsParseModifiers(e,["readonly"],["declare","abstract","private","protected","public","static","override"],fe.InvalidModifierOnTypeMember);return this.tsTryParseIndexSignature(e)||(this.parsePropertyName(e,!1),e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||!this.tsTokenCanFollowModifier()||(e.kind=e.key.name,this.parsePropertyName(e,!1)),this.tsParsePropertyOrMethodSignature(e,!!e.readonly))}tsParseTypeLiteral(){const e=this.startNode();return e.members=this.tsParseObjectTypeMembers(),this.finishNode(e,"TSTypeLiteral")}tsParseObjectTypeMembers(){this.expect(c.braceL);const e=this.tsParseList("TypeMembers",this.tsParseTypeMember.bind(this));return this.expect(c.braceR),e}tsIsStartOfMappedType(){return this.next(),this.eat(c.plusMin)?this.isContextual("readonly"):(this.isContextual("readonly")&&this.next(),!!this.match(c.bracketL)&&(this.next(),!!this.tsIsIdentifier()&&(this.next(),this.match(c._in))))}tsParseMappedTypeParameter(){const e=this.startNode();return e.name=this.parseIdentifierName(e.start),e.constraint=this.tsExpectThenParseType(c._in),this.finishNode(e,"TSTypeParameter")}tsParseMappedType(){const e=this.startNode();return this.expect(c.braceL),this.match(c.plusMin)?(e.readonly=this.state.value,this.next(),this.expectContextual("readonly")):this.eatContextual("readonly")&&(e.readonly=!0),this.expect(c.bracketL),e.typeParameter=this.tsParseMappedTypeParameter(),e.nameType=this.eatContextual("as")?this.tsParseType():null,this.expect(c.bracketR),this.match(c.plusMin)?(e.optional=this.state.value,this.next(),this.expect(c.question)):this.eat(c.question)&&(e.optional=!0),e.typeAnnotation=this.tsTryParseType(),this.semicolon(),this.expect(c.braceR),this.finishNode(e,"TSMappedType")}tsParseTupleType(){const e=this.startNode();e.elementTypes=this.tsParseBracketedList("TupleElementTypes",this.tsParseTupleElementType.bind(this),!0,!1);let t=!1,r=null;return e.elementTypes.forEach((e=>{var n;let{type:i}=e;!t||"TSRestType"===i||"TSOptionalType"===i||"TSNamedTupleMember"===i&&e.optional||this.raise(e.start,fe.OptionalTypeBeforeRequired),t=t||"TSNamedTupleMember"===i&&e.optional||"TSOptionalType"===i,"TSRestType"===i&&(i=(e=e.typeAnnotation).type);const s="TSNamedTupleMember"===i;r=null!=(n=r)?n:s,r!==s&&this.raise(e.start,fe.MixedLabeledAndUnlabeledElements)})),this.finishNode(e,"TSTupleType")}tsParseTupleElementType(){const{start:e,startLoc:t}=this.state,r=this.eat(c.ellipsis);let n=this.tsParseType();const i=this.eat(c.question);if(this.eat(c.colon)){const e=this.startNodeAtNode(n);e.optional=i,"TSTypeReference"!==n.type||n.typeParameters||"Identifier"!==n.typeName.type?(this.raise(n.start,fe.InvalidTupleMemberLabel),e.label=n):e.label=n.typeName,e.elementType=this.tsParseType(),n=this.finishNode(e,"TSNamedTupleMember")}else if(i){const e=this.startNodeAtNode(n);e.typeAnnotation=n,n=this.finishNode(e,"TSOptionalType")}if(r){const r=this.startNodeAt(e,t);r.typeAnnotation=n,n=this.finishNode(r,"TSRestType")}return n}tsParseParenthesizedType(){const e=this.startNode();return this.expect(c.parenL),e.typeAnnotation=this.tsParseType(),this.expect(c.parenR),this.finishNode(e,"TSParenthesizedType")}tsParseFunctionOrConstructorType(e,t){const r=this.startNode();return"TSConstructorType"===e&&(r.abstract=!!t,t&&this.next(),this.next()),this.tsFillSignature(c.arrow,r),this.finishNode(r,e)}tsParseLiteralTypeNode(){const e=this.startNode();return e.literal=(()=>{switch(this.state.type){case c.num:case c.bigint:case c.string:case c._true:case c._false:return this.parseExprAtom();default:throw this.unexpected()}})(),this.finishNode(e,"TSLiteralType")}tsParseTemplateLiteralType(){const e=this.startNode();return e.literal=this.parseTemplate(!1),this.finishNode(e,"TSLiteralType")}parseTemplateSubstitution(){return this.state.inType?this.tsParseType():super.parseTemplateSubstitution()}tsParseThisTypeOrThisTypePredicate(){const e=this.tsParseThisTypeNode();return this.isContextual("is")&&!this.hasPrecedingLineBreak()?this.tsParseThisTypePredicate(e):e}tsParseNonArrayType(){switch(this.state.type){case c.name:case c._void:case c._null:{const e=this.match(c._void)?"TSVoidKeyword":this.match(c._null)?"TSNullKeyword":function(e){switch(e){case"any":return"TSAnyKeyword";case"boolean":return"TSBooleanKeyword";case"bigint":return"TSBigIntKeyword";case"never":return"TSNeverKeyword";case"number":return"TSNumberKeyword";case"object":return"TSObjectKeyword";case"string":return"TSStringKeyword";case"symbol":return"TSSymbolKeyword";case"undefined":return"TSUndefinedKeyword";case"unknown":return"TSUnknownKeyword";default:return}}(this.state.value);if(void 0!==e&&46!==this.lookaheadCharCode()){const t=this.startNode();return this.next(),this.finishNode(t,e)}return this.tsParseTypeReference()}case c.string:case c.num:case c.bigint:case c._true:case c._false:return this.tsParseLiteralTypeNode();case c.plusMin:if("-"===this.state.value){const e=this.startNode(),t=this.lookahead();if(t.type!==c.num&&t.type!==c.bigint)throw this.unexpected();return e.literal=this.parseMaybeUnary(),this.finishNode(e,"TSLiteralType")}break;case c._this:return this.tsParseThisTypeOrThisTypePredicate();case c._typeof:return this.tsParseTypeQuery();case c._import:return this.tsParseImportType();case c.braceL:return this.tsLookAhead(this.tsIsStartOfMappedType.bind(this))?this.tsParseMappedType():this.tsParseTypeLiteral();case c.bracketL:return this.tsParseTupleType();case c.parenL:return this.tsParseParenthesizedType();case c.backQuote:return this.tsParseTemplateLiteralType()}throw this.unexpected()}tsParseArrayTypeOrHigher(){let e=this.tsParseNonArrayType();for(;!this.hasPrecedingLineBreak()&&this.eat(c.bracketL);)if(this.match(c.bracketR)){const t=this.startNodeAtNode(e);t.elementType=e,this.expect(c.bracketR),e=this.finishNode(t,"TSArrayType")}else{const t=this.startNodeAtNode(e);t.objectType=e,t.indexType=this.tsParseType(),this.expect(c.bracketR),e=this.finishNode(t,"TSIndexedAccessType")}return e}tsParseTypeOperator(e){const t=this.startNode();return this.expectContextual(e),t.operator=e,t.typeAnnotation=this.tsParseTypeOperatorOrHigher(),"readonly"===e&&this.tsCheckTypeAnnotationForReadOnly(t),this.finishNode(t,"TSTypeOperator")}tsCheckTypeAnnotationForReadOnly(e){switch(e.typeAnnotation.type){case"TSTupleType":case"TSArrayType":return;default:this.raise(e.start,fe.UnexpectedReadonly)}}tsParseInferType(){const e=this.startNode();this.expectContextual("infer");const t=this.startNode();return t.name=this.parseIdentifierName(t.start),e.typeParameter=this.finishNode(t,"TSTypeParameter"),this.finishNode(e,"TSInferType")}tsParseTypeOperatorOrHigher(){const e=["keyof","unique","readonly"].find((e=>this.isContextual(e)));return e?this.tsParseTypeOperator(e):this.isContextual("infer")?this.tsParseInferType():this.tsParseArrayTypeOrHigher()}tsParseUnionOrIntersectionType(e,t,r){const n=this.startNode(),i=this.eat(r),s=[];do{s.push(t())}while(this.eat(r));return 1!==s.length||i?(n.types=s,this.finishNode(n,e)):s[0]}tsParseIntersectionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSIntersectionType",this.tsParseTypeOperatorOrHigher.bind(this),c.bitwiseAND)}tsParseUnionTypeOrHigher(){return this.tsParseUnionOrIntersectionType("TSUnionType",this.tsParseIntersectionTypeOrHigher.bind(this),c.bitwiseOR)}tsIsStartOfFunctionType(){return!!this.isRelational("<")||this.match(c.parenL)&&this.tsLookAhead(this.tsIsUnambiguouslyStartOfFunctionType.bind(this))}tsSkipParameterStart(){if(this.match(c.name)||this.match(c._this))return this.next(),!0;if(this.match(c.braceL)){let e=1;for(this.next();e>0;)this.match(c.braceL)?++e:this.match(c.braceR)&&--e,this.next();return!0}if(this.match(c.bracketL)){let e=1;for(this.next();e>0;)this.match(c.bracketL)?++e:this.match(c.bracketR)&&--e,this.next();return!0}return!1}tsIsUnambiguouslyStartOfFunctionType(){if(this.next(),this.match(c.parenR)||this.match(c.ellipsis))return!0;if(this.tsSkipParameterStart()){if(this.match(c.colon)||this.match(c.comma)||this.match(c.question)||this.match(c.eq))return!0;if(this.match(c.parenR)&&(this.next(),this.match(c.arrow)))return!0}return!1}tsParseTypeOrTypePredicateAnnotation(e){return this.tsInType((()=>{const t=this.startNode();this.expect(e);const r=this.startNode(),n=!!this.tsTryParse(this.tsParseTypePredicateAsserts.bind(this));if(n&&this.match(c._this)){let e=this.tsParseThisTypeOrThisTypePredicate();return"TSThisType"===e.type?(r.parameterName=e,r.asserts=!0,e=this.finishNode(r,"TSTypePredicate")):(this.resetStartLocationFromNode(e,r),e.asserts=!0),t.typeAnnotation=e,this.finishNode(t,"TSTypeAnnotation")}const i=this.tsIsIdentifier()&&this.tsTryParse(this.tsParseTypePredicatePrefix.bind(this));if(!i)return n?(r.parameterName=this.parseIdentifier(),r.asserts=n,t.typeAnnotation=this.finishNode(r,"TSTypePredicate"),this.finishNode(t,"TSTypeAnnotation")):this.tsParseTypeAnnotation(!1,t);const s=this.tsParseTypeAnnotation(!1);return r.parameterName=i,r.typeAnnotation=s,r.asserts=n,t.typeAnnotation=this.finishNode(r,"TSTypePredicate"),this.finishNode(t,"TSTypeAnnotation")}))}tsTryParseTypeOrTypePredicateAnnotation(){return this.match(c.colon)?this.tsParseTypeOrTypePredicateAnnotation(c.colon):void 0}tsTryParseTypeAnnotation(){return this.match(c.colon)?this.tsParseTypeAnnotation():void 0}tsTryParseType(){return this.tsEatThenParseType(c.colon)}tsParseTypePredicatePrefix(){const e=this.parseIdentifier();if(this.isContextual("is")&&!this.hasPrecedingLineBreak())return this.next(),e}tsParseTypePredicateAsserts(){if(!this.match(c.name)||"asserts"!==this.state.value||this.hasPrecedingLineBreak())return!1;const e=this.state.containsEsc;return this.next(),!(!this.match(c.name)&&!this.match(c._this)||(e&&this.raise(this.state.lastTokStart,E.InvalidEscapedReservedWord,"asserts"),0))}tsParseTypeAnnotation(e=!0,t=this.startNode()){return this.tsInType((()=>{e&&this.expect(c.colon),t.typeAnnotation=this.tsParseType()})),this.finishNode(t,"TSTypeAnnotation")}tsParseType(){he(this.state.inType);const e=this.tsParseNonConditionalType();if(this.hasPrecedingLineBreak()||!this.eat(c._extends))return e;const t=this.startNodeAtNode(e);return t.checkType=e,t.extendsType=this.tsParseNonConditionalType(),this.expect(c.question),t.trueType=this.tsParseType(),this.expect(c.colon),t.falseType=this.tsParseType(),this.finishNode(t,"TSConditionalType")}isAbstractConstructorSignature(){return this.isContextual("abstract")&&this.lookahead().type===c._new}tsParseNonConditionalType(){return this.tsIsStartOfFunctionType()?this.tsParseFunctionOrConstructorType("TSFunctionType"):this.match(c._new)?this.tsParseFunctionOrConstructorType("TSConstructorType"):this.isAbstractConstructorSignature()?this.tsParseFunctionOrConstructorType("TSConstructorType",!0):this.tsParseUnionTypeOrHigher()}tsParseTypeAssertion(){const e=this.startNode(),t=this.tsTryNextParseConstantContext();return e.typeAnnotation=t||this.tsNextThenParseType(),this.expectRelational(">"),e.expression=this.parseMaybeUnary(),this.finishNode(e,"TSTypeAssertion")}tsParseHeritageClause(e){const t=this.state.start,r=this.tsParseDelimitedList("HeritageClauseElement",this.tsParseExpressionWithTypeArguments.bind(this));return r.length||this.raise(t,fe.EmptyHeritageClauseType,e),r}tsParseExpressionWithTypeArguments(){const e=this.startNode();return e.expression=this.tsParseEntityName(!1),this.isRelational("<")&&(e.typeParameters=this.tsParseTypeArguments()),this.finishNode(e,"TSExpressionWithTypeArguments")}tsParseInterfaceDeclaration(e){e.id=this.parseIdentifier(),this.checkLVal(e.id,"typescript interface declaration",130),e.typeParameters=this.tsTryParseTypeParameters(),this.eat(c._extends)&&(e.extends=this.tsParseHeritageClause("extends"));const t=this.startNode();return t.body=this.tsInType(this.tsParseObjectTypeMembers.bind(this)),e.body=this.finishNode(t,"TSInterfaceBody"),this.finishNode(e,"TSInterfaceDeclaration")}tsParseTypeAliasDeclaration(e){return e.id=this.parseIdentifier(),this.checkLVal(e.id,"typescript type alias",2),e.typeParameters=this.tsTryParseTypeParameters(),e.typeAnnotation=this.tsInType((()=>{if(this.expect(c.eq),this.isContextual("intrinsic")&&this.lookahead().type!==c.dot){const e=this.startNode();return this.next(),this.finishNode(e,"TSIntrinsicKeyword")}return this.tsParseType()})),this.semicolon(),this.finishNode(e,"TSTypeAliasDeclaration")}tsInNoContext(e){const t=this.state.context;this.state.context=[t[0]];try{return e()}finally{this.state.context=t}}tsInType(e){const t=this.state.inType;this.state.inType=!0;try{return e()}finally{this.state.inType=t}}tsEatThenParseType(e){return this.match(e)?this.tsNextThenParseType():void 0}tsExpectThenParseType(e){return this.tsDoThenParseType((()=>this.expect(e)))}tsNextThenParseType(){return this.tsDoThenParseType((()=>this.next()))}tsDoThenParseType(e){return this.tsInType((()=>(e(),this.tsParseType())))}tsParseEnumMember(){const e=this.startNode();return e.id=this.match(c.string)?this.parseExprAtom():this.parseIdentifier(!0),this.eat(c.eq)&&(e.initializer=this.parseMaybeAssignAllowIn()),this.finishNode(e,"TSEnumMember")}tsParseEnumDeclaration(e,t){return t&&(e.const=!0),e.id=this.parseIdentifier(),this.checkLVal(e.id,"typescript enum declaration",t?779:267),this.expect(c.braceL),e.members=this.tsParseDelimitedList("EnumMembers",this.tsParseEnumMember.bind(this)),this.expect(c.braceR),this.finishNode(e,"TSEnumDeclaration")}tsParseModuleBlock(){const e=this.startNode();return this.scope.enter(0),this.expect(c.braceL),this.parseBlockOrModuleBlockBody(e.body=[],void 0,!0,c.braceR),this.scope.exit(),this.finishNode(e,"TSModuleBlock")}tsParseModuleOrNamespaceDeclaration(e,t=!1){if(e.id=this.parseIdentifier(),t||this.checkLVal(e.id,"module or namespace declaration",1024),this.eat(c.dot)){const t=this.startNode();this.tsParseModuleOrNamespaceDeclaration(t,!0),e.body=t}else this.scope.enter(256),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit();return this.finishNode(e,"TSModuleDeclaration")}tsParseAmbientExternalModuleDeclaration(e){return this.isContextual("global")?(e.global=!0,e.id=this.parseIdentifier()):this.match(c.string)?e.id=this.parseExprAtom():this.unexpected(),this.match(c.braceL)?(this.scope.enter(256),this.prodParam.enter(0),e.body=this.tsParseModuleBlock(),this.prodParam.exit(),this.scope.exit()):this.semicolon(),this.finishNode(e,"TSModuleDeclaration")}tsParseImportEqualsDeclaration(e,t){e.isExport=t||!1,e.id=this.parseIdentifier(),this.checkLVal(e.id,"import equals declaration",9),this.expect(c.eq);const r=this.tsParseModuleReference();return"type"===e.importKind&&"TSExternalModuleReference"!==r.type&&this.raise(r.start,fe.ImportAliasHasImportType),e.moduleReference=r,this.semicolon(),this.finishNode(e,"TSImportEqualsDeclaration")}tsIsExternalModuleReference(){return this.isContextual("require")&&40===this.lookaheadCharCode()}tsParseModuleReference(){return this.tsIsExternalModuleReference()?this.tsParseExternalModuleReference():this.tsParseEntityName(!1)}tsParseExternalModuleReference(){const e=this.startNode();if(this.expectContextual("require"),this.expect(c.parenL),!this.match(c.string))throw this.unexpected();return e.expression=this.parseExprAtom(),this.expect(c.parenR),this.finishNode(e,"TSExternalModuleReference")}tsLookAhead(e){const t=this.state.clone(),r=e();return this.state=t,r}tsTryParseAndCatch(e){const t=this.tryParse((t=>e()||t()));if(!t.aborted&&t.node)return t.error&&(this.state=t.failState),t.node}tsTryParse(e){const t=this.state.clone(),r=e();return void 0!==r&&!1!==r?r:void(this.state=t)}tsTryParseDeclare(e){if(this.isLineTerminator())return;let t,r=this.state.type;return this.isContextual("let")&&(r=c._var,t="let"),this.tsInAmbientContext((()=>{switch(r){case c._function:return e.declare=!0,this.parseFunctionStatement(e,!1,!0);case c._class:return e.declare=!0,this.parseClass(e,!0,!1);case c._const:if(this.match(c._const)&&this.isLookaheadContextual("enum"))return this.expect(c._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(e,!0);case c._var:return t=t||this.state.value,this.parseVarStatement(e,t);case c.name:{const t=this.state.value;return"global"===t?this.tsParseAmbientExternalModuleDeclaration(e):this.tsParseDeclaration(e,t,!0)}}}))}tsTryParseExportDeclaration(){return this.tsParseDeclaration(this.startNode(),this.state.value,!0)}tsParseExpressionStatement(e,t){switch(t.name){case"declare":{const t=this.tsTryParseDeclare(e);if(t)return t.declare=!0,t;break}case"global":if(this.match(c.braceL)){this.scope.enter(256),this.prodParam.enter(0);const r=e;return r.global=!0,r.id=t,r.body=this.tsParseModuleBlock(),this.scope.exit(),this.prodParam.exit(),this.finishNode(r,"TSModuleDeclaration")}break;default:return this.tsParseDeclaration(e,t.name,!1)}}tsParseDeclaration(e,t,r){switch(t){case"abstract":if(this.tsCheckLineTerminator(r)&&(this.match(c._class)||this.match(c.name)))return this.tsParseAbstractDeclaration(e);break;case"enum":if(r||this.match(c.name))return r&&this.next(),this.tsParseEnumDeclaration(e,!1);break;case"interface":if(this.tsCheckLineTerminator(r)&&this.match(c.name))return this.tsParseInterfaceDeclaration(e);break;case"module":if(this.tsCheckLineTerminator(r)){if(this.match(c.string))return this.tsParseAmbientExternalModuleDeclaration(e);if(this.match(c.name))return this.tsParseModuleOrNamespaceDeclaration(e)}break;case"namespace":if(this.tsCheckLineTerminator(r)&&this.match(c.name))return this.tsParseModuleOrNamespaceDeclaration(e);break;case"type":if(this.tsCheckLineTerminator(r)&&this.match(c.name))return this.tsParseTypeAliasDeclaration(e)}}tsCheckLineTerminator(e){return e?!this.hasFollowingLineBreak()&&(this.next(),!0):!this.isLineTerminator()}tsTryParseGenericAsyncArrowFunction(e,t){if(!this.isRelational("<"))return;const r=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0;const n=this.tsTryParseAndCatch((()=>{const r=this.startNodeAt(e,t);return r.typeParameters=this.tsParseTypeParameters(),super.parseFunctionParams(r),r.returnType=this.tsTryParseTypeOrTypePredicateAnnotation(),this.expect(c.arrow),r}));return this.state.maybeInArrowParameters=r,n?this.parseArrowExpression(n,null,!0):void 0}tsParseTypeArguments(){const e=this.startNode();return e.params=this.tsInType((()=>this.tsInNoContext((()=>(this.expectRelational("<"),this.tsParseDelimitedList("TypeParametersOrArguments",this.tsParseType.bind(this))))))),0===e.params.length&&this.raise(e.start,fe.EmptyTypeArguments),this.state.exprAllowed=!1,this.expectRelational(">"),this.finishNode(e,"TSTypeParameterInstantiation")}tsIsDeclarationStart(){if(this.match(c.name))switch(this.state.value){case"abstract":case"declare":case"enum":case"interface":case"module":case"namespace":case"type":return!0}return!1}isExportDefaultSpecifier(){return!this.tsIsDeclarationStart()&&super.isExportDefaultSpecifier()}parseAssignableListItem(e,t){const r=this.state.start,n=this.state.startLoc;let i,s=!1;void 0!==e&&(i=this.parseAccessModifier(),s=!!this.tsParseModifier(["readonly"]),!1===e&&(i||s)&&this.raise(r,fe.UnexpectedParameterModifier));const a=this.parseMaybeDefault();this.parseAssignableListItemTypes(a);const o=this.parseMaybeDefault(a.start,a.loc.start,a);if(i||s){const e=this.startNodeAt(r,n);return t.length&&(e.decorators=t),i&&(e.accessibility=i),s&&(e.readonly=s),"Identifier"!==o.type&&"AssignmentPattern"!==o.type&&this.raise(e.start,fe.UnsupportedParameterPropertyKind),e.parameter=o,this.finishNode(e,"TSParameterProperty")}return t.length&&(a.decorators=t),o}parseFunctionBodyAndFinish(e,t,r=!1){this.match(c.colon)&&(e.returnType=this.tsParseTypeOrTypePredicateAnnotation(c.colon));const n="FunctionDeclaration"===t?"TSDeclareFunction":"ClassMethod"===t?"TSDeclareMethod":void 0;n&&!this.match(c.braceL)&&this.isLineTerminator()?this.finishNode(e,n):"TSDeclareFunction"===n&&this.state.isAmbientContext&&(this.raise(e.start,fe.DeclareFunctionHasImplementation),e.declare)?super.parseFunctionBodyAndFinish(e,n,r):super.parseFunctionBodyAndFinish(e,t,r)}registerFunctionStatementId(e){!e.body&&e.id?this.checkLVal(e.id,"function name",1024):super.registerFunctionStatementId(...arguments)}tsCheckForInvalidTypeCasts(e){e.forEach((e=>{"TSTypeCastExpression"===(null==e?void 0:e.type)&&this.raise(e.typeAnnotation.start,fe.UnexpectedTypeAnnotation)}))}toReferencedList(e,t){return this.tsCheckForInvalidTypeCasts(e),e}parseArrayLike(...e){const t=super.parseArrayLike(...e);return"ArrayExpression"===t.type&&this.tsCheckForInvalidTypeCasts(t.elements),t}parseSubscript(e,t,r,n,i){if(!this.hasPrecedingLineBreak()&&this.match(c.bang)){this.state.exprAllowed=!1,this.next();const n=this.startNodeAt(t,r);return n.expression=e,this.finishNode(n,"TSNonNullExpression")}if(this.isRelational("<")){const s=this.tsTryParseAndCatch((()=>{if(!n&&this.atPossibleAsyncArrow(e)){const e=this.tsTryParseGenericAsyncArrowFunction(t,r);if(e)return e}const s=this.startNodeAt(t,r);s.callee=e;const a=this.tsParseTypeArguments();if(a){if(!n&&this.eat(c.parenL))return s.arguments=this.parseCallExpressionArguments(c.parenR,!1),this.tsCheckForInvalidTypeCasts(s.arguments),s.typeParameters=a,i.optionalChainMember&&(s.optional=!1),this.finishCallExpression(s,i.optionalChainMember);if(this.match(c.backQuote)){const n=this.parseTaggedTemplateExpression(e,t,r,i);return n.typeParameters=a,n}}this.unexpected()}));if(s)return s}return super.parseSubscript(e,t,r,n,i)}parseNewArguments(e){if(this.isRelational("<")){const t=this.tsTryParseAndCatch((()=>{const e=this.tsParseTypeArguments();return this.match(c.parenL)||this.unexpected(),e}));t&&(e.typeParameters=t)}super.parseNewArguments(e)}parseExprOp(e,t,r,n){if(de(c._in.binop)>n&&!this.hasPrecedingLineBreak()&&this.isContextual("as")){const i=this.startNodeAt(t,r);i.expression=e;const s=this.tsTryNextParseConstantContext();return i.typeAnnotation=s||this.tsNextThenParseType(),this.finishNode(i,"TSAsExpression"),this.reScan_lt_gt(),this.parseExprOp(i,t,r,n)}return super.parseExprOp(e,t,r,n)}checkReservedWord(e,t,r,n){}checkDuplicateExports(){}parseImport(e){if(e.importKind="value",this.match(c.name)||this.match(c.star)||this.match(c.braceL)){let t=this.lookahead();if(!this.isContextual("type")||t.type===c.comma||t.type===c.name&&"from"===t.value||t.type===c.eq||(e.importKind="type",this.next(),t=this.lookahead()),this.match(c.name)&&t.type===c.eq)return this.tsParseImportEqualsDeclaration(e)}const t=super.parseImport(e);return"type"===t.importKind&&t.specifiers.length>1&&"ImportDefaultSpecifier"===t.specifiers[0].type&&this.raise(t.start,fe.TypeImportCannotSpecifyDefaultAndNamed),t}parseExport(e){if(this.match(c._import))return this.next(),this.isContextual("type")&&61!==this.lookaheadCharCode()?(e.importKind="type",this.next()):e.importKind="value",this.tsParseImportEqualsDeclaration(e,!0);if(this.eat(c.eq)){const t=e;return t.expression=this.parseExpression(),this.semicolon(),this.finishNode(t,"TSExportAssignment")}if(this.eatContextual("as")){const t=e;return this.expectContextual("namespace"),t.id=this.parseIdentifier(),this.semicolon(),this.finishNode(t,"TSNamespaceExportDeclaration")}return this.isContextual("type")&&this.lookahead().type===c.braceL?(this.next(),e.exportKind="type"):e.exportKind="value",super.parseExport(e)}isAbstractClass(){return this.isContextual("abstract")&&this.lookahead().type===c._class}parseExportDefaultExpression(){if(this.isAbstractClass()){const e=this.startNode();return this.next(),e.abstract=!0,this.parseClass(e,!0,!0),e}if("interface"===this.state.value){const e=this.tsParseDeclaration(this.startNode(),this.state.value,!0);if(e)return e}return super.parseExportDefaultExpression()}parseStatementContent(e,t){if(this.state.type===c._const){const e=this.lookahead();if(e.type===c.name&&"enum"===e.value){const e=this.startNode();return this.expect(c._const),this.expectContextual("enum"),this.tsParseEnumDeclaration(e,!0)}}return super.parseStatementContent(e,t)}parseAccessModifier(){return this.tsParseModifier(["public","protected","private"])}tsHasSomeModifiers(e,t){return t.some((t=>me(t)?e.accessibility===t:!!e[t]))}parseClassMember(e,t,r){const n=["declare","private","public","protected","override","abstract","readonly"];this.tsParseModifiers(t,n.concat(["static"]));const i=()=>{const i=!!t.static;i&&this.eat(c.braceL)?(this.tsHasSomeModifiers(t,n)&&this.raise(this.state.pos,fe.StaticBlockCannotHaveModifier),this.parseClassStaticBlock(e,t)):this.parseClassMemberWithIsStatic(e,t,r,i)};t.declare?this.tsInAmbientContext(i):i()}parseClassMemberWithIsStatic(e,t,r,n){const i=this.tsTryParseIndexSignature(t);if(i)return e.body.push(i),t.abstract&&this.raise(t.start,fe.IndexSignatureHasAbstract),t.accessibility&&this.raise(t.start,fe.IndexSignatureHasAccessibility,t.accessibility),t.declare&&this.raise(t.start,fe.IndexSignatureHasDeclare),void(t.override&&this.raise(t.start,fe.IndexSignatureHasOverride));!this.state.inAbstractClass&&t.abstract&&this.raise(t.start,fe.NonAbstractClassHasAbstractMethod),t.override&&(r.hadSuperClass||this.raise(t.start,fe.OverrideNotInSubClass)),super.parseClassMemberWithIsStatic(e,t,r,n)}parsePostMemberNameModifiers(e){this.eat(c.question)&&(e.optional=!0),e.readonly&&this.match(c.parenL)&&this.raise(e.start,fe.ClassMethodHasReadonly),e.declare&&this.match(c.parenL)&&this.raise(e.start,fe.ClassMethodHasDeclare)}parseExpressionStatement(e,t){return("Identifier"===t.type?this.tsParseExpressionStatement(e,t):void 0)||super.parseExpressionStatement(e,t)}shouldParseExportDeclaration(){return!!this.tsIsDeclarationStart()||super.shouldParseExportDeclaration()}parseConditional(e,t,r,n){if(!n||!this.match(c.question))return super.parseConditional(e,t,r,n);const i=this.tryParse((()=>super.parseConditional(e,t,r)));return i.node?(i.error&&(this.state=i.failState),i.node):(n.start=i.error.pos||this.state.start,e)}parseParenItem(e,t,r){if(e=super.parseParenItem(e,t,r),this.eat(c.question)&&(e.optional=!0,this.resetEndLocation(e)),this.match(c.colon)){const n=this.startNodeAt(t,r);return n.expression=e,n.typeAnnotation=this.tsParseTypeAnnotation(),this.finishNode(n,"TSTypeCastExpression")}return e}parseExportDeclaration(e){const t=this.state.start,r=this.state.startLoc,n=this.eatContextual("declare");if(n&&(this.isContextual("declare")||!this.shouldParseExportDeclaration()))throw this.raise(this.state.start,fe.ExpectedAmbientAfterExportDeclare);let i;return this.match(c.name)&&(i=this.tsTryParseExportDeclaration()),i||(i=super.parseExportDeclaration(e)),i&&("TSInterfaceDeclaration"===i.type||"TSTypeAliasDeclaration"===i.type||n)&&(e.exportKind="type"),i&&n&&(this.resetStartLocation(i,t,r),i.declare=!0),i}parseClassId(e,t,r){if((!t||r)&&this.isContextual("implements"))return;super.parseClassId(e,t,r,e.declare?1024:139);const n=this.tsTryParseTypeParameters();n&&(e.typeParameters=n)}parseClassPropertyAnnotation(e){!e.optional&&this.eat(c.bang)&&(e.definite=!0);const t=this.tsTryParseTypeAnnotation();t&&(e.typeAnnotation=t)}parseClassProperty(e){return this.parseClassPropertyAnnotation(e),this.state.isAmbientContext&&this.match(c.eq)&&this.raise(this.state.start,fe.DeclareClassFieldHasInitializer),super.parseClassProperty(e)}parseClassPrivateProperty(e){return e.abstract&&this.raise(e.start,fe.PrivateElementHasAbstract),e.accessibility&&this.raise(e.start,fe.PrivateElementHasAccessibility,e.accessibility),this.parseClassPropertyAnnotation(e),super.parseClassPrivateProperty(e)}pushClassMethod(e,t,r,n,i,s){const a=this.tsTryParseTypeParameters();a&&i&&this.raise(a.start,fe.ConstructorHasTypeParameters),!t.declare||"get"!==t.kind&&"set"!==t.kind||this.raise(t.start,fe.DeclareAccessor,t.kind),a&&(t.typeParameters=a),super.pushClassMethod(e,t,r,n,i,s)}pushClassPrivateMethod(e,t,r,n){const i=this.tsTryParseTypeParameters();i&&(t.typeParameters=i),super.pushClassPrivateMethod(e,t,r,n)}parseClassSuper(e){super.parseClassSuper(e),e.superClass&&this.isRelational("<")&&(e.superTypeParameters=this.tsParseTypeArguments()),this.eatContextual("implements")&&(e.implements=this.tsParseHeritageClause("implements"))}parseObjPropValue(e,...t){const r=this.tsTryParseTypeParameters();r&&(e.typeParameters=r),super.parseObjPropValue(e,...t)}parseFunctionParams(e,t){const r=this.tsTryParseTypeParameters();r&&(e.typeParameters=r),super.parseFunctionParams(e,t)}parseVarId(e,t){super.parseVarId(e,t),"Identifier"===e.id.type&&this.eat(c.bang)&&(e.definite=!0);const r=this.tsTryParseTypeAnnotation();r&&(e.id.typeAnnotation=r,this.resetEndLocation(e.id))}parseAsyncArrowFromCallExpression(e,t){return this.match(c.colon)&&(e.returnType=this.tsParseTypeAnnotation()),super.parseAsyncArrowFromCallExpression(e,t)}parseMaybeAssign(...e){var t,r,n,i,s,a,o;let l,p,u,d;if(this.hasPlugin("jsx")&&(this.match(c.jsxTagStart)||this.isRelational("<"))){if(l=this.state.clone(),p=this.tryParse((()=>super.parseMaybeAssign(...e)),l),!p.error)return p.node;const{context:t}=this.state;t[t.length-1]===O.j_oTag?t.length-=2:t[t.length-1]===O.j_expr&&(t.length-=1)}if(!(null!=(t=p)&&t.error||this.isRelational("<")))return super.parseMaybeAssign(...e);l=l||this.state.clone();const h=this.tryParse((t=>{var r,n;d=this.tsParseTypeParameters();const i=super.parseMaybeAssign(...e);return("ArrowFunctionExpression"!==i.type||null!=(r=i.extra)&&r.parenthesized)&&t(),0!==(null==(n=d)?void 0:n.params.length)&&this.resetStartLocationFromNode(i,d),i.typeParameters=d,i}),l);if(!h.error&&!h.aborted)return h.node;if(!p&&(he(!this.hasPlugin("jsx")),u=this.tryParse((()=>super.parseMaybeAssign(...e)),l),!u.error))return u.node;if(null!=(r=p)&&r.node)return this.state=p.failState,p.node;if(h.node)return this.state=h.failState,h.node;if(null!=(n=u)&&n.node)return this.state=u.failState,u.node;if(null!=(i=p)&&i.thrown)throw p.error;if(h.thrown)throw h.error;if(null!=(s=u)&&s.thrown)throw u.error;throw(null==(a=p)?void 0:a.error)||h.error||(null==(o=u)?void 0:o.error)}parseMaybeUnary(e){return!this.hasPlugin("jsx")&&this.isRelational("<")?this.tsParseTypeAssertion():super.parseMaybeUnary(e)}parseArrow(e){if(this.match(c.colon)){const t=this.tryParse((e=>{const t=this.tsParseTypeOrTypePredicateAnnotation(c.colon);return!this.canInsertSemicolon()&&this.match(c.arrow)||e(),t}));if(t.aborted)return;t.thrown||(t.error&&(this.state=t.failState),e.returnType=t.node)}return super.parseArrow(e)}parseAssignableListItemTypes(e){this.eat(c.question)&&("Identifier"===e.type||this.state.isAmbientContext||this.state.inType||this.raise(e.start,fe.PatternIsOptional),e.optional=!0);const t=this.tsTryParseTypeAnnotation();return t&&(e.typeAnnotation=t),this.resetEndLocation(e),e}toAssignable(e,t=!1){switch(e.type){case"TSTypeCastExpression":return super.toAssignable(this.typeCastToParameter(e),t);case"TSParameterProperty":default:return super.toAssignable(e,t);case"ParenthesizedExpression":return this.toAssignableParenthesizedExpression(e,t);case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":return e.expression=this.toAssignable(e.expression,t),e}}toAssignableParenthesizedExpression(e,t){switch(e.expression.type){case"TSAsExpression":case"TSNonNullExpression":case"TSTypeAssertion":case"ParenthesizedExpression":return e.expression=this.toAssignable(e.expression,t),e;default:return super.toAssignable(e,t)}}checkLVal(e,t,...r){var n;switch(e.type){case"TSTypeCastExpression":return;case"TSParameterProperty":return void this.checkLVal(e.parameter,"parameter property",...r);case"TSAsExpression":case"TSTypeAssertion":if(!(r[0]||"parenthesized expression"===t||null!=(n=e.extra)&&n.parenthesized)){this.raise(e.start,E.InvalidLhs,t);break}return void this.checkLVal(e.expression,"parenthesized expression",...r);case"TSNonNullExpression":return void this.checkLVal(e.expression,t,...r);default:return void super.checkLVal(e,t,...r)}}parseBindingAtom(){return this.state.type===c._this?this.parseIdentifier(!0):super.parseBindingAtom()}parseMaybeDecoratorArguments(e){if(this.isRelational("<")){const t=this.tsParseTypeArguments();if(this.match(c.parenL)){const r=super.parseMaybeDecoratorArguments(e);return r.typeParameters=t,r}this.unexpected(this.state.start,c.parenL)}return super.parseMaybeDecoratorArguments(e)}checkCommaAfterRest(e){this.state.isAmbientContext&&this.match(c.comma)&&this.lookaheadCharCode()===e?this.next():super.checkCommaAfterRest(e)}isClassMethod(){return this.isRelational("<")||super.isClassMethod()}isClassProperty(){return this.match(c.bang)||this.match(c.colon)||super.isClassProperty()}parseMaybeDefault(...e){const t=super.parseMaybeDefault(...e);return"AssignmentPattern"===t.type&&t.typeAnnotation&&t.right.start<t.typeAnnotation.start&&this.raise(t.typeAnnotation.start,fe.TypeAnnotationAfterAssign),t}getTokenFromCode(e){return!this.state.inType||62!==e&&60!==e?super.getTokenFromCode(e):this.finishOp(c.relational,1)}reScan_lt_gt(){if(this.match(c.relational)){const e=this.input.charCodeAt(this.state.start);60!==e&&62!==e||(this.state.pos-=1,this.readToken_lt_gt(e))}}toAssignableList(e){for(let t=0;t<e.length;t++){const r=e[t];if(r)switch(r.type){case"TSTypeCastExpression":e[t]=this.typeCastToParameter(r);break;case"TSAsExpression":case"TSTypeAssertion":this.state.maybeInArrowParameters?this.raise(r.start,fe.UnexpectedTypeCastInParameter):e[t]=this.typeCastToParameter(r)}}return super.toAssignableList(...arguments)}typeCastToParameter(e){return e.expression.typeAnnotation=e.typeAnnotation,this.resetEndLocation(e.expression,e.typeAnnotation.end,e.typeAnnotation.loc.end),e.expression}shouldParseArrow(){return this.match(c.colon)||super.shouldParseArrow()}shouldParseAsyncArrow(){return this.match(c.colon)||super.shouldParseAsyncArrow()}canHaveLeadingDecorator(){return super.canHaveLeadingDecorator()||this.isAbstractClass()}jsxParseOpeningElementAfterName(e){if(this.isRelational("<")){const t=this.tsTryParseAndCatch((()=>this.tsParseTypeArguments()));t&&(e.typeParameters=t)}return super.jsxParseOpeningElementAfterName(e)}getGetterSetterExpectedParamCount(e){const t=super.getGetterSetterExpectedParamCount(e),r=this.getObjectOrClassMethodParams(e)[0];return r&&this.isThisParam(r)?t+1:t}parseCatchClauseParam(){const e=super.parseCatchClauseParam(),t=this.tsTryParseTypeAnnotation();return t&&(e.typeAnnotation=t,this.resetEndLocation(e)),e}tsInAmbientContext(e){const t=this.state.isAmbientContext;this.state.isAmbientContext=!0;try{return e()}finally{this.state.isAmbientContext=t}}parseClass(e,...t){const r=this.state.inAbstractClass;this.state.inAbstractClass=!!e.abstract;try{return super.parseClass(e,...t)}finally{this.state.inAbstractClass=r}}tsParseAbstractDeclaration(e){if(this.match(c._class))return e.abstract=!0,this.parseClass(e,!0,!1);if(this.isContextual("interface")){if(!this.hasFollowingLineBreak())return e.abstract=!0,this.raise(e.start,fe.NonClassMethodPropertyHasAbstractModifer),this.next(),this.tsParseInterfaceDeclaration(e)}else this.unexpected(null,c._class)}parseMethod(...e){const t=super.parseMethod(...e);if(t.abstract&&(this.hasPlugin("estree")?t.value.body:t.body)){const{key:e}=t;this.raise(t.start,fe.AbstractMethodHasImplementation,"Identifier"===e.type?e.name:`[${this.input.slice(e.start,e.end)}]`)}return t}shouldParseAsAmbientContext(){return!!this.getPluginOption("typescript","dts")}parse(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.parse()}getExpression(){return this.shouldParseAsAmbientContext()&&(this.state.isAmbientContext=!0),super.getExpression()}},v8intrinsic:e=>class extends e{parseV8Intrinsic(){if(this.match(c.modulo)){const e=this.state.start,t=this.startNode();if(this.eat(c.modulo),this.match(c.name)){const e=this.parseIdentifierName(this.state.start),r=this.createIdentifier(t,e);if(r.type="V8IntrinsicIdentifier",this.match(c.parenL))return r}this.unexpected(e)}}parseExprAtom(){return this.parseV8Intrinsic()||super.parseExprAtom(...arguments)}},placeholders:e=>class extends e{parsePlaceholder(e){if(this.match(c.placeholder)){const t=this.startNode();return this.next(),this.assertNoSpace("Unexpected space in placeholder."),t.name=super.parseIdentifier(!0),this.assertNoSpace("Unexpected space in placeholder."),this.expect(c.placeholder),this.finishPlaceholder(t,e)}}finishPlaceholder(e,t){const r=!(!e.expectedNode||"Placeholder"!==e.type);return e.expectedNode=t,r?e:this.finishNode(e,"Placeholder")}getTokenFromCode(e){return 37===e&&37===this.input.charCodeAt(this.state.pos+1)?this.finishOp(c.placeholder,2):super.getTokenFromCode(...arguments)}parseExprAtom(){return this.parsePlaceholder("Expression")||super.parseExprAtom(...arguments)}parseIdentifier(){return this.parsePlaceholder("Identifier")||super.parseIdentifier(...arguments)}checkReservedWord(e){void 0!==e&&super.checkReservedWord(...arguments)}parseBindingAtom(){return this.parsePlaceholder("Pattern")||super.parseBindingAtom(...arguments)}checkLVal(e){"Placeholder"!==e.type&&super.checkLVal(...arguments)}toAssignable(e){return e&&"Placeholder"===e.type&&"Expression"===e.expectedNode?(e.expectedNode="Pattern",e):super.toAssignable(...arguments)}isLet(e){return!!super.isLet(e)||!!this.isContextual("let")&&(!e&&this.lookahead().type===c.placeholder)}verifyBreakContinue(e){e.label&&"Placeholder"===e.label.type||super.verifyBreakContinue(...arguments)}parseExpressionStatement(e,t){if("Placeholder"!==t.type||t.extra&&t.extra.parenthesized)return super.parseExpressionStatement(...arguments);if(this.match(c.colon)){const r=e;return r.label=this.finishPlaceholder(t,"Identifier"),this.next(),r.body=this.parseStatement("label"),this.finishNode(r,"LabeledStatement")}return this.semicolon(),e.name=t.name,this.finishPlaceholder(e,"Statement")}parseBlock(){return this.parsePlaceholder("BlockStatement")||super.parseBlock(...arguments)}parseFunctionId(){return this.parsePlaceholder("Identifier")||super.parseFunctionId(...arguments)}parseClass(e,t,r){const n=t?"ClassDeclaration":"ClassExpression";this.next(),this.takeDecorators(e);const i=this.state.strict,s=this.parsePlaceholder("Identifier");if(s)if(this.match(c._extends)||this.match(c.placeholder)||this.match(c.braceL))e.id=s;else{if(r||!t)return e.id=null,e.body=this.finishPlaceholder(s,"ClassBody"),this.finishNode(e,n);this.unexpected(null,ye.ClassNameIsRequired)}else this.parseClassId(e,t,r);return this.parseClassSuper(e),e.body=this.parsePlaceholder("ClassBody")||this.parseClassBody(!!e.superClass,i),this.finishNode(e,n)}parseExport(e){const t=this.parsePlaceholder("Identifier");if(!t)return super.parseExport(...arguments);if(!this.isContextual("from")&&!this.match(c.comma))return e.specifiers=[],e.source=null,e.declaration=this.finishPlaceholder(t,"Declaration"),this.finishNode(e,"ExportNamedDeclaration");this.expectPlugin("exportDefaultFrom");const r=this.startNode();return r.exported=t,e.specifiers=[this.finishNode(r,"ExportDefaultSpecifier")],super.parseExport(e)}isExportDefaultSpecifier(){if(this.match(c._default)){const e=this.nextTokenStart();if(this.isUnparsedContextual(e,"from")&&this.input.startsWith(c.placeholder.label,this.nextTokenStartSince(e+4)))return!0}return super.isExportDefaultSpecifier()}maybeParseExportDefaultSpecifier(e){return!!(e.specifiers&&e.specifiers.length>0)||super.maybeParseExportDefaultSpecifier(...arguments)}checkExport(e){const{specifiers:t}=e;null!=t&&t.length&&(e.specifiers=t.filter((e=>"Placeholder"===e.exported.type))),super.checkExport(e),e.specifiers=t}parseImport(e){const t=this.parsePlaceholder("Identifier");if(!t)return super.parseImport(...arguments);if(e.specifiers=[],!this.isContextual("from")&&!this.match(c.comma))return e.source=this.finishPlaceholder(t,"StringLiteral"),this.semicolon(),this.finishNode(e,"ImportDeclaration");const r=this.startNodeAtNode(t);return r.local=t,this.finishNode(r,"ImportDefaultSpecifier"),e.specifiers.push(r),this.eat(c.comma)&&(this.maybeParseStarImportSpecifier(e)||this.parseNamedImportSpecifiers(e)),this.expectContextual("from"),e.source=this.parseImportSource(),this.semicolon(),this.finishNode(e,"ImportDeclaration")}parseImportSource(){return this.parsePlaceholder("StringLiteral")||super.parseImportSource(...arguments)}}},Ee=Object.keys(xe),Pe={sourceType:"script",sourceFilename:void 0,startLine:1,allowAwaitOutsideFunction:!1,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowSuperOutsideMethod:!1,allowUndeclaredExports:!1,plugins:[],strictMode:null,ranges:!1,tokens:!1,createParenthesizedExpressions:!1,errorRecovery:!1};class ve{constructor(){this.strict=void 0,this.curLine=void 0,this.startLoc=void 0,this.endLoc=void 0,this.errors=[],this.potentialArrowAt=-1,this.noArrowAt=[],this.noArrowParamsConversionAt=[],this.maybeInArrowParameters=!1,this.inPipeline=!1,this.inType=!1,this.noAnonFunctionType=!1,this.inPropertyName=!1,this.hasFlowComment=!1,this.isAmbientContext=!1,this.inAbstractClass=!1,this.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null},this.soloAwait=!1,this.inFSharpPipelineDirectBody=!1,this.labels=[],this.decoratorStack=[[]],this.comments=[],this.trailingComments=[],this.leadingComments=[],this.commentStack=[],this.commentPreviousNode=null,this.pos=0,this.lineStart=0,this.type=c.eof,this.value=null,this.start=0,this.end=0,this.lastTokEndLoc=null,this.lastTokStartLoc=null,this.lastTokStart=0,this.lastTokEnd=0,this.context=[O.braceStatement],this.exprAllowed=!0,this.containsEsc=!1,this.strictErrors=new Map,this.exportedIdentifiers=[],this.tokensLength=0}init(e){this.strict=!1!==e.strictMode&&"module"===e.sourceType,this.curLine=e.startLine,this.startLoc=this.endLoc=this.curPosition()}curPosition(){return new y(this.curLine,this.pos-this.lineStart)}clone(e){const t=new ve,r=Object.keys(this);for(let n=0,i=r.length;n<i;n++){const i=r[n];let s=this[i];!e&&Array.isArray(s)&&(s=s.slice()),t[i]=s}return t}}var Ae=function(e){return e>=48&&e<=57};const we=new Set(["g","m","s","i","y","u"]),Oe={decBinOct:[46,66,69,79,95,98,101,111],hex:[46,88,95,120]},Ie={bin:[48,49]};Ie.oct=[...Ie.bin,50,51,52,53,54,55],Ie.dec=[...Ie.oct,56,57],Ie.hex=[...Ie.dec,65,66,67,68,69,70,97,98,99,100,101,102];class Ne{constructor(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,this.loc=new T(e.startLoc,e.endLoc)}}class Ce extends A{constructor(e,t){super(),this.isLookahead=void 0,this.tokens=[],this.state=new ve,this.state.init(e),this.input=t,this.length=t.length,this.isLookahead=!1}pushToken(e){this.tokens.length=this.state.tokensLength,this.tokens.push(e),++this.state.tokensLength}next(){this.isLookahead||(this.checkKeywordEscapes(),this.options.tokens&&this.pushToken(new Ne(this.state))),this.state.lastTokEnd=this.state.end,this.state.lastTokStart=this.state.start,this.state.lastTokEndLoc=this.state.endLoc,this.state.lastTokStartLoc=this.state.startLoc,this.nextToken()}eat(e){return!!this.match(e)&&(this.next(),!0)}match(e){return this.state.type===e}lookahead(){const e=this.state;this.state=e.clone(!0),this.isLookahead=!0,this.next(),this.isLookahead=!1;const t=this.state;return this.state=e,t}nextTokenStart(){return this.nextTokenStartSince(this.state.pos)}nextTokenStartSince(e){return f.lastIndex=e,e+f.exec(this.input)[0].length}lookaheadCharCode(){return this.input.charCodeAt(this.nextTokenStart())}codePointAtPos(e){let t=this.input.charCodeAt(e);if(55296==(64512&t)&&++e<this.input.length){const r=this.input.charCodeAt(e);56320==(64512&r)&&(t=65536+((1023&t)<<10)+(1023&r))}return t}setStrict(e){this.state.strict=e,e&&(this.state.strictErrors.forEach(((e,t)=>this.raise(t,e))),this.state.strictErrors.clear())}curContext(){return this.state.context[this.state.context.length-1]}nextToken(){const e=this.curContext();if(null!=e&&e.preserveSpace||this.skipSpace(),this.state.start=this.state.pos,this.state.startLoc=this.state.curPosition(),this.state.pos>=this.length)return void this.finishToken(c.eof);const t=null==e?void 0:e.override;t?t(this):this.getTokenFromCode(this.codePointAtPos(this.state.pos))}pushComment(e,t,r,n,i,s){const a={type:e?"CommentBlock":"CommentLine",value:t,start:r,end:n,loc:new T(i,s)};this.options.tokens&&this.pushToken(a),this.state.comments.push(a),this.addComment(a)}skipBlockComment(){const e=this.state.curPosition(),t=this.state.pos,r=this.input.indexOf("*/",this.state.pos+2);if(-1===r)throw this.raise(t,E.UnterminatedComment);let n;for(this.state.pos=r+2,d.lastIndex=t;(n=d.exec(this.input))&&n.index<this.state.pos;)++this.state.curLine,this.state.lineStart=n.index+n[0].length;this.isLookahead||this.pushComment(!0,this.input.slice(t+2,r),t,this.state.pos,e,this.state.curPosition())}skipLineComment(e){const t=this.state.pos,r=this.state.curPosition();let n=this.input.charCodeAt(this.state.pos+=e);if(this.state.pos<this.length)for(;!h(n)&&++this.state.pos<this.length;)n=this.input.charCodeAt(this.state.pos);this.isLookahead||this.pushComment(!1,this.input.slice(t+e,this.state.pos),t,this.state.pos,r,this.state.curPosition())}skipSpace(){e:for(;this.state.pos<this.length;){const e=this.input.charCodeAt(this.state.pos);switch(e){case 32:case 160:case 9:++this.state.pos;break;case 13:10===this.input.charCodeAt(this.state.pos+1)&&++this.state.pos;case 10:case 8232:case 8233:++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;break;case 47:switch(this.input.charCodeAt(this.state.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!m(e))break e;++this.state.pos}}}finishToken(e,t){this.state.end=this.state.pos,this.state.endLoc=this.state.curPosition();const r=this.state.type;this.state.type=e,this.state.value=t,this.isLookahead||this.updateContext(r)}readToken_numberSign(){if(0===this.state.pos&&this.readToken_interpreter())return;const e=this.state.pos+1,t=this.codePointAtPos(e);if(t>=48&&t<=57)throw this.raise(this.state.pos,E.UnexpectedDigitAfterHash);if(123===t||91===t&&this.hasPlugin("recordAndTuple")){if(this.expectPlugin("recordAndTuple"),"hash"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,123===t?E.RecordExpressionHashIncorrectStartSyntaxType:E.TupleExpressionHashIncorrectStartSyntaxType);123===t?this.finishToken(c.braceHashL):this.finishToken(c.bracketHashL),this.state.pos+=2}else M(t)?(++this.state.pos,this.finishToken(c.privateName,this.readWord1(t))):92===t?(++this.state.pos,this.finishToken(c.privateName,this.readWord1())):this.finishOp(c.hash,1)}readToken_dot(){const e=this.input.charCodeAt(this.state.pos+1);e>=48&&e<=57?this.readNumber(!0):46===e&&46===this.input.charCodeAt(this.state.pos+2)?(this.state.pos+=3,this.finishToken(c.ellipsis)):(++this.state.pos,this.finishToken(c.dot))}readToken_slash(){if(this.state.exprAllowed&&!this.state.inType)return++this.state.pos,void this.readRegexp();61===this.input.charCodeAt(this.state.pos+1)?this.finishOp(c.assign,2):this.finishOp(c.slash,1)}readToken_interpreter(){if(0!==this.state.pos||this.length<2)return!1;let e=this.input.charCodeAt(this.state.pos+1);if(33!==e)return!1;const t=this.state.pos;for(this.state.pos+=1;!h(e)&&++this.state.pos<this.length;)e=this.input.charCodeAt(this.state.pos);const r=this.input.slice(t+2,this.state.pos);return this.finishToken(c.interpreterDirective,r),!0}readToken_mult_modulo(e){let t=42===e?c.star:c.modulo,r=1,n=this.input.charCodeAt(this.state.pos+1);const i=this.state.exprAllowed;42===e&&42===n&&(r++,n=this.input.charCodeAt(this.state.pos+2),t=c.exponent),61!==n||i||(r++,t=c.assign),this.finishOp(t,r)}readToken_pipe_amp(e){const t=this.input.charCodeAt(this.state.pos+1);if(t!==e){if(124===e){if(62===t)return void this.finishOp(c.pipeline,2);if(this.hasPlugin("recordAndTuple")&&125===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,E.RecordExpressionBarIncorrectEndSyntaxType);return void this.finishOp(c.braceBarR,2)}if(this.hasPlugin("recordAndTuple")&&93===t){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,E.TupleExpressionBarIncorrectEndSyntaxType);return void this.finishOp(c.bracketBarR,2)}}61!==t?this.finishOp(124===e?c.bitwiseOR:c.bitwiseAND,1):this.finishOp(c.assign,2)}else 61===this.input.charCodeAt(this.state.pos+2)?this.finishOp(c.assign,3):this.finishOp(124===e?c.logicalOR:c.logicalAND,2)}readToken_caret(){61===this.input.charCodeAt(this.state.pos+1)?this.finishOp(c.assign,2):this.finishOp(c.bitwiseXOR,1)}readToken_plus_min(e){const t=this.input.charCodeAt(this.state.pos+1);if(t===e)return 45!==t||this.inModule||62!==this.input.charCodeAt(this.state.pos+2)||0!==this.state.lastTokEnd&&!this.hasPrecedingLineBreak()?void this.finishOp(c.incDec,2):(this.skipLineComment(3),this.skipSpace(),void this.nextToken());61===t?this.finishOp(c.assign,2):this.finishOp(c.plusMin,1)}readToken_lt_gt(e){const t=this.input.charCodeAt(this.state.pos+1);let r=1;return t===e?(r=62===e&&62===this.input.charCodeAt(this.state.pos+2)?3:2,61===this.input.charCodeAt(this.state.pos+r)?void this.finishOp(c.assign,r+1):void this.finishOp(c.bitShift,r)):33!==t||60!==e||this.inModule||45!==this.input.charCodeAt(this.state.pos+2)||45!==this.input.charCodeAt(this.state.pos+3)?(61===t&&(r=2),void this.finishOp(c.relational,r)):(this.skipLineComment(4),this.skipSpace(),void this.nextToken())}readToken_eq_excl(e){const t=this.input.charCodeAt(this.state.pos+1);if(61!==t)return 61===e&&62===t?(this.state.pos+=2,void this.finishToken(c.arrow)):void this.finishOp(61===e?c.eq:c.bang,1);this.finishOp(c.equality,61===this.input.charCodeAt(this.state.pos+2)?3:2)}readToken_question(){const e=this.input.charCodeAt(this.state.pos+1),t=this.input.charCodeAt(this.state.pos+2);63===e?61===t?this.finishOp(c.assign,3):this.finishOp(c.nullishCoalescing,2):46!==e||t>=48&&t<=57?(++this.state.pos,this.finishToken(c.question)):(this.state.pos+=2,this.finishToken(c.questionDot))}getTokenFromCode(e){switch(e){case 46:return void this.readToken_dot();case 40:return++this.state.pos,void this.finishToken(c.parenL);case 41:return++this.state.pos,void this.finishToken(c.parenR);case 59:return++this.state.pos,void this.finishToken(c.semi);case 44:return++this.state.pos,void this.finishToken(c.comma);case 91:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,E.TupleExpressionBarIncorrectStartSyntaxType);this.finishToken(c.bracketBarL),this.state.pos+=2}else++this.state.pos,this.finishToken(c.bracketL);return;case 93:return++this.state.pos,void this.finishToken(c.bracketR);case 123:if(this.hasPlugin("recordAndTuple")&&124===this.input.charCodeAt(this.state.pos+1)){if("bar"!==this.getPluginOption("recordAndTuple","syntaxType"))throw this.raise(this.state.pos,E.RecordExpressionBarIncorrectStartSyntaxType);this.finishToken(c.braceBarL),this.state.pos+=2}else++this.state.pos,this.finishToken(c.braceL);return;case 125:return++this.state.pos,void this.finishToken(c.braceR);case 58:return void(this.hasPlugin("functionBind")&&58===this.input.charCodeAt(this.state.pos+1)?this.finishOp(c.doubleColon,2):(++this.state.pos,this.finishToken(c.colon)));case 63:return void this.readToken_question();case 96:return++this.state.pos,void this.finishToken(c.backQuote);case 48:{const e=this.input.charCodeAt(this.state.pos+1);if(120===e||88===e)return void this.readRadixNumber(16);if(111===e||79===e)return void this.readRadixNumber(8);if(98===e||66===e)return void this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return void this.readNumber(!1);case 34:case 39:return void this.readString(e);case 47:return void this.readToken_slash();case 37:case 42:return void this.readToken_mult_modulo(e);case 124:case 38:return void this.readToken_pipe_amp(e);case 94:return void this.readToken_caret();case 43:case 45:return void this.readToken_plus_min(e);case 60:case 62:return void this.readToken_lt_gt(e);case 61:case 33:return void this.readToken_eq_excl(e);case 126:return void this.finishOp(c.tilde,1);case 64:return++this.state.pos,void this.finishToken(c.at);case 35:return void this.readToken_numberSign();case 92:return void this.readWord();default:if(M(e))return void this.readWord(e)}throw this.raise(this.state.pos,E.InvalidOrUnexpectedToken,String.fromCodePoint(e))}finishOp(e,t){const r=this.input.slice(this.state.pos,this.state.pos+t);this.state.pos+=t,this.finishToken(e,r)}readRegexp(){const e=this.state.pos;let t,r;for(;;){if(this.state.pos>=this.length)throw this.raise(e,E.UnterminatedRegExp);const n=this.input.charAt(this.state.pos);if(u.test(n))throw this.raise(e,E.UnterminatedRegExp);if(t)t=!1;else{if("["===n)r=!0;else if("]"===n&&r)r=!1;else if("/"===n&&!r)break;t="\\"===n}++this.state.pos}const n=this.input.slice(e,this.state.pos);++this.state.pos;let i="";for(;this.state.pos<this.length;){const e=this.input[this.state.pos],t=this.codePointAtPos(this.state.pos);if(we.has(e))i.indexOf(e)>-1&&this.raise(this.state.pos+1,E.DuplicateRegExpFlags);else{if(!j(t)&&92!==t)break;this.raise(this.state.pos+1,E.MalformedRegExpFlags)}++this.state.pos,i+=e}this.finishToken(c.regexp,{pattern:n,flags:i})}readInt(e,t,r,n=!0){const i=this.state.pos,s=16===e?Oe.hex:Oe.decBinOct,a=16===e?Ie.hex:10===e?Ie.dec:8===e?Ie.oct:Ie.bin;let o=!1,l=0;for(let i=0,p=null==t?1/0:t;i<p;++i){const t=this.input.charCodeAt(this.state.pos);let p;if(95!==t){if(p=t>=97?t-97+10:t>=65?t-65+10:Ae(t)?t-48:1/0,p>=e)if(this.options.errorRecovery&&p<=9)p=0,this.raise(this.state.start+i+2,E.InvalidDigit,e);else{if(!r)break;p=0,o=!0}++this.state.pos,l=l*e+p}else{const e=this.input.charCodeAt(this.state.pos-1),t=this.input.charCodeAt(this.state.pos+1);(-1===a.indexOf(t)||s.indexOf(e)>-1||s.indexOf(t)>-1||Number.isNaN(t))&&this.raise(this.state.pos,E.UnexpectedNumericSeparator),n||this.raise(this.state.pos,E.NumericSeparatorInEscapeSequence),++this.state.pos}}return this.state.pos===i||null!=t&&this.state.pos-i!==t||o?null:l}readRadixNumber(e){const t=this.state.pos;let r=!1;this.state.pos+=2;const n=this.readInt(e);null==n&&this.raise(this.state.start+2,E.InvalidDigit,e);const i=this.input.charCodeAt(this.state.pos);if(110===i)++this.state.pos,r=!0;else if(109===i)throw this.raise(t,E.InvalidDecimal);if(M(this.codePointAtPos(this.state.pos)))throw this.raise(this.state.pos,E.NumberIdentifier);if(r){const e=this.input.slice(t,this.state.pos).replace(/[_n]/g,"");this.finishToken(c.bigint,e)}else this.finishToken(c.num,n)}readNumber(e){const t=this.state.pos;let r=!1,n=!1,i=!1,s=!1,a=!1;e||null!==this.readInt(10)||this.raise(t,E.InvalidNumber);const o=this.state.pos-t>=2&&48===this.input.charCodeAt(t);if(o){const e=this.input.slice(t,this.state.pos);if(this.recordStrictModeErrors(t,E.StrictOctalLiteral),!this.state.strict){const r=e.indexOf("_");r>0&&this.raise(r+t,E.ZeroDigitNumericSeparator)}a=o&&!/[89]/.test(e)}let l=this.input.charCodeAt(this.state.pos);if(46!==l||a||(++this.state.pos,this.readInt(10),r=!0,l=this.input.charCodeAt(this.state.pos)),69!==l&&101!==l||a||(l=this.input.charCodeAt(++this.state.pos),43!==l&&45!==l||++this.state.pos,null===this.readInt(10)&&this.raise(t,E.InvalidOrMissingExponent),r=!0,s=!0,l=this.input.charCodeAt(this.state.pos)),110===l&&((r||o)&&this.raise(t,E.InvalidBigIntLiteral),++this.state.pos,n=!0),109===l&&(this.expectPlugin("decimal",this.state.pos),(s||o)&&this.raise(t,E.InvalidDecimal),++this.state.pos,i=!0),M(this.codePointAtPos(this.state.pos)))throw this.raise(this.state.pos,E.NumberIdentifier);const p=this.input.slice(t,this.state.pos).replace(/[_mn]/g,"");if(n)return void this.finishToken(c.bigint,p);if(i)return void this.finishToken(c.decimal,p);const u=a?parseInt(p,8):parseFloat(p);this.finishToken(c.num,u)}readCodePoint(e){let t;if(123===this.input.charCodeAt(this.state.pos)){const r=++this.state.pos;if(t=this.readHexChar(this.input.indexOf("}",this.state.pos)-this.state.pos,!0,e),++this.state.pos,null!==t&&t>1114111){if(!e)return null;this.raise(r,E.InvalidCodePoint)}}else t=this.readHexChar(4,!1,e);return t}readString(e){let t="",r=++this.state.pos;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,E.UnterminatedString);const n=this.input.charCodeAt(this.state.pos);if(n===e)break;if(92===n)t+=this.input.slice(r,this.state.pos),t+=this.readEscapedChar(!1),r=this.state.pos;else if(8232===n||8233===n)++this.state.pos,++this.state.curLine,this.state.lineStart=this.state.pos;else{if(h(n))throw this.raise(this.state.start,E.UnterminatedString);++this.state.pos}}t+=this.input.slice(r,this.state.pos++),this.finishToken(c.string,t)}readTmplToken(){let e="",t=this.state.pos,r=!1;for(;;){if(this.state.pos>=this.length)throw this.raise(this.state.start,E.UnterminatedTemplate);const n=this.input.charCodeAt(this.state.pos);if(96===n||36===n&&123===this.input.charCodeAt(this.state.pos+1))return this.state.pos===this.state.start&&this.match(c.template)?36===n?(this.state.pos+=2,void this.finishToken(c.dollarBraceL)):(++this.state.pos,void this.finishToken(c.backQuote)):(e+=this.input.slice(t,this.state.pos),void this.finishToken(c.template,r?null:e));if(92===n){e+=this.input.slice(t,this.state.pos);const n=this.readEscapedChar(!0);null===n?r=!0:e+=n,t=this.state.pos}else if(h(n)){switch(e+=this.input.slice(t,this.state.pos),++this.state.pos,n){case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(n)}++this.state.curLine,this.state.lineStart=this.state.pos,t=this.state.pos}else++this.state.pos}}recordStrictModeErrors(e,t){this.state.strict&&!this.state.strictErrors.has(e)?this.raise(e,t):this.state.strictErrors.set(e,t)}readEscapedChar(e){const t=!e,r=this.input.charCodeAt(++this.state.pos);switch(++this.state.pos,r){case 110:return"\n";case 114:return"\r";case 120:{const e=this.readHexChar(2,!1,t);return null===e?null:String.fromCharCode(e)}case 117:{const e=this.readCodePoint(t);return null===e?null:String.fromCodePoint(e)}case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.state.pos)&&++this.state.pos;case 10:this.state.lineStart=this.state.pos,++this.state.curLine;case 8232:case 8233:return"";case 56:case 57:if(e)return null;this.recordStrictModeErrors(this.state.pos-1,E.StrictNumericEscape);default:if(r>=48&&r<=55){const t=this.state.pos-1;let r=this.input.substr(this.state.pos-1,3).match(/^[0-7]+/)[0],n=parseInt(r,8);n>255&&(r=r.slice(0,-1),n=parseInt(r,8)),this.state.pos+=r.length-1;const i=this.input.charCodeAt(this.state.pos);if("0"!==r||56===i||57===i){if(e)return null;this.recordStrictModeErrors(t,E.StrictNumericEscape)}return String.fromCharCode(n)}return String.fromCharCode(r)}}readHexChar(e,t,r){const n=this.state.pos,i=this.readInt(16,e,t,!1);return null===i&&(r?this.raise(n,E.InvalidEscapeSequence):this.state.pos=n-1),i}readWord1(e){this.state.containsEsc=!1;let t="";const r=this.state.pos;let n=this.state.pos;for(void 0!==e&&(this.state.pos+=e<=65535?1:2);this.state.pos<this.length;){const e=this.codePointAtPos(this.state.pos);if(j(e))this.state.pos+=e<=65535?1:2;else{if(92!==e)break;{this.state.containsEsc=!0,t+=this.input.slice(n,this.state.pos);const e=this.state.pos,i=this.state.pos===r?M:j;if(117!==this.input.charCodeAt(++this.state.pos)){this.raise(this.state.pos,E.MissingUnicodeEscape),n=this.state.pos-1;continue}++this.state.pos;const s=this.readCodePoint(!0);null!==s&&(i(s)||this.raise(e,E.EscapedCharNotAnIdentifier),t+=String.fromCodePoint(s)),n=this.state.pos}}}return t+this.input.slice(n,this.state.pos)}readWord(e){const t=this.readWord1(e),r=o.get(t)||c.name;this.finishToken(r,t)}checkKeywordEscapes(){const e=this.state.type.keyword;e&&this.state.containsEsc&&this.raise(this.state.start,E.InvalidEscapedReservedWord,e)}braceIsBlock(e){const t=this.curContext();return t===O.functionExpression||t===O.functionStatement||(e!==c.colon||t!==O.braceStatement&&t!==O.braceExpression?e===c._return||e===c.name&&this.state.exprAllowed?this.hasPrecedingLineBreak():e===c._else||e===c.semi||e===c.eof||e===c.parenR||e===c.arrow||(e===c.braceL?t===O.braceStatement:e!==c._var&&e!==c._const&&e!==c.name&&(e===c.relational||!this.state.exprAllowed)):!t.isExpr)}updateContext(e){const t=this.state.type;let r;!t.keyword||e!==c.dot&&e!==c.questionDot?(r=t.updateContext)?r.call(this,e):this.state.exprAllowed=t.beforeExpr:this.state.exprAllowed=!1}}class ke{constructor(){this.privateNames=new Set,this.loneAccessors=new Map,this.undefinedPrivateNames=new Map}}class De{constructor(e){this.stack=[],this.undefinedPrivateNames=new Map,this.raise=e}current(){return this.stack[this.stack.length-1]}enter(){this.stack.push(new ke)}exit(){const e=this.stack.pop(),t=this.current();for(const[r,n]of Array.from(e.undefinedPrivateNames))t?t.undefinedPrivateNames.has(r)||t.undefinedPrivateNames.set(r,n):this.raise(n,E.InvalidPrivateFieldResolution,r)}declarePrivateName(e,t,r){const n=this.current();let i=n.privateNames.has(e);if(3&t){const r=i&&n.loneAccessors.get(e);r?(i=(3&r)==(3&t)||(4&r)!=(4&t),i||n.loneAccessors.delete(e)):i||n.loneAccessors.set(e,t)}i&&this.raise(r,E.PrivateNameRedeclaration,e),n.privateNames.add(e),n.undefinedPrivateNames.delete(e)}usePrivateName(e,t){let r;for(r of this.stack)if(r.privateNames.has(e))return;r?r.undefinedPrivateNames.set(e,t):this.raise(t,E.InvalidPrivateFieldResolution,e)}}class _e{constructor(e=0){this.type=void 0,this.type=e}canBeArrowParameterDeclaration(){return 2===this.type||1===this.type}isCertainlyParameterDeclaration(){return 3===this.type}}class Le extends _e{constructor(e){super(e),this.errors=new Map}recordDeclarationError(e,t){this.errors.set(e,t)}clearDeclarationError(e){this.errors.delete(e)}iterateErrors(e){this.errors.forEach(e)}}class Me{constructor(e){this.stack=[new _e],this.raise=e}enter(e){this.stack.push(e)}exit(){this.stack.pop()}recordParameterInitializerError(e,t){const{stack:r}=this;let n=r.length-1,i=r[n];for(;!i.isCertainlyParameterDeclaration();){if(!i.canBeArrowParameterDeclaration())return;i.recordDeclarationError(e,t),i=r[--n]}this.raise(e,t)}recordParenthesizedIdentifierError(e,t){const{stack:r}=this,n=r[r.length-1];if(n.isCertainlyParameterDeclaration())this.raise(e,t);else{if(!n.canBeArrowParameterDeclaration())return;n.recordDeclarationError(e,t)}}recordAsyncArrowParametersError(e,t){const{stack:r}=this;let n=r.length-1,i=r[n];for(;i.canBeArrowParameterDeclaration();)2===i.type&&i.recordDeclarationError(e,t),i=r[--n]}validateAsPattern(){const{stack:e}=this,t=e[e.length-1];t.canBeArrowParameterDeclaration()&&t.iterateErrors(((t,r)=>{this.raise(r,t);let n=e.length-2,i=e[n];for(;i.canBeArrowParameterDeclaration();)i.clearDeclarationError(r),i=e[--n]}))}}function je(){return new _e}class Fe extends Ce{addExtra(e,t,r){e&&((e.extra=e.extra||{})[t]=r)}isRelational(e){return this.match(c.relational)&&this.state.value===e}expectRelational(e){this.isRelational(e)?this.next():this.unexpected(null,c.relational)}isContextual(e){return this.match(c.name)&&this.state.value===e&&!this.state.containsEsc}isUnparsedContextual(e,t){const r=e+t.length;return this.input.slice(e,r)===t&&(r===this.input.length||!j(this.input.charCodeAt(r)))}isLookaheadContextual(e){const t=this.nextTokenStart();return this.isUnparsedContextual(t,e)}eatContextual(e){return this.isContextual(e)&&this.eat(c.name)}expectContextual(e,t){this.eatContextual(e)||this.unexpected(null,t)}canInsertSemicolon(){return this.match(c.eof)||this.match(c.braceR)||this.hasPrecedingLineBreak()}hasPrecedingLineBreak(){return u.test(this.input.slice(this.state.lastTokEnd,this.state.start))}hasFollowingLineBreak(){return u.test(this.input.slice(this.state.end,this.nextTokenStart()))}isLineTerminator(){return this.eat(c.semi)||this.canInsertSemicolon()}semicolon(e=!0){(e?this.isLineTerminator():this.eat(c.semi))||this.raise(this.state.lastTokEnd,E.MissingSemicolon)}expect(e,t){this.eat(e)||this.unexpected(t,e)}assertNoSpace(e="Unexpected space."){this.state.start>this.state.lastTokEnd&&this.raise(this.state.lastTokEnd,{code:x.SyntaxError,reasonCode:"UnexpectedSpace",template:e})}unexpected(e,t={code:x.SyntaxError,reasonCode:"UnexpectedToken",template:"Unexpected token"}){throw t instanceof a&&(t={code:x.SyntaxError,reasonCode:"UnexpectedToken",template:`Unexpected token, expected "${t.label}"`}),this.raise(null!=e?e:this.state.start,t)}expectPlugin(e,t){if(!this.hasPlugin(e))throw this.raiseWithData(null!=t?t:this.state.start,{missingPlugin:[e]},`This experimental syntax requires enabling the parser plugin: '${e}'`);return!0}expectOnePlugin(e,t){if(!e.some((e=>this.hasPlugin(e))))throw this.raiseWithData(null!=t?t:this.state.start,{missingPlugin:e},`This experimental syntax requires enabling one of the following parser plugin(s): '${e.join(", ")}'`)}tryParse(e,t=this.state.clone()){const r={node:null};try{const n=e(((e=null)=>{throw r.node=e,r}));if(this.state.errors.length>t.errors.length){const e=this.state;return this.state=t,this.state.tokensLength=e.tokensLength,{node:n,error:e.errors[t.errors.length],thrown:!1,aborted:!1,failState:e}}return{node:n,error:null,thrown:!1,aborted:!1,failState:null}}catch(e){const n=this.state;if(this.state=t,e instanceof SyntaxError)return{node:null,error:e,thrown:!0,aborted:!1,failState:n};if(e===r)return{node:r.node,error:null,thrown:!1,aborted:!0,failState:n};throw e}}checkExpressionErrors(e,t){if(!e)return!1;const{shorthandAssign:r,doubleProto:n}=e;if(!t)return r>=0||n>=0;r>=0&&this.unexpected(r),n>=0&&this.raise(n,E.DuplicateProto)}isLiteralPropertyName(){return this.match(c.name)||!!this.state.type.keyword||this.match(c.string)||this.match(c.num)||this.match(c.bigint)||this.match(c.decimal)}isPrivateName(e){return"PrivateName"===e.type}getPrivateNameSV(e){return e.id.name}hasPropertyAsPrivateName(e){return("MemberExpression"===e.type||"OptionalMemberExpression"===e.type)&&this.isPrivateName(e.property)}isOptionalChain(e){return"OptionalMemberExpression"===e.type||"OptionalCallExpression"===e.type}isObjectProperty(e){return"ObjectProperty"===e.type}isObjectMethod(e){return"ObjectMethod"===e.type}initializeScopes(e="module"===this.options.sourceType){const t=this.state.labels;this.state.labels=[];const r=this.state.exportedIdentifiers;this.state.exportedIdentifiers=[];const n=this.inModule;this.inModule=e;const i=this.scope,s=this.getScopeHandler();this.scope=new s(this.raise.bind(this),this.inModule);const a=this.prodParam;this.prodParam=new ce;const o=this.classScope;this.classScope=new De(this.raise.bind(this));const l=this.expressionScope;return this.expressionScope=new Me(this.raise.bind(this)),()=>{this.state.labels=t,this.state.exportedIdentifiers=r,this.inModule=n,this.scope=i,this.prodParam=a,this.classScope=o,this.expressionScope=l}}enterInitialScopes(){let e=0;this.hasPlugin("topLevelAwait")&&this.inModule&&(e|=2),this.scope.enter(1),this.prodParam.enter(e)}}class Be{constructor(){this.shorthandAssign=-1,this.doubleProto=-1}}class Re{constructor(e,t,r){this.type=void 0,this.start=void 0,this.end=void 0,this.loc=void 0,this.range=void 0,this.leadingComments=void 0,this.trailingComments=void 0,this.innerComments=void 0,this.extra=void 0,this.type="",this.start=t,this.end=0,this.loc=new T(r),null!=e&&e.options.ranges&&(this.range=[t,0]),null!=e&&e.filename&&(this.loc.filename=e.filename)}__clone(){const e=new Re,t=Object.keys(this);for(let r=0,n=t.length;r<n;r++){const n=t[r];"leadingComments"!==n&&"trailingComments"!==n&&"innerComments"!==n&&(e[n]=this[n])}return e}}class Ue extends Fe{startNode(){return new Re(this,this.state.start,this.state.startLoc)}startNodeAt(e,t){return new Re(this,e,t)}startNodeAtNode(e){return this.startNodeAt(e.start,e.loc.start)}finishNode(e,t){return this.finishNodeAt(e,t,this.state.lastTokEnd,this.state.lastTokEndLoc)}finishNodeAt(e,t,r,n){return e.type=t,e.end=r,e.loc.end=n,this.options.ranges&&(e.range[1]=r),this.processComment(e),e}resetStartLocation(e,t,r){e.start=t,e.loc.start=r,this.options.ranges&&(e.range[0]=t)}resetEndLocation(e,t=this.state.lastTokEnd,r=this.state.lastTokEndLoc){e.end=t,e.loc.end=r,this.options.ranges&&(e.range[1]=t)}resetStartLocationFromNode(e,t){this.resetStartLocation(e,t.start,t.loc.start)}}const Ve=e=>"ParenthesizedExpression"===e.type?Ve(e.expression):e;class Ke extends Ue{toAssignable(e,t=!1){var r,n;let i;switch(("ParenthesizedExpression"===e.type||null!=(r=e.extra)&&r.parenthesized)&&(i=Ve(e),t?"Identifier"===i.type?this.expressionScope.recordParenthesizedIdentifierError(e.start,E.InvalidParenthesizedAssignment):"MemberExpression"!==i.type&&this.raise(e.start,E.InvalidParenthesizedAssignment):this.raise(e.start,E.InvalidParenthesizedAssignment)),e.type){case"Identifier":case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":break;case"ObjectExpression":e.type="ObjectPattern";for(let r=0,n=e.properties.length,i=n-1;r<n;r++){var s;const n=e.properties[r],a=r===i;this.toAssignableObjectExpressionProp(n,a,t),a&&"RestElement"===n.type&&null!=(s=e.extra)&&s.trailingComma&&this.raiseRestNotLast(e.extra.trailingComma)}break;case"ObjectProperty":this.toAssignable(e.value,t);break;case"SpreadElement":{this.checkToRestConversion(e),e.type="RestElement";const r=e.argument;this.toAssignable(r,t);break}case"ArrayExpression":e.type="ArrayPattern",this.toAssignableList(e.elements,null==(n=e.extra)?void 0:n.trailingComma,t);break;case"AssignmentExpression":"="!==e.operator&&this.raise(e.left.end,E.MissingEqInAssignment),e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(i,t)}return e}toAssignableObjectExpressionProp(e,t,r){if("ObjectMethod"===e.type){const t="get"===e.kind||"set"===e.kind?E.PatternHasAccessor:E.PatternHasMethod;this.raise(e.key.start,t)}else"SpreadElement"!==e.type||t?this.toAssignable(e,r):this.raiseRestNotLast(e.start)}toAssignableList(e,t,r){let n=e.length;if(n){const i=e[n-1];if("RestElement"===(null==i?void 0:i.type))--n;else if("SpreadElement"===(null==i?void 0:i.type)){i.type="RestElement";let e=i.argument;this.toAssignable(e,r),e=Ve(e),"Identifier"!==e.type&&"MemberExpression"!==e.type&&"ArrayPattern"!==e.type&&"ObjectPattern"!==e.type&&this.unexpected(e.start),t&&this.raiseTrailingCommaAfterRest(t),--n}}for(let t=0;t<n;t++){const n=e[t];n&&(this.toAssignable(n,r),"RestElement"===n.type&&this.raiseRestNotLast(n.start))}return e}toReferencedList(e,t){return e}toReferencedListDeep(e,t){this.toReferencedList(e,t);for(const t of e)"ArrayExpression"===(null==t?void 0:t.type)&&this.toReferencedListDeep(t.elements)}parseSpread(e,t){const r=this.startNode();return this.next(),r.argument=this.parseMaybeAssignAllowIn(e,void 0,t),this.finishNode(r,"SpreadElement")}parseRestBinding(){const e=this.startNode();return this.next(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")}parseBindingAtom(){switch(this.state.type){case c.bracketL:{const e=this.startNode();return this.next(),e.elements=this.parseBindingList(c.bracketR,93,!0),this.finishNode(e,"ArrayPattern")}case c.braceL:return this.parseObjectLike(c.braceR,!0)}return this.parseIdentifier()}parseBindingList(e,t,r,n){const i=[];let s=!0;for(;!this.eat(e);)if(s?s=!1:this.expect(c.comma),r&&this.match(c.comma))i.push(null);else{if(this.eat(e))break;if(this.match(c.ellipsis)){i.push(this.parseAssignableListItemTypes(this.parseRestBinding())),this.checkCommaAfterRest(t),this.expect(e);break}{const e=[];for(this.match(c.at)&&this.hasPlugin("decorators")&&this.raise(this.state.start,E.UnsupportedParameterDecorator);this.match(c.at);)e.push(this.parseDecorator());i.push(this.parseAssignableListItem(n,e))}}return i}parseAssignableListItem(e,t){const r=this.parseMaybeDefault();this.parseAssignableListItemTypes(r);const n=this.parseMaybeDefault(r.start,r.loc.start,r);return t.length&&(r.decorators=t),n}parseAssignableListItemTypes(e){return e}parseMaybeDefault(e,t,r){var n,i,s;if(t=null!=(n=t)?n:this.state.startLoc,e=null!=(i=e)?i:this.state.start,r=null!=(s=r)?s:this.parseBindingAtom(),!this.eat(c.eq))return r;const a=this.startNodeAt(e,t);return a.left=r,a.right=this.parseMaybeAssignAllowIn(),this.finishNode(a,"AssignmentPattern")}checkLVal(e,t,r=64,n,i,s=!1){switch(e.type){case"Identifier":{const{name:t}=e;this.state.strict&&(s?q(t,this.inModule):K(t))&&this.raise(e.start,64===r?E.StrictEvalArguments:E.StrictEvalArgumentsBinding,t),n&&(n.has(t)?this.raise(e.start,E.ParamDupe):n.add(t)),i&&"let"===t&&this.raise(e.start,E.LetInLexicalBinding),64&r||this.scope.declareName(t,r,e.start);break}case"MemberExpression":64!==r&&this.raise(e.start,E.InvalidPropertyBindingPattern);break;case"ObjectPattern":for(let t of e.properties){if(this.isObjectProperty(t))t=t.value;else if(this.isObjectMethod(t))continue;this.checkLVal(t,"object destructuring pattern",r,n,i)}break;case"ArrayPattern":for(const t of e.elements)t&&this.checkLVal(t,"array destructuring pattern",r,n,i);break;case"AssignmentPattern":this.checkLVal(e.left,"assignment pattern",r,n);break;case"RestElement":this.checkLVal(e.argument,"rest element",r,n);break;case"ParenthesizedExpression":this.checkLVal(e.expression,"parenthesized expression",r,n);break;default:this.raise(e.start,64===r?E.InvalidLhs:E.InvalidLhsBinding,t)}}checkToRestConversion(e){"Identifier"!==e.argument.type&&"MemberExpression"!==e.argument.type&&this.raise(e.argument.start,E.InvalidRestAssignmentPattern)}checkCommaAfterRest(e){this.match(c.comma)&&(this.lookaheadCharCode()===e?this.raiseTrailingCommaAfterRest(this.state.start):this.raiseRestNotLast(this.state.start))}raiseRestNotLast(e){throw this.raise(e,E.ElementAfterRest)}raiseTrailingCommaAfterRest(e){this.raise(e,E.RestTrailingComma)}}class qe extends Ke{checkProto(e,t,r,n){if("SpreadElement"===e.type||this.isObjectMethod(e)||e.computed||e.shorthand)return;const i=e.key;if("__proto__"===("Identifier"===i.type?i.name:i.value)){if(t)return void this.raise(i.start,E.RecordNoProto);r.used&&(n?-1===n.doubleProto&&(n.doubleProto=i.start):this.raise(i.start,E.DuplicateProto)),r.used=!0}}shouldExitDescending(e,t){return"ArrowFunctionExpression"===e.type&&e.start===t}getExpression(){let e=0;this.hasPlugin("topLevelAwait")&&this.inModule&&(e|=2),this.scope.enter(1),this.prodParam.enter(e),this.nextToken();const t=this.parseExpression();return this.match(c.eof)||this.unexpected(),t.comments=this.state.comments,t.errors=this.state.errors,this.options.tokens&&(t.tokens=this.tokens),t}parseExpression(e,t){return e?this.disallowInAnd((()=>this.parseExpressionBase(t))):this.allowInAnd((()=>this.parseExpressionBase(t)))}parseExpressionBase(e){const t=this.state.start,r=this.state.startLoc,n=this.parseMaybeAssign(e);if(this.match(c.comma)){const i=this.startNodeAt(t,r);for(i.expressions=[n];this.eat(c.comma);)i.expressions.push(this.parseMaybeAssign(e));return this.toReferencedList(i.expressions),this.finishNode(i,"SequenceExpression")}return n}parseMaybeAssignDisallowIn(e,t,r){return this.disallowInAnd((()=>this.parseMaybeAssign(e,t,r)))}parseMaybeAssignAllowIn(e,t,r){return this.allowInAnd((()=>this.parseMaybeAssign(e,t,r)))}parseMaybeAssign(e,t,r){const n=this.state.start,i=this.state.startLoc;if(this.isContextual("yield")&&this.prodParam.hasYield){this.state.exprAllowed=!0;let e=this.parseYield();return t&&(e=t.call(this,e,n,i)),e}let s;e?s=!1:(e=new Be,s=!0),(this.match(c.parenL)||this.match(c.name))&&(this.state.potentialArrowAt=this.state.start);let a=this.parseMaybeConditional(e,r);if(t&&(a=t.call(this,a,n,i)),this.state.type.isAssign){const t=this.startNodeAt(n,i),r=this.state.value;return t.operator=r,this.match(c.eq)?(t.left=this.toAssignable(a,!0),e.doubleProto=-1):t.left=a,e.shorthandAssign>=t.left.start&&(e.shorthandAssign=-1),this.checkLVal(a,"assignment expression"),this.next(),t.right=this.parseMaybeAssign(),this.finishNode(t,"AssignmentExpression")}return s&&this.checkExpressionErrors(e,!0),a}parseMaybeConditional(e,t){const r=this.state.start,n=this.state.startLoc,i=this.state.potentialArrowAt,s=this.parseExprOps(e);return this.shouldExitDescending(s,i)?s:this.parseConditional(s,r,n,t)}parseConditional(e,t,r,n){if(this.eat(c.question)){const n=this.startNodeAt(t,r);return n.test=e,n.consequent=this.parseMaybeAssignAllowIn(),this.expect(c.colon),n.alternate=this.parseMaybeAssign(),this.finishNode(n,"ConditionalExpression")}return e}parseExprOps(e){const t=this.state.start,r=this.state.startLoc,n=this.state.potentialArrowAt,i=this.parseMaybeUnary(e);return this.shouldExitDescending(i,n)?i:this.parseExprOp(i,t,r,-1)}parseExprOp(e,t,r,n){let i=this.state.type.binop;if(null!=i&&(this.prodParam.hasIn||!this.match(c._in))&&i>n){const s=this.state.type;if(s===c.pipeline){if(this.expectPlugin("pipelineOperator"),this.state.inFSharpPipelineDirectBody)return e;this.state.inPipeline=!0,this.checkPipelineAtInfixOperator(e,t)}const a=this.startNodeAt(t,r);a.left=e,a.operator=this.state.value;const o=s===c.logicalOR||s===c.logicalAND,l=s===c.nullishCoalescing;if(l&&(i=c.logicalAND.binop),this.next(),s===c.pipeline&&"minimal"===this.getPluginOption("pipelineOperator","proposal")&&this.match(c.name)&&"await"===this.state.value&&this.prodParam.hasAwait)throw this.raise(this.state.start,E.UnexpectedAwaitAfterPipelineBody);a.right=this.parseExprOpRightExpr(s,i),this.finishNode(a,o||l?"LogicalExpression":"BinaryExpression");const p=this.state.type;if(l&&(p===c.logicalOR||p===c.logicalAND)||o&&p===c.nullishCoalescing)throw this.raise(this.state.start,E.MixingCoalesceWithLogical);return this.parseExprOp(a,t,r,n)}return e}parseExprOpRightExpr(e,t){const r=this.state.start,n=this.state.startLoc;if(e===c.pipeline)switch(this.getPluginOption("pipelineOperator","proposal")){case"smart":return this.withTopicPermittingContext((()=>this.parseSmartPipelineBody(this.parseExprOpBaseRightExpr(e,t),r,n)));case"fsharp":return this.withSoloAwaitPermittingContext((()=>this.parseFSharpPipelineBody(t)))}return this.parseExprOpBaseRightExpr(e,t)}parseExprOpBaseRightExpr(e,t){const r=this.state.start,n=this.state.startLoc;return this.parseExprOp(this.parseMaybeUnary(),r,n,e.rightAssociative?t-1:t)}checkExponentialAfterUnary(e){this.match(c.exponent)&&this.raise(e.argument.start,E.UnexpectedTokenUnaryExponentiation)}parseMaybeUnary(e,t){const r=this.state.start,n=this.state.startLoc,i=this.isContextual("await");if(i&&this.isAwaitAllowed()){this.next();const e=this.parseAwait(r,n);return t||this.checkExponentialAfterUnary(e),e}if(this.isContextual("module")&&123===this.lookaheadCharCode()&&!this.hasFollowingLineBreak())return this.parseModuleExpression();const s=this.match(c.incDec),a=this.startNode();if(this.state.type.prefix){a.operator=this.state.value,a.prefix=!0,this.match(c._throw)&&this.expectPlugin("throwExpressions");const r=this.match(c._delete);if(this.next(),a.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),this.state.strict&&r){const e=a.argument;"Identifier"===e.type?this.raise(a.start,E.StrictDelete):this.hasPropertyAsPrivateName(e)&&this.raise(a.start,E.DeletePrivateField)}if(!s)return t||this.checkExponentialAfterUnary(a),this.finishNode(a,"UnaryExpression")}const o=this.parseUpdate(a,s,e);return i&&(this.hasPlugin("v8intrinsic")?this.state.type.startsExpr:this.state.type.startsExpr&&!this.match(c.modulo))&&!this.isAmbiguousAwait()?(this.raiseOverwrite(r,this.hasPlugin("topLevelAwait")?E.AwaitNotInAsyncContext:E.AwaitNotInAsyncFunction),this.parseAwait(r,n)):o}parseUpdate(e,t,r){if(t)return this.checkLVal(e.argument,"prefix operation"),this.finishNode(e,"UpdateExpression");const n=this.state.start,i=this.state.startLoc;let s=this.parseExprSubscripts(r);if(this.checkExpressionErrors(r,!1))return s;for(;this.state.type.postfix&&!this.canInsertSemicolon();){const e=this.startNodeAt(n,i);e.operator=this.state.value,e.prefix=!1,e.argument=s,this.checkLVal(s,"postfix operation"),this.next(),s=this.finishNode(e,"UpdateExpression")}return s}parseExprSubscripts(e){const t=this.state.start,r=this.state.startLoc,n=this.state.potentialArrowAt,i=this.parseExprAtom(e);return this.shouldExitDescending(i,n)?i:this.parseSubscripts(i,t,r)}parseSubscripts(e,t,r,n){const i={optionalChainMember:!1,maybeAsyncArrow:this.atPossibleAsyncArrow(e),stop:!1};do{e=this.parseSubscript(e,t,r,n,i),i.maybeAsyncArrow=!1}while(!i.stop);return e}parseSubscript(e,t,r,n,i){if(!n&&this.eat(c.doubleColon))return this.parseBind(e,t,r,n,i);if(this.match(c.backQuote))return this.parseTaggedTemplateExpression(e,t,r,i);let s=!1;if(this.match(c.questionDot)){if(n&&40===this.lookaheadCharCode())return i.stop=!0,e;i.optionalChainMember=s=!0,this.next()}return!n&&this.match(c.parenL)?this.parseCoverCallAndAsyncArrowHead(e,t,r,i,s):s||this.match(c.bracketL)||this.eat(c.dot)?this.parseMember(e,t,r,i,s):(i.stop=!0,e)}parseMember(e,t,r,n,i){const s=this.startNodeAt(t,r),a=this.eat(c.bracketL);s.object=e,s.computed=a;const o=!a&&this.match(c.privateName)&&this.state.value,l=a?this.parseExpression():o?this.parsePrivateName():this.parseIdentifier(!0);return!1!==o&&("Super"===s.object.type&&this.raise(t,E.SuperPrivateField),this.classScope.usePrivateName(o,l.start)),s.property=l,a&&this.expect(c.bracketR),n.optionalChainMember?(s.optional=i,this.finishNode(s,"OptionalMemberExpression")):this.finishNode(s,"MemberExpression")}parseBind(e,t,r,n,i){const s=this.startNodeAt(t,r);return s.object=e,s.callee=this.parseNoCallExpr(),i.stop=!0,this.parseSubscripts(this.finishNode(s,"BindExpression"),t,r,n)}parseCoverCallAndAsyncArrowHead(e,t,r,n,i){const s=this.state.maybeInArrowParameters;this.state.maybeInArrowParameters=!0,this.next();let a=this.startNodeAt(t,r);return a.callee=e,n.maybeAsyncArrow&&this.expressionScope.enter(new Le(2)),n.optionalChainMember&&(a.optional=i),a.arguments=i?this.parseCallExpressionArguments(c.parenR,!1):this.parseCallExpressionArguments(c.parenR,n.maybeAsyncArrow,"Import"===e.type,"Super"!==e.type,a),this.finishCallExpression(a,n.optionalChainMember),n.maybeAsyncArrow&&this.shouldParseAsyncArrow()&&!i?(n.stop=!0,this.expressionScope.validateAsPattern(),this.expressionScope.exit(),a=this.parseAsyncArrowFromCallExpression(this.startNodeAt(t,r),a)):(n.maybeAsyncArrow&&this.expressionScope.exit(),this.toReferencedArguments(a)),this.state.maybeInArrowParameters=s,a}toReferencedArguments(e,t){this.toReferencedListDeep(e.arguments,t)}parseTaggedTemplateExpression(e,t,r,n){const i=this.startNodeAt(t,r);return i.tag=e,i.quasi=this.parseTemplate(!0),n.optionalChainMember&&this.raise(t,E.OptionalChainingNoTemplate),this.finishNode(i,"TaggedTemplateExpression")}atPossibleAsyncArrow(e){return"Identifier"===e.type&&"async"===e.name&&this.state.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start==5&&e.start===this.state.potentialArrowAt}finishCallExpression(e,t){if("Import"===e.callee.type)if(2===e.arguments.length&&(this.hasPlugin("moduleAttributes")||this.expectPlugin("importAssertions")),0===e.arguments.length||e.arguments.length>2)this.raise(e.start,E.ImportCallArity,this.hasPlugin("importAssertions")||this.hasPlugin("moduleAttributes")?"one or two arguments":"one argument");else for(const t of e.arguments)"SpreadElement"===t.type&&this.raise(t.start,E.ImportCallSpreadArgument);return this.finishNode(e,t?"OptionalCallExpression":"CallExpression")}parseCallExpressionArguments(e,t,r,n,i){const s=[];let a=!0;const o=this.state.inFSharpPipelineDirectBody;for(this.state.inFSharpPipelineDirectBody=!1;!this.eat(e);){if(a)a=!1;else if(this.expect(c.comma),this.match(e)){!r||this.hasPlugin("importAssertions")||this.hasPlugin("moduleAttributes")||this.raise(this.state.lastTokStart,E.ImportCallArgumentTrailingComma),i&&this.addExtra(i,"trailingComma",this.state.lastTokStart),this.next();break}s.push(this.parseExprListItem(!1,t?new Be:void 0,t?{start:0}:void 0,n))}return this.state.inFSharpPipelineDirectBody=o,s}shouldParseAsyncArrow(){return this.match(c.arrow)&&!this.canInsertSemicolon()}parseAsyncArrowFromCallExpression(e,t){var r;return this.expect(c.arrow),this.parseArrowExpression(e,t.arguments,!0,null==(r=t.extra)?void 0:r.trailingComma),e}parseNoCallExpr(){const e=this.state.start,t=this.state.startLoc;return this.parseSubscripts(this.parseExprAtom(),e,t,!0)}parseExprAtom(e){this.state.type===c.slash&&this.readRegexp();const t=this.state.potentialArrowAt===this.state.start;let r;switch(this.state.type){case c._super:return this.parseSuper();case c._import:return r=this.startNode(),this.next(),this.match(c.dot)?this.parseImportMetaProperty(r):(this.match(c.parenL)||this.raise(this.state.lastTokStart,E.UnsupportedImport),this.finishNode(r,"Import"));case c._this:return r=this.startNode(),this.next(),this.finishNode(r,"ThisExpression");case c.name:{const e=this.state.containsEsc,r=this.parseIdentifier();if(!e&&"async"===r.name&&!this.canInsertSemicolon()){if(this.match(c._function)){const e=this.state.context.length-1;if(this.state.context[e]!==O.functionStatement)throw new Error("Internal error");return this.state.context[e]=O.functionExpression,this.next(),this.parseFunction(this.startNodeAtNode(r),void 0,!0)}if(this.match(c.name))return 61===this.lookaheadCharCode()?this.parseAsyncArrowUnaryFunction(r):r;if(this.match(c._do))return this.parseDo(!0)}return t&&this.match(c.arrow)&&!this.canInsertSemicolon()?(this.next(),this.parseArrowExpression(this.startNodeAtNode(r),[r],!1)):r}case c._do:return this.parseDo(!1);case c.regexp:{const e=this.state.value;return r=this.parseLiteral(e.value,"RegExpLiteral"),r.pattern=e.pattern,r.flags=e.flags,r}case c.num:return this.parseLiteral(this.state.value,"NumericLiteral");case c.bigint:return this.parseLiteral(this.state.value,"BigIntLiteral");case c.decimal:return this.parseLiteral(this.state.value,"DecimalLiteral");case c.string:return this.parseLiteral(this.state.value,"StringLiteral");case c._null:return r=this.startNode(),this.next(),this.finishNode(r,"NullLiteral");case c._true:case c._false:return this.parseBooleanLiteral();case c.parenL:return this.parseParenAndDistinguishExpression(t);case c.bracketBarL:case c.bracketHashL:return this.parseArrayLike(this.state.type===c.bracketBarL?c.bracketBarR:c.bracketR,!1,!0,e);case c.bracketL:return this.parseArrayLike(c.bracketR,!0,!1,e);case c.braceBarL:case c.braceHashL:return this.parseObjectLike(this.state.type===c.braceBarL?c.braceBarR:c.braceR,!1,!0,e);case c.braceL:return this.parseObjectLike(c.braceR,!1,!1,e);case c._function:return this.parseFunctionOrFunctionSent();case c.at:this.parseDecorators();case c._class:return r=this.startNode(),this.takeDecorators(r),this.parseClass(r,!1);case c._new:return this.parseNewOrNewTarget();case c.backQuote:return this.parseTemplate(!1);case c.doubleColon:{r=this.startNode(),this.next(),r.object=null;const e=r.callee=this.parseNoCallExpr();if("MemberExpression"===e.type)return this.finishNode(r,"BindExpression");throw this.raise(e.start,E.UnsupportedBind)}case c.privateName:{const e=this.state.start,t=this.state.value;if(r=this.parsePrivateName(),this.match(c._in))this.expectPlugin("privateIn"),this.classScope.usePrivateName(t,r.start);else{if(!this.hasPlugin("privateIn"))throw this.unexpected(e);this.raise(this.state.start,E.PrivateInExpectedIn,t)}return r}case c.hash:if(this.state.inPipeline)return r=this.startNode(),"smart"!==this.getPluginOption("pipelineOperator","proposal")&&this.raise(r.start,E.PrimaryTopicRequiresSmartPipeline),this.next(),this.primaryTopicReferenceIsAllowedInCurrentTopicContext()||this.raise(r.start,E.PrimaryTopicNotAllowed),this.registerTopicReference(),this.finishNode(r,"PipelinePrimaryTopicReference");case c.relational:if("<"===this.state.value){const e=this.input.codePointAt(this.nextTokenStart());(M(e)||62===e)&&this.expectOnePlugin(["jsx","flow","typescript"])}default:throw this.unexpected()}}parseAsyncArrowUnaryFunction(e){const t=this.startNodeAtNode(e);this.prodParam.enter(ue(!0,this.prodParam.hasYield));const r=[this.parseIdentifier()];return this.prodParam.exit(),this.hasPrecedingLineBreak()&&this.raise(this.state.pos,E.LineTerminatorBeforeArrow),this.expect(c.arrow),this.parseArrowExpression(t,r,!0),t}parseDo(e){this.expectPlugin("doExpressions"),e&&this.expectPlugin("asyncDoExpressions");const t=this.startNode();t.async=e,this.next();const r=this.state.labels;return this.state.labels=[],e?(this.prodParam.enter(2),t.body=this.parseBlock(),this.prodParam.exit()):t.body=this.parseBlock(),this.state.labels=r,this.finishNode(t,"DoExpression")}parseSuper(){const e=this.startNode();return this.next(),!this.match(c.parenL)||this.scope.allowDirectSuper||this.options.allowSuperOutsideMethod?this.scope.allowSuper||this.options.allowSuperOutsideMethod||this.raise(e.start,E.UnexpectedSuper):this.raise(e.start,E.SuperNotAllowed),this.match(c.parenL)||this.match(c.bracketL)||this.match(c.dot)||this.raise(e.start,E.UnsupportedSuper),this.finishNode(e,"Super")}parseBooleanLiteral(){const e=this.startNode();return e.value=this.match(c._true),this.next(),this.finishNode(e,"BooleanLiteral")}parseMaybePrivateName(e){return this.match(c.privateName)?(e||this.raise(this.state.start+1,E.UnexpectedPrivateField),this.parsePrivateName()):this.parseIdentifier(!0)}parsePrivateName(){const e=this.startNode(),t=this.startNodeAt(this.state.start+1,new y(this.state.curLine,this.state.start+1-this.state.lineStart)),r=this.state.value;return this.next(),e.id=this.createIdentifier(t,r),this.finishNode(e,"PrivateName")}parseFunctionOrFunctionSent(){const e=this.startNode();if(this.next(),this.prodParam.hasYield&&this.match(c.dot)){const t=this.createIdentifier(this.startNodeAtNode(e),"function");return this.next(),this.parseMetaProperty(e,t,"sent")}return this.parseFunction(e)}parseMetaProperty(e,t,r){e.meta=t,"function"===t.name&&"sent"===r&&(this.isContextual(r)?this.expectPlugin("functionSent"):this.hasPlugin("functionSent")||this.unexpected());const n=this.state.containsEsc;return e.property=this.parseIdentifier(!0),(e.property.name!==r||n)&&this.raise(e.property.start,E.UnsupportedMetaProperty,t.name,r),this.finishNode(e,"MetaProperty")}parseImportMetaProperty(e){const t=this.createIdentifier(this.startNodeAtNode(e),"import");return this.next(),this.isContextual("meta")&&(this.inModule||this.raise(t.start,P.ImportMetaOutsideModule),this.sawUnambiguousESM=!0),this.parseMetaProperty(e,t,"meta")}parseLiteral(e,t,r,n){r=r||this.state.start,n=n||this.state.startLoc;const i=this.startNodeAt(r,n);return this.addExtra(i,"rawValue",e),this.addExtra(i,"raw",this.input.slice(r,this.state.end)),i.value=e,this.next(),this.finishNode(i,t)}parseParenAndDistinguishExpression(e){const t=this.state.start,r=this.state.startLoc;let n;this.next(),this.expressionScope.enter(new Le(1));const i=this.state.maybeInArrowParameters,s=this.state.inFSharpPipelineDirectBody;this.state.maybeInArrowParameters=!0,this.state.inFSharpPipelineDirectBody=!1;const a=this.state.start,o=this.state.startLoc,l=[],p=new Be,u={start:0};let d,h,f=!0;for(;!this.match(c.parenR);){if(f)f=!1;else if(this.expect(c.comma,u.start||null),this.match(c.parenR)){h=this.state.start;break}if(this.match(c.ellipsis)){const e=this.state.start,t=this.state.startLoc;d=this.state.start,l.push(this.parseParenItem(this.parseRestBinding(),e,t)),this.checkCommaAfterRest(41);break}l.push(this.parseMaybeAssignAllowIn(p,this.parseParenItem,u))}const m=this.state.lastTokEnd,y=this.state.lastTokEndLoc;this.expect(c.parenR),this.state.maybeInArrowParameters=i,this.state.inFSharpPipelineDirectBody=s;let T=this.startNodeAt(t,r);if(e&&this.shouldParseArrow()&&(T=this.parseArrow(T)))return this.expressionScope.validateAsPattern(),this.expressionScope.exit(),this.parseArrowExpression(T,l,!1),T;if(this.expressionScope.exit(),l.length||this.unexpected(this.state.lastTokStart),h&&this.unexpected(h),d&&this.unexpected(d),this.checkExpressionErrors(p,!0),u.start&&this.unexpected(u.start),this.toReferencedListDeep(l,!0),l.length>1?(n=this.startNodeAt(a,o),n.expressions=l,this.finishNodeAt(n,"SequenceExpression",m,y)):n=l[0],!this.options.createParenthesizedExpressions)return this.addExtra(n,"parenthesized",!0),this.addExtra(n,"parenStart",t),n;const b=this.startNodeAt(t,r);return b.expression=n,this.finishNode(b,"ParenthesizedExpression"),b}shouldParseArrow(){return!this.canInsertSemicolon()}parseArrow(e){if(this.eat(c.arrow))return e}parseParenItem(e,t,r){return e}parseNewOrNewTarget(){const e=this.startNode();if(this.next(),this.match(c.dot)){const t=this.createIdentifier(this.startNodeAtNode(e),"new");this.next();const r=this.parseMetaProperty(e,t,"target");return this.scope.inNonArrowFunction||this.scope.inClass||this.raise(r.start,E.UnexpectedNewTarget),r}return this.parseNew(e)}parseNew(e){return e.callee=this.parseNoCallExpr(),"Import"===e.callee.type?this.raise(e.callee.start,E.ImportCallNotNewExpression):this.isOptionalChain(e.callee)?this.raise(this.state.lastTokEnd,E.OptionalChainingNoNew):this.eat(c.questionDot)&&this.raise(this.state.start,E.OptionalChainingNoNew),this.parseNewArguments(e),this.finishNode(e,"NewExpression")}parseNewArguments(e){if(this.eat(c.parenL)){const t=this.parseExprList(c.parenR);this.toReferencedList(t),e.arguments=t}else e.arguments=[]}parseTemplateElement(e){const t=this.startNode();return null===this.state.value&&(e||this.raise(this.state.start+1,E.InvalidEscapeSequenceTemplate)),t.value={raw:this.input.slice(this.state.start,this.state.end).replace(/\r\n?/g,"\n"),cooked:this.state.value},this.next(),t.tail=this.match(c.backQuote),this.finishNode(t,"TemplateElement")}parseTemplate(e){const t=this.startNode();this.next(),t.expressions=[];let r=this.parseTemplateElement(e);for(t.quasis=[r];!r.tail;)this.expect(c.dollarBraceL),t.expressions.push(this.parseTemplateSubstitution()),this.expect(c.braceR),t.quasis.push(r=this.parseTemplateElement(e));return this.next(),this.finishNode(t,"TemplateLiteral")}parseTemplateSubstitution(){return this.parseExpression()}parseObjectLike(e,t,r,n){r&&this.expectPlugin("recordAndTuple");const i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;const s=Object.create(null);let a=!0;const o=this.startNode();for(o.properties=[],this.next();!this.match(e);){if(a)a=!1;else if(this.expect(c.comma),this.match(e)){this.addExtra(o,"trailingComma",this.state.lastTokStart);break}const i=this.parsePropertyDefinition(t,n);t||this.checkProto(i,r,s,n),r&&!this.isObjectProperty(i)&&"SpreadElement"!==i.type&&this.raise(i.start,E.InvalidRecordProperty),i.shorthand&&this.addExtra(i,"shorthand",!0),o.properties.push(i)}this.state.exprAllowed=!1,this.next(),this.state.inFSharpPipelineDirectBody=i;let l="ObjectExpression";return t?l="ObjectPattern":r&&(l="RecordExpression"),this.finishNode(o,l)}maybeAsyncOrAccessorProp(e){return!e.computed&&"Identifier"===e.key.type&&(this.isLiteralPropertyName()||this.match(c.bracketL)||this.match(c.star))}parsePropertyDefinition(e,t){let r=[];if(this.match(c.at))for(this.hasPlugin("decorators")&&this.raise(this.state.start,E.UnsupportedPropertyDecorator);this.match(c.at);)r.push(this.parseDecorator());const n=this.startNode();let i,s,a=!1,o=!1,l=!1;if(this.match(c.ellipsis))return r.length&&this.unexpected(),e?(this.next(),n.argument=this.parseIdentifier(),this.checkCommaAfterRest(125),this.finishNode(n,"RestElement")):this.parseSpread();r.length&&(n.decorators=r,r=[]),n.method=!1,(e||t)&&(i=this.state.start,s=this.state.startLoc),e||(a=this.eat(c.star));const p=this.state.containsEsc,u=this.parsePropertyName(n,!1);if(!e&&!a&&!p&&this.maybeAsyncOrAccessorProp(n)){const e=u.name;"async"!==e||this.hasPrecedingLineBreak()||(o=!0,a=this.eat(c.star),this.parsePropertyName(n,!1)),"get"!==e&&"set"!==e||(l=!0,n.kind=e,this.match(c.star)&&(a=!0,this.raise(this.state.pos,E.AccessorIsGenerator,e),this.next()),this.parsePropertyName(n,!1))}return this.parseObjPropValue(n,i,s,a,o,e,l,t),n}getGetterSetterExpectedParamCount(e){return"get"===e.kind?0:1}getObjectOrClassMethodParams(e){return e.params}checkGetterSetterParams(e){var t;const r=this.getGetterSetterExpectedParamCount(e),n=this.getObjectOrClassMethodParams(e),i=e.start;n.length!==r&&("get"===e.kind?this.raise(i,E.BadGetterArity):this.raise(i,E.BadSetterArity)),"set"===e.kind&&"RestElement"===(null==(t=n[n.length-1])?void 0:t.type)&&this.raise(i,E.BadSetterRestParameter)}parseObjectMethod(e,t,r,n,i){return i?(this.parseMethod(e,t,!1,!1,!1,"ObjectMethod"),this.checkGetterSetterParams(e),e):r||t||this.match(c.parenL)?(n&&this.unexpected(),e.kind="method",e.method=!0,this.parseMethod(e,t,r,!1,!1,"ObjectMethod")):void 0}parseObjectProperty(e,t,r,n,i){return e.shorthand=!1,this.eat(c.colon)?(e.value=n?this.parseMaybeDefault(this.state.start,this.state.startLoc):this.parseMaybeAssignAllowIn(i),this.finishNode(e,"ObjectProperty")):e.computed||"Identifier"!==e.key.type?void 0:(this.checkReservedWord(e.key.name,e.key.start,!0,!1),n?e.value=this.parseMaybeDefault(t,r,e.key.__clone()):this.match(c.eq)&&i?(-1===i.shorthandAssign&&(i.shorthandAssign=this.state.start),e.value=this.parseMaybeDefault(t,r,e.key.__clone())):e.value=e.key.__clone(),e.shorthand=!0,this.finishNode(e,"ObjectProperty"))}parseObjPropValue(e,t,r,n,i,s,a,o){const l=this.parseObjectMethod(e,n,i,s,a)||this.parseObjectProperty(e,t,r,s,o);return l||this.unexpected(),l}parsePropertyName(e,t){if(this.eat(c.bracketL))e.computed=!0,e.key=this.parseMaybeAssignAllowIn(),this.expect(c.bracketR);else{const r=this.state.inPropertyName;this.state.inPropertyName=!0;const n=this.state.type;e.key=n===c.num||n===c.string||n===c.bigint||n===c.decimal?this.parseExprAtom():this.parseMaybePrivateName(t),n!==c.privateName&&(e.computed=!1),this.state.inPropertyName=r}return e.key}initFunction(e,t){e.id=null,e.generator=!1,e.async=!!t}parseMethod(e,t,r,n,i,s,a=!1){this.initFunction(e,r),e.generator=!!t;const o=n;return this.scope.enter(18|(a?64:0)|(i?32:0)),this.prodParam.enter(ue(r,e.generator)),this.parseFunctionParams(e,o),this.parseFunctionBodyAndFinish(e,s,!0),this.prodParam.exit(),this.scope.exit(),e}parseArrayLike(e,t,r,n){r&&this.expectPlugin("recordAndTuple");const i=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!1;const s=this.startNode();return this.next(),s.elements=this.parseExprList(e,!r,n,s),this.state.inFSharpPipelineDirectBody=i,this.finishNode(s,r?"TupleExpression":"ArrayExpression")}parseArrowExpression(e,t,r,n){this.scope.enter(6);let i=ue(r,!1);!this.match(c.bracketL)&&this.prodParam.hasIn&&(i|=8),this.prodParam.enter(i),this.initFunction(e,r);const s=this.state.maybeInArrowParameters;return t&&(this.state.maybeInArrowParameters=!0,this.setArrowFunctionParameters(e,t,n)),this.state.maybeInArrowParameters=!1,this.parseFunctionBody(e,!0),this.prodParam.exit(),this.scope.exit(),this.state.maybeInArrowParameters=s,this.finishNode(e,"ArrowFunctionExpression")}setArrowFunctionParameters(e,t,r){e.params=this.toAssignableList(t,r,!1)}parseFunctionBodyAndFinish(e,t,r=!1){this.parseFunctionBody(e,!1,r),this.finishNode(e,t)}parseFunctionBody(e,t,r=!1){const n=t&&!this.match(c.braceL);if(this.expressionScope.enter(je()),n)e.body=this.parseMaybeAssign(),this.checkParams(e,!1,t,!1);else{const n=this.state.strict,i=this.state.labels;this.state.labels=[],this.prodParam.enter(4|this.prodParam.currentFlags()),e.body=this.parseBlock(!0,!1,(i=>{const s=!this.isSimpleParamList(e.params);if(i&&s){const t="method"!==e.kind&&"constructor"!==e.kind||!e.key?e.start:e.key.end;this.raise(t,E.IllegalLanguageModeDirective)}const a=!n&&this.state.strict;this.checkParams(e,!(this.state.strict||t||r||s),t,a),this.state.strict&&e.id&&this.checkLVal(e.id,"function name",65,void 0,void 0,a)})),this.prodParam.exit(),this.expressionScope.exit(),this.state.labels=i}}isSimpleParamList(e){for(let t=0,r=e.length;t<r;t++)if("Identifier"!==e[t].type)return!1;return!0}checkParams(e,t,r,n=!0){const i=new Set;for(const r of e.params)this.checkLVal(r,"function parameter list",5,t?null:i,void 0,n)}parseExprList(e,t,r,n){const i=[];let s=!0;for(;!this.eat(e);){if(s)s=!1;else if(this.expect(c.comma),this.match(e)){n&&this.addExtra(n,"trailingComma",this.state.lastTokStart),this.next();break}i.push(this.parseExprListItem(t,r))}return i}parseExprListItem(e,t,r,n){let i;if(this.match(c.comma))e||this.raise(this.state.pos,E.UnexpectedToken,","),i=null;else if(this.match(c.ellipsis)){const e=this.state.start,n=this.state.startLoc;i=this.parseParenItem(this.parseSpread(t,r),e,n)}else if(this.match(c.question)){this.expectPlugin("partialApplication"),n||this.raise(this.state.start,E.UnexpectedArgumentPlaceholder);const e=this.startNode();this.next(),i=this.finishNode(e,"ArgumentPlaceholder")}else i=this.parseMaybeAssignAllowIn(t,this.parseParenItem,r);return i}parseIdentifier(e){const t=this.startNode(),r=this.parseIdentifierName(t.start,e);return this.createIdentifier(t,r)}createIdentifier(e,t){return e.name=t,e.loc.identifierName=t,this.finishNode(e,"Identifier")}parseIdentifierName(e,t){let r;const{start:n,type:i}=this.state;if(i===c.name)r=this.state.value;else{if(!i.keyword)throw this.unexpected();{r=i.keyword;const e=this.curContext();i!==c._class&&i!==c._function||e!==O.functionStatement&&e!==O.functionExpression||this.state.context.pop()}}return t?this.state.type=c.name:this.checkReservedWord(r,n,!!i.keyword,!1),this.next(),r}checkReservedWord(e,t,r,n){if(this.prodParam.hasYield&&"yield"===e)this.raise(t,E.YieldBindingIdentifier);else{if("await"===e){if(this.prodParam.hasAwait)return void this.raise(t,E.AwaitBindingIdentifier);if(this.scope.inStaticBlock&&!this.scope.inNonArrowFunction)return void this.raise(t,E.AwaitBindingIdentifierInStaticBlock);this.expressionScope.recordAsyncArrowParametersError(t,E.AwaitBindingIdentifier)}!this.scope.inClass||this.scope.inNonArrowFunction||"arguments"!==e?r&&Y(e)?this.raise(t,E.UnexpectedKeyword,e):(this.state.strict?n?q:V:U)(e,this.inModule)&&this.raise(t,E.UnexpectedReservedWord,e):this.raise(t,E.ArgumentsInClass)}}isAwaitAllowed(){return!!this.prodParam.hasAwait||!(!this.options.allowAwaitOutsideFunction||this.scope.inFunction)}parseAwait(e,t){const r=this.startNodeAt(e,t);return this.expressionScope.recordParameterInitializerError(r.start,E.AwaitExpressionFormalParameter),this.eat(c.star)&&this.raise(r.start,E.ObsoleteAwaitStar),this.scope.inFunction||this.options.allowAwaitOutsideFunction||(this.isAmbiguousAwait()?this.ambiguousScriptDifferentAst=!0:this.sawUnambiguousESM=!0),this.state.soloAwait||(r.argument=this.parseMaybeUnary(null,!0)),this.finishNode(r,"AwaitExpression")}isAmbiguousAwait(){return this.hasPrecedingLineBreak()||this.match(c.plusMin)||this.match(c.parenL)||this.match(c.bracketL)||this.match(c.backQuote)||this.match(c.regexp)||this.match(c.slash)||this.hasPlugin("v8intrinsic")&&this.match(c.modulo)}parseYield(){const e=this.startNode();return this.expressionScope.recordParameterInitializerError(e.start,E.YieldInParameter),this.next(),this.match(c.semi)||!this.match(c.star)&&!this.state.type.startsExpr||this.hasPrecedingLineBreak()?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(c.star),e.argument=this.parseMaybeAssign()),this.finishNode(e,"YieldExpression")}checkPipelineAtInfixOperator(e,t){"smart"===this.getPluginOption("pipelineOperator","proposal")&&"SequenceExpression"===e.type&&this.raise(t,E.PipelineHeadSequenceExpression)}parseSmartPipelineBody(e,t,r){return this.checkSmartPipelineBodyEarlyErrors(e,t),this.parseSmartPipelineBodyInStyle(e,t,r)}checkSmartPipelineBodyEarlyErrors(e,t){if(this.match(c.arrow))throw this.raise(this.state.start,E.PipelineBodyNoArrow);"SequenceExpression"===e.type&&this.raise(t,E.PipelineBodySequenceExpression)}parseSmartPipelineBodyInStyle(e,t,r){const n=this.startNodeAt(t,r),i=this.isSimpleReference(e);return i?n.callee=e:(this.topicReferenceWasUsedInCurrentTopicContext()||this.raise(t,E.PipelineTopicUnused),n.expression=e),this.finishNode(n,i?"PipelineBareFunction":"PipelineTopicExpression")}isSimpleReference(e){switch(e.type){case"MemberExpression":return!e.computed&&this.isSimpleReference(e.object);case"Identifier":return!0;default:return!1}}withTopicPermittingContext(e){const t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:1,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}withTopicForbiddingContext(e){const t=this.state.topicContext;this.state.topicContext={maxNumOfResolvableTopics:0,maxTopicIndex:null};try{return e()}finally{this.state.topicContext=t}}withSoloAwaitPermittingContext(e){const t=this.state.soloAwait;this.state.soloAwait=!0;try{return e()}finally{this.state.soloAwait=t}}allowInAnd(e){const t=this.prodParam.currentFlags();if(8&~t){this.prodParam.enter(8|t);try{return e()}finally{this.prodParam.exit()}}return e()}disallowInAnd(e){const t=this.prodParam.currentFlags();if(8&t){this.prodParam.enter(-9&t);try{return e()}finally{this.prodParam.exit()}}return e()}registerTopicReference(){this.state.topicContext.maxTopicIndex=0}primaryTopicReferenceIsAllowedInCurrentTopicContext(){return this.state.topicContext.maxNumOfResolvableTopics>=1}topicReferenceWasUsedInCurrentTopicContext(){return null!=this.state.topicContext.maxTopicIndex&&this.state.topicContext.maxTopicIndex>=0}parseFSharpPipelineBody(e){const t=this.state.start,r=this.state.startLoc;this.state.potentialArrowAt=this.state.start;const n=this.state.inFSharpPipelineDirectBody;this.state.inFSharpPipelineDirectBody=!0;const i=this.parseExprOp(this.parseMaybeUnary(),t,r,e);return this.state.inFSharpPipelineDirectBody=n,i}parseModuleExpression(){this.expectPlugin("moduleBlocks");const e=this.startNode();this.next(),this.eat(c.braceL);const t=this.initializeScopes(!0);this.enterInitialScopes();const r=this.startNode();try{e.body=this.parseProgram(r,c.braceR,"module")}finally{t()}return this.eat(c.braceR),this.finishNode(e,"ModuleExpression")}}const Ye={kind:"loop"},We={kind:"switch"},Xe=/[\uD800-\uDFFF]/u;class Je extends qe{parseTopLevel(e,t){return e.program=this.parseProgram(t),e.comments=this.state.comments,this.options.tokens&&(e.tokens=function(e){for(let t=0;t<e.length;t++){const r=e[t];if(r.type===c.privateName){const{loc:n,start:i,value:s,end:a}=r,o=i+1,l=new y(n.start.line,n.start.column+1);e.splice(t,1,new Ne({type:c.hash,value:"#",start:i,end:o,startLoc:n.start,endLoc:l}),new Ne({type:c.name,value:s,start:o,end:a,startLoc:l,endLoc:n.end}))}}return e}(this.tokens)),this.finishNode(e,"File")}parseProgram(e,t=c.eof,r=this.options.sourceType){if(e.sourceType=r,e.interpreter=this.parseInterpreterDirective(),this.parseBlockBody(e,!0,!0,t),this.inModule&&!this.options.allowUndeclaredExports&&this.scope.undefinedExports.size>0)for(const[e]of Array.from(this.scope.undefinedExports)){const t=this.scope.undefinedExports.get(e);this.raise(t,E.ModuleExportUndefined,e)}return this.finishNode(e,"Program")}stmtToDirective(e){const t=e.expression,r=this.startNodeAt(t.start,t.loc.start),n=this.startNodeAt(e.start,e.loc.start),i=this.input.slice(t.start,t.end),s=r.value=i.slice(1,-1);return this.addExtra(r,"raw",i),this.addExtra(r,"rawValue",s),n.value=this.finishNodeAt(r,"DirectiveLiteral",t.end,t.loc.end),this.finishNodeAt(n,"Directive",e.end,e.loc.end)}parseInterpreterDirective(){if(!this.match(c.interpreterDirective))return null;const e=this.startNode();return e.value=this.state.value,this.next(),this.finishNode(e,"InterpreterDirective")}isLet(e){return!!this.isContextual("let")&&this.isLetKeyword(e)}isLetKeyword(e){const t=this.nextTokenStart(),r=this.input.charCodeAt(t);if(92===r||91===r)return!0;if(e)return!1;if(123===r)return!0;if(M(r)){let e=t+1;for(;j(this.input.charCodeAt(e));)++e;const r=this.input.slice(t,e);if(!W.test(r))return!0}return!1}parseStatement(e,t){return this.match(c.at)&&this.parseDecorators(!0),this.parseStatementContent(e,t)}parseStatementContent(e,t){let r=this.state.type;const n=this.startNode();let i;switch(this.isLet(e)&&(r=c._var,i="let"),r){case c._break:case c._continue:return this.parseBreakContinueStatement(n,r.keyword);case c._debugger:return this.parseDebuggerStatement(n);case c._do:return this.parseDoStatement(n);case c._for:return this.parseForStatement(n);case c._function:if(46===this.lookaheadCharCode())break;return e&&(this.state.strict?this.raise(this.state.start,E.StrictFunction):"if"!==e&&"label"!==e&&this.raise(this.state.start,E.SloppyFunction)),this.parseFunctionStatement(n,!1,!e);case c._class:return e&&this.unexpected(),this.parseClass(n,!0);case c._if:return this.parseIfStatement(n);case c._return:return this.parseReturnStatement(n);case c._switch:return this.parseSwitchStatement(n);case c._throw:return this.parseThrowStatement(n);case c._try:return this.parseTryStatement(n);case c._const:case c._var:return i=i||this.state.value,e&&"var"!==i&&this.raise(this.state.start,E.UnexpectedLexicalDeclaration),this.parseVarStatement(n,i);case c._while:return this.parseWhileStatement(n);case c._with:return this.parseWithStatement(n);case c.braceL:return this.parseBlock();case c.semi:return this.parseEmptyStatement(n);case c._import:{const e=this.lookaheadCharCode();if(40===e||46===e)break}case c._export:{let e;return this.options.allowImportExportEverywhere||t||this.raise(this.state.start,E.UnexpectedImportExport),this.next(),r===c._import?(e=this.parseImport(n),"ImportDeclaration"!==e.type||e.importKind&&"value"!==e.importKind||(this.sawUnambiguousESM=!0)):(e=this.parseExport(n),("ExportNamedDeclaration"!==e.type||e.exportKind&&"value"!==e.exportKind)&&("ExportAllDeclaration"!==e.type||e.exportKind&&"value"!==e.exportKind)&&"ExportDefaultDeclaration"!==e.type||(this.sawUnambiguousESM=!0)),this.assertModuleNodeAllowed(n),e}default:if(this.isAsyncFunction())return e&&this.raise(this.state.start,E.AsyncFunctionInSingleStatementContext),this.next(),this.parseFunctionStatement(n,!0,!e)}const s=this.state.value,a=this.parseExpression();return r===c.name&&"Identifier"===a.type&&this.eat(c.colon)?this.parseLabeledStatement(n,s,a,e):this.parseExpressionStatement(n,a)}assertModuleNodeAllowed(e){this.options.allowImportExportEverywhere||this.inModule||this.raise(e.start,P.ImportOutsideModule)}takeDecorators(e){const t=this.state.decoratorStack[this.state.decoratorStack.length-1];t.length&&(e.decorators=t,this.resetStartLocationFromNode(e,t[0]),this.state.decoratorStack[this.state.decoratorStack.length-1]=[])}canHaveLeadingDecorator(){return this.match(c._class)}parseDecorators(e){const t=this.state.decoratorStack[this.state.decoratorStack.length-1];for(;this.match(c.at);){const e=this.parseDecorator();t.push(e)}if(this.match(c._export))e||this.unexpected(),this.hasPlugin("decorators")&&!this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,E.DecoratorExportClass);else if(!this.canHaveLeadingDecorator())throw this.raise(this.state.start,E.UnexpectedLeadingDecorator)}parseDecorator(){this.expectOnePlugin(["decorators-legacy","decorators"]);const e=this.startNode();if(this.next(),this.hasPlugin("decorators")){this.state.decoratorStack.push([]);const t=this.state.start,r=this.state.startLoc;let n;if(this.eat(c.parenL))n=this.parseExpression(),this.expect(c.parenR);else for(n=this.parseIdentifier(!1);this.eat(c.dot);){const e=this.startNodeAt(t,r);e.object=n,e.property=this.parseIdentifier(!0),e.computed=!1,n=this.finishNode(e,"MemberExpression")}e.expression=this.parseMaybeDecoratorArguments(n),this.state.decoratorStack.pop()}else e.expression=this.parseExprSubscripts();return this.finishNode(e,"Decorator")}parseMaybeDecoratorArguments(e){if(this.eat(c.parenL)){const t=this.startNodeAtNode(e);return t.callee=e,t.arguments=this.parseCallExpressionArguments(c.parenR,!1),this.toReferencedList(t.arguments),this.finishNode(t,"CallExpression")}return e}parseBreakContinueStatement(e,t){const r="break"===t;return this.next(),this.isLineTerminator()?e.label=null:(e.label=this.parseIdentifier(),this.semicolon()),this.verifyBreakContinue(e,t),this.finishNode(e,r?"BreakStatement":"ContinueStatement")}verifyBreakContinue(e,t){const r="break"===t;let n;for(n=0;n<this.state.labels.length;++n){const t=this.state.labels[n];if(null==e.label||t.name===e.label.name){if(null!=t.kind&&(r||"loop"===t.kind))break;if(e.label&&r)break}}n===this.state.labels.length&&this.raise(e.start,E.IllegalBreakContinue,t)}parseDebuggerStatement(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")}parseHeaderExpression(){this.expect(c.parenL);const e=this.parseExpression();return this.expect(c.parenR),e}parseDoStatement(e){return this.next(),this.state.labels.push(Ye),e.body=this.withTopicForbiddingContext((()=>this.parseStatement("do"))),this.state.labels.pop(),this.expect(c._while),e.test=this.parseHeaderExpression(),this.eat(c.semi),this.finishNode(e,"DoWhileStatement")}parseForStatement(e){this.next(),this.state.labels.push(Ye);let t=-1;if(this.isAwaitAllowed()&&this.eatContextual("await")&&(t=this.state.lastTokStart),this.scope.enter(0),this.expect(c.parenL),this.match(c.semi))return t>-1&&this.unexpected(t),this.parseFor(e,null);const r=this.isContextual("let"),n=r&&this.isLetKeyword();if(this.match(c._var)||this.match(c._const)||n){const r=this.startNode(),i=n?"let":this.state.value;return this.next(),this.parseVar(r,!0,i),this.finishNode(r,"VariableDeclaration"),(this.match(c._in)||this.isContextual("of"))&&1===r.declarations.length?this.parseForIn(e,r,t):(t>-1&&this.unexpected(t),this.parseFor(e,r))}const i=this.match(c.name)&&!this.state.containsEsc,s=new Be,a=this.parseExpression(!0,s),o=this.isContextual("of");if(o&&(r?this.raise(a.start,E.ForOfLet):-1===t&&i&&"Identifier"===a.type&&"async"===a.name&&this.raise(a.start,E.ForOfAsync)),o||this.match(c._in)){this.toAssignable(a,!0);const r=o?"for-of statement":"for-in statement";return this.checkLVal(a,r),this.parseForIn(e,a,t)}return this.checkExpressionErrors(s,!0),t>-1&&this.unexpected(t),this.parseFor(e,a)}parseFunctionStatement(e,t,r){return this.next(),this.parseFunction(e,1|(r?0:2),t)}parseIfStatement(e){return this.next(),e.test=this.parseHeaderExpression(),e.consequent=this.parseStatement("if"),e.alternate=this.eat(c._else)?this.parseStatement("if"):null,this.finishNode(e,"IfStatement")}parseReturnStatement(e){return this.prodParam.hasReturn||this.options.allowReturnOutsideFunction||this.raise(this.state.start,E.IllegalReturn),this.next(),this.isLineTerminator()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")}parseSwitchStatement(e){this.next(),e.discriminant=this.parseHeaderExpression();const t=e.cases=[];let r;this.expect(c.braceL),this.state.labels.push(We),this.scope.enter(0);for(let e;!this.match(c.braceR);)if(this.match(c._case)||this.match(c._default)){const n=this.match(c._case);r&&this.finishNode(r,"SwitchCase"),t.push(r=this.startNode()),r.consequent=[],this.next(),n?r.test=this.parseExpression():(e&&this.raise(this.state.lastTokStart,E.MultipleDefaultsInSwitch),e=!0,r.test=null),this.expect(c.colon)}else r?r.consequent.push(this.parseStatement(null)):this.unexpected();return this.scope.exit(),r&&this.finishNode(r,"SwitchCase"),this.next(),this.state.labels.pop(),this.finishNode(e,"SwitchStatement")}parseThrowStatement(e){return this.next(),this.hasPrecedingLineBreak()&&this.raise(this.state.lastTokEnd,E.NewlineAfterThrow),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")}parseCatchClauseParam(){const e=this.parseBindingAtom(),t="Identifier"===e.type;return this.scope.enter(t?8:0),this.checkLVal(e,"catch clause",9),e}parseTryStatement(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.match(c._catch)){const t=this.startNode();this.next(),this.match(c.parenL)?(this.expect(c.parenL),t.param=this.parseCatchClauseParam(),this.expect(c.parenR)):(t.param=null,this.scope.enter(0)),t.body=this.withTopicForbiddingContext((()=>this.parseBlock(!1,!1))),this.scope.exit(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(c._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,E.NoCatchOrFinally),this.finishNode(e,"TryStatement")}parseVarStatement(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")}parseWhileStatement(e){return this.next(),e.test=this.parseHeaderExpression(),this.state.labels.push(Ye),e.body=this.withTopicForbiddingContext((()=>this.parseStatement("while"))),this.state.labels.pop(),this.finishNode(e,"WhileStatement")}parseWithStatement(e){return this.state.strict&&this.raise(this.state.start,E.StrictWith),this.next(),e.object=this.parseHeaderExpression(),e.body=this.withTopicForbiddingContext((()=>this.parseStatement("with"))),this.finishNode(e,"WithStatement")}parseEmptyStatement(e){return this.next(),this.finishNode(e,"EmptyStatement")}parseLabeledStatement(e,t,r,n){for(const e of this.state.labels)e.name===t&&this.raise(r.start,E.LabelRedeclaration,t);const i=this.state.type.isLoop?"loop":this.match(c._switch)?"switch":null;for(let t=this.state.labels.length-1;t>=0;t--){const r=this.state.labels[t];if(r.statementStart!==e.start)break;r.statementStart=this.state.start,r.kind=i}return this.state.labels.push({name:t,kind:i,statementStart:this.state.start}),e.body=this.parseStatement(n?-1===n.indexOf("label")?n+"label":n:"label"),this.state.labels.pop(),e.label=r,this.finishNode(e,"LabeledStatement")}parseExpressionStatement(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")}parseBlock(e=!1,t=!0,r){const n=this.startNode();return e&&this.state.strictErrors.clear(),this.expect(c.braceL),t&&this.scope.enter(0),this.parseBlockBody(n,e,!1,c.braceR,r),t&&this.scope.exit(),this.finishNode(n,"BlockStatement")}isValidDirective(e){return"ExpressionStatement"===e.type&&"StringLiteral"===e.expression.type&&!e.expression.extra.parenthesized}parseBlockBody(e,t,r,n,i){const s=e.body=[],a=e.directives=[];this.parseBlockOrModuleBlockBody(s,t?a:void 0,r,n,i)}parseBlockOrModuleBlockBody(e,t,r,n,i){const s=this.state.strict;let a=!1,o=!1;for(;!this.match(n);){const n=this.parseStatement(null,r);if(t&&!o){if(this.isValidDirective(n)){const e=this.stmtToDirective(n);t.push(e),a||"use strict"!==e.value.value||(a=!0,this.setStrict(!0));continue}o=!0,this.state.strictErrors.clear()}e.push(n)}i&&i.call(this,a),s||this.setStrict(!1),this.next()}parseFor(e,t){return e.init=t,this.semicolon(!1),e.test=this.match(c.semi)?null:this.parseExpression(),this.semicolon(!1),e.update=this.match(c.parenR)?null:this.parseExpression(),this.expect(c.parenR),e.body=this.withTopicForbiddingContext((()=>this.parseStatement("for"))),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,"ForStatement")}parseForIn(e,t,r){const n=this.match(c._in);return this.next(),n?r>-1&&this.unexpected(r):e.await=r>-1,"VariableDeclaration"!==t.type||null==t.declarations[0].init||n&&!this.state.strict&&"var"===t.kind&&"Identifier"===t.declarations[0].id.type?"AssignmentPattern"===t.type&&this.raise(t.start,E.InvalidLhs,"for-loop"):this.raise(t.start,E.ForInOfLoopInitializer,n?"for-in":"for-of"),e.left=t,e.right=n?this.parseExpression():this.parseMaybeAssignAllowIn(),this.expect(c.parenR),e.body=this.withTopicForbiddingContext((()=>this.parseStatement("for"))),this.scope.exit(),this.state.labels.pop(),this.finishNode(e,n?"ForInStatement":"ForOfStatement")}parseVar(e,t,r){const n=e.declarations=[],i=this.hasPlugin("typescript");for(e.kind=r;;){const e=this.startNode();if(this.parseVarId(e,r),this.eat(c.eq)?e.init=t?this.parseMaybeAssignDisallowIn():this.parseMaybeAssignAllowIn():("const"!==r||this.match(c._in)||this.isContextual("of")?"Identifier"===e.id.type||t&&(this.match(c._in)||this.isContextual("of"))||this.raise(this.state.lastTokEnd,E.DeclarationMissingInitializer,"Complex binding patterns"):i||this.raise(this.state.lastTokEnd,E.DeclarationMissingInitializer,"Const declarations"),e.init=null),n.push(this.finishNode(e,"VariableDeclarator")),!this.eat(c.comma))break}return e}parseVarId(e,t){e.id=this.parseBindingAtom(),this.checkLVal(e.id,"variable declaration","var"===t?5:9,void 0,"var"!==t)}parseFunction(e,t=0,r=!1){const n=1&t,i=2&t,s=!(!n||4&t);this.initFunction(e,r),this.match(c.star)&&i&&this.raise(this.state.start,E.GeneratorInSingleStatementContext),e.generator=this.eat(c.star),n&&(e.id=this.parseFunctionId(s));const a=this.state.maybeInArrowParameters;return this.state.maybeInArrowParameters=!1,this.scope.enter(2),this.prodParam.enter(ue(r,e.generator)),n||(e.id=this.parseFunctionId()),this.parseFunctionParams(e,!1),this.withTopicForbiddingContext((()=>{this.parseFunctionBodyAndFinish(e,n?"FunctionDeclaration":"FunctionExpression")})),this.prodParam.exit(),this.scope.exit(),n&&!i&&this.registerFunctionStatementId(e),this.state.maybeInArrowParameters=a,e}parseFunctionId(e){return e||this.match(c.name)?this.parseIdentifier():null}parseFunctionParams(e,t){this.expect(c.parenL),this.expressionScope.enter(new _e(3)),e.params=this.parseBindingList(c.parenR,41,!1,t),this.expressionScope.exit()}registerFunctionStatementId(e){e.id&&this.scope.declareName(e.id.name,this.state.strict||e.generator||e.async?this.scope.treatFunctionsAsVar?5:9:17,e.id.start)}parseClass(e,t,r){this.next(),this.takeDecorators(e);const n=this.state.strict;return this.state.strict=!0,this.parseClassId(e,t,r),this.parseClassSuper(e),e.body=this.parseClassBody(!!e.superClass,n),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")}isClassProperty(){return this.match(c.eq)||this.match(c.semi)||this.match(c.braceR)}isClassMethod(){return this.match(c.parenL)}isNonstaticConstructor(e){return!(e.computed||e.static||"constructor"!==e.key.name&&"constructor"!==e.key.value)}parseClassBody(e,t){this.classScope.enter();const r={hadConstructor:!1,hadSuperClass:e};let n=[];const i=this.startNode();if(i.body=[],this.expect(c.braceL),this.withTopicForbiddingContext((()=>{for(;!this.match(c.braceR);){if(this.eat(c.semi)){if(n.length>0)throw this.raise(this.state.lastTokEnd,E.DecoratorSemicolon);continue}if(this.match(c.at)){n.push(this.parseDecorator());continue}const e=this.startNode();n.length&&(e.decorators=n,this.resetStartLocationFromNode(e,n[0]),n=[]),this.parseClassMember(i,e,r),"constructor"===e.kind&&e.decorators&&e.decorators.length>0&&this.raise(e.start,E.DecoratorConstructor)}})),this.state.strict=t,this.next(),n.length)throw this.raise(this.state.start,E.TrailingDecorator);return this.classScope.exit(),this.finishNode(i,"ClassBody")}parseClassMemberFromModifier(e,t){const r=this.parseIdentifier(!0);if(this.isClassMethod()){const n=t;return n.kind="method",n.computed=!1,n.key=r,n.static=!1,this.pushClassMethod(e,n,!1,!1,!1,!1),!0}if(this.isClassProperty()){const n=t;return n.computed=!1,n.key=r,n.static=!1,e.body.push(this.parseClassProperty(n)),!0}return!1}parseClassMember(e,t,r){const n=this.isContextual("static");if(n){if(this.parseClassMemberFromModifier(e,t))return;if(this.eat(c.braceL))return void this.parseClassStaticBlock(e,t)}this.parseClassMemberWithIsStatic(e,t,r,n)}parseClassMemberWithIsStatic(e,t,r,n){const i=t,s=t,a=t,o=t,l=i,p=i;if(t.static=n,this.eat(c.star)){l.kind="method";const t=this.match(c.privateName);return this.parseClassElementName(l),t?void this.pushClassPrivateMethod(e,s,!0,!1):(this.isNonstaticConstructor(i)&&this.raise(i.key.start,E.ConstructorIsGenerator),void this.pushClassMethod(e,i,!0,!1,!1,!1))}const u=this.state.containsEsc,d=this.match(c.privateName),h=this.parseClassElementName(t),f="Identifier"===h.type,m=this.state.start;if(this.parsePostMemberNameModifiers(p),this.isClassMethod()){if(l.kind="method",d)return void this.pushClassPrivateMethod(e,s,!1,!1);const n=this.isNonstaticConstructor(i);let a=!1;n&&(i.kind="constructor",r.hadConstructor&&!this.hasPlugin("typescript")&&this.raise(h.start,E.DuplicateConstructor),n&&this.hasPlugin("typescript")&&t.override&&this.raise(h.start,E.OverrideOnConstructor),r.hadConstructor=!0,a=r.hadSuperClass),this.pushClassMethod(e,i,!1,!1,n,a)}else if(this.isClassProperty())d?this.pushClassPrivateProperty(e,o):this.pushClassProperty(e,a);else if(!f||"async"!==h.name||u||this.isLineTerminator())if(!f||"get"!==h.name&&"set"!==h.name||u||this.match(c.star)&&this.isLineTerminator())this.isLineTerminator()?d?this.pushClassPrivateProperty(e,o):this.pushClassProperty(e,a):this.unexpected();else{l.kind=h.name;const t=this.match(c.privateName);this.parseClassElementName(i),t?this.pushClassPrivateMethod(e,s,!1,!1):(this.isNonstaticConstructor(i)&&this.raise(i.key.start,E.ConstructorIsAccessor),this.pushClassMethod(e,i,!1,!1,!1,!1)),this.checkGetterSetterParams(i)}else{const t=this.eat(c.star);p.optional&&this.unexpected(m),l.kind="method";const r=this.match(c.privateName);this.parseClassElementName(l),this.parsePostMemberNameModifiers(p),r?this.pushClassPrivateMethod(e,s,t,!0):(this.isNonstaticConstructor(i)&&this.raise(i.key.start,E.ConstructorIsAsync),this.pushClassMethod(e,i,t,!0,!1,!1))}}parseClassElementName(e){const{type:t,value:r,start:n}=this.state;return t!==c.name&&t!==c.string||!e.static||"prototype"!==r||this.raise(n,E.StaticPrototype),t===c.privateName&&"constructor"===r&&this.raise(n,E.ConstructorClassPrivateField),this.parsePropertyName(e,!0)}parseClassStaticBlock(e,t){var r;this.expectPlugin("classStaticBlock",t.start),this.scope.enter(208);const n=this.state.labels;this.state.labels=[],this.prodParam.enter(0);const i=t.body=[];this.parseBlockOrModuleBlockBody(i,void 0,!1,c.braceR),this.prodParam.exit(),this.scope.exit(),this.state.labels=n,e.body.push(this.finishNode(t,"StaticBlock")),null!=(r=t.decorators)&&r.length&&this.raise(t.start,E.DecoratorStaticBlock)}pushClassProperty(e,t){t.computed||"constructor"!==t.key.name&&"constructor"!==t.key.value||this.raise(t.key.start,E.ConstructorClassField),e.body.push(this.parseClassProperty(t))}pushClassPrivateProperty(e,t){const r=this.parseClassPrivateProperty(t);e.body.push(r),this.classScope.declarePrivateName(this.getPrivateNameSV(r.key),0,r.key.start)}pushClassMethod(e,t,r,n,i,s){e.body.push(this.parseMethod(t,r,n,i,s,"ClassMethod",!0))}pushClassPrivateMethod(e,t,r,n){const i=this.parseMethod(t,r,n,!1,!1,"ClassPrivateMethod",!0);e.body.push(i);const s="get"===i.kind?i.static?6:2:"set"===i.kind?i.static?5:1:0;this.classScope.declarePrivateName(this.getPrivateNameSV(i.key),s,i.key.start)}parsePostMemberNameModifiers(e){}parseClassPrivateProperty(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassPrivateProperty")}parseClassProperty(e){return this.parseInitializer(e),this.semicolon(),this.finishNode(e,"ClassProperty")}parseInitializer(e){this.scope.enter(80),this.expressionScope.enter(je()),this.prodParam.enter(0),e.value=this.eat(c.eq)?this.parseMaybeAssignAllowIn():null,this.expressionScope.exit(),this.prodParam.exit(),this.scope.exit()}parseClassId(e,t,r,n=139){this.match(c.name)?(e.id=this.parseIdentifier(),t&&this.checkLVal(e.id,"class name",n)):r||!t?e.id=null:this.unexpected(null,E.MissingClassName)}parseClassSuper(e){e.superClass=this.eat(c._extends)?this.parseExprSubscripts():null}parseExport(e){const t=this.maybeParseExportDefaultSpecifier(e),r=!t||this.eat(c.comma),n=r&&this.eatExportStar(e),i=n&&this.maybeParseExportNamespaceSpecifier(e),s=r&&(!i||this.eat(c.comma)),a=t||n;if(n&&!i)return t&&this.unexpected(),this.parseExportFrom(e,!0),this.finishNode(e,"ExportAllDeclaration");const o=this.maybeParseExportNamedSpecifiers(e);if(t&&r&&!n&&!o||i&&s&&!o)throw this.unexpected(null,c.braceL);let l;if(a||o?(l=!1,this.parseExportFrom(e,a)):l=this.maybeParseExportDeclaration(e),a||o||l)return this.checkExport(e,!0,!1,!!e.source),this.finishNode(e,"ExportNamedDeclaration");if(this.eat(c._default))return e.declaration=this.parseExportDefaultExpression(),this.checkExport(e,!0,!0),this.finishNode(e,"ExportDefaultDeclaration");throw this.unexpected(null,c.braceL)}eatExportStar(e){return this.eat(c.star)}maybeParseExportDefaultSpecifier(e){if(this.isExportDefaultSpecifier()){this.expectPlugin("exportDefaultFrom");const t=this.startNode();return t.exported=this.parseIdentifier(!0),e.specifiers=[this.finishNode(t,"ExportDefaultSpecifier")],!0}return!1}maybeParseExportNamespaceSpecifier(e){if(this.isContextual("as")){e.specifiers||(e.specifiers=[]);const t=this.startNodeAt(this.state.lastTokStart,this.state.lastTokStartLoc);return this.next(),t.exported=this.parseModuleExportName(),e.specifiers.push(this.finishNode(t,"ExportNamespaceSpecifier")),!0}return!1}maybeParseExportNamedSpecifiers(e){return!!this.match(c.braceL)&&(e.specifiers||(e.specifiers=[]),e.specifiers.push(...this.parseExportSpecifiers()),e.source=null,e.declaration=null,!0)}maybeParseExportDeclaration(e){return!!this.shouldParseExportDeclaration()&&(e.specifiers=[],e.source=null,e.declaration=this.parseExportDeclaration(e),!0)}isAsyncFunction(){if(!this.isContextual("async"))return!1;const e=this.nextTokenStart();return!u.test(this.input.slice(this.state.pos,e))&&this.isUnparsedContextual(e,"function")}parseExportDefaultExpression(){const e=this.startNode(),t=this.isAsyncFunction();if(this.match(c._function)||t)return this.next(),t&&this.next(),this.parseFunction(e,5,t);if(this.match(c._class))return this.parseClass(e,!0,!0);if(this.match(c.at))return this.hasPlugin("decorators")&&this.getPluginOption("decorators","decoratorsBeforeExport")&&this.raise(this.state.start,E.DecoratorBeforeExport),this.parseDecorators(!1),this.parseClass(e,!0,!0);if(this.match(c._const)||this.match(c._var)||this.isLet())throw this.raise(this.state.start,E.UnsupportedDefaultExport);{const e=this.parseMaybeAssignAllowIn();return this.semicolon(),e}}parseExportDeclaration(e){return this.parseStatement(null)}isExportDefaultSpecifier(){if(this.match(c.name)){const e=this.state.value;if("async"===e&&!this.state.containsEsc||"let"===e)return!1;if(("type"===e||"interface"===e)&&!this.state.containsEsc){const e=this.lookahead();if(e.type===c.name&&"from"!==e.value||e.type===c.braceL)return this.expectOnePlugin(["flow","typescript"]),!1}}else if(!this.match(c._default))return!1;const e=this.nextTokenStart(),t=this.isUnparsedContextual(e,"from");if(44===this.input.charCodeAt(e)||this.match(c.name)&&t)return!0;if(this.match(c._default)&&t){const t=this.input.charCodeAt(this.nextTokenStartSince(e+4));return 34===t||39===t}return!1}parseExportFrom(e,t){if(this.eatContextual("from")){e.source=this.parseImportSource(),this.checkExport(e);const t=this.maybeParseImportAssertions();t&&(e.assertions=t)}else t?this.unexpected():e.source=null;this.semicolon()}shouldParseExportDeclaration(){if(this.match(c.at)&&(this.expectOnePlugin(["decorators","decorators-legacy"]),this.hasPlugin("decorators"))){if(!this.getPluginOption("decorators","decoratorsBeforeExport"))return!0;this.unexpected(this.state.start,E.DecoratorBeforeExport)}return"var"===this.state.type.keyword||"const"===this.state.type.keyword||"function"===this.state.type.keyword||"class"===this.state.type.keyword||this.isLet()||this.isAsyncFunction()}checkExport(e,t,r,n){if(t)if(r){if(this.checkDuplicateExports(e,"default"),this.hasPlugin("exportDefaultFrom")){var i;const t=e.declaration;"Identifier"!==t.type||"from"!==t.name||t.end-t.start!=4||null!=(i=t.extra)&&i.parenthesized||this.raise(t.start,E.ExportDefaultFromAsIdentifier)}}else if(e.specifiers&&e.specifiers.length)for(const t of e.specifiers){const{exported:e}=t,r="Identifier"===e.type?e.name:e.value;if(this.checkDuplicateExports(t,r),!n&&t.local){const{local:e}=t;"StringLiteral"===e.type?this.raise(t.start,E.ExportBindingIsString,e.value,r):(this.checkReservedWord(e.name,e.start,!0,!1),this.scope.checkLocalExport(e))}}else if(e.declaration)if("FunctionDeclaration"===e.declaration.type||"ClassDeclaration"===e.declaration.type){const t=e.declaration.id;if(!t)throw new Error("Assertion failure");this.checkDuplicateExports(e,t.name)}else if("VariableDeclaration"===e.declaration.type)for(const t of e.declaration.declarations)this.checkDeclaration(t.id);if(this.state.decoratorStack[this.state.decoratorStack.length-1].length)throw this.raise(e.start,E.UnsupportedDecoratorExport)}checkDeclaration(e){if("Identifier"===e.type)this.checkDuplicateExports(e,e.name);else if("ObjectPattern"===e.type)for(const t of e.properties)this.checkDeclaration(t);else if("ArrayPattern"===e.type)for(const t of e.elements)t&&this.checkDeclaration(t);else"ObjectProperty"===e.type?this.checkDeclaration(e.value):"RestElement"===e.type?this.checkDeclaration(e.argument):"AssignmentPattern"===e.type&&this.checkDeclaration(e.left)}checkDuplicateExports(e,t){this.state.exportedIdentifiers.indexOf(t)>-1&&this.raise(e.start,"default"===t?E.DuplicateDefaultExport:E.DuplicateExport,t),this.state.exportedIdentifiers.push(t)}parseExportSpecifiers(){const e=[];let t=!0;for(this.expect(c.braceL);!this.eat(c.braceR);){if(t)t=!1;else if(this.expect(c.comma),this.eat(c.braceR))break;const r=this.startNode();r.local=this.parseModuleExportName(),r.exported=this.eatContextual("as")?this.parseModuleExportName():r.local.__clone(),e.push(this.finishNode(r,"ExportSpecifier"))}return e}parseModuleExportName(){if(this.match(c.string)){const e=this.parseLiteral(this.state.value,"StringLiteral"),t=e.value.match(Xe);return t&&this.raise(e.start,E.ModuleExportNameHasLoneSurrogate,t[0].charCodeAt(0).toString(16)),e}return this.parseIdentifier(!0)}parseImport(e){if(e.specifiers=[],!this.match(c.string)){const t=!this.maybeParseDefaultImportSpecifier(e)||this.eat(c.comma),r=t&&this.maybeParseStarImportSpecifier(e);t&&!r&&this.parseNamedImportSpecifiers(e),this.expectContextual("from")}e.source=this.parseImportSource();const t=this.maybeParseImportAssertions();if(t)e.assertions=t;else{const t=this.maybeParseModuleAttributes();t&&(e.attributes=t)}return this.semicolon(),this.finishNode(e,"ImportDeclaration")}parseImportSource(){return this.match(c.string)||this.unexpected(),this.parseExprAtom()}shouldParseDefaultImport(e){return this.match(c.name)}parseImportSpecifierLocal(e,t,r,n){t.local=this.parseIdentifier(),this.checkLVal(t.local,n,9),e.specifiers.push(this.finishNode(t,r))}parseAssertEntries(){const e=[],t=new Set;do{if(this.match(c.braceR))break;const r=this.startNode(),n=this.state.value;if(this.match(c.string)?r.key=this.parseLiteral(n,"StringLiteral"):r.key=this.parseIdentifier(!0),this.expect(c.colon),"type"!==n&&this.raise(r.key.start,E.ModuleAttributeDifferentFromType,n),t.has(n)&&this.raise(r.key.start,E.ModuleAttributesWithDuplicateKeys,n),t.add(n),!this.match(c.string))throw this.unexpected(this.state.start,E.ModuleAttributeInvalidValue);r.value=this.parseLiteral(this.state.value,"StringLiteral"),this.finishNode(r,"ImportAttribute"),e.push(r)}while(this.eat(c.comma));return e}maybeParseModuleAttributes(){if(!this.match(c._with)||this.hasPrecedingLineBreak())return this.hasPlugin("moduleAttributes")?[]:null;this.expectPlugin("moduleAttributes"),this.next();const e=[],t=new Set;do{const r=this.startNode();if(r.key=this.parseIdentifier(!0),"type"!==r.key.name&&this.raise(r.key.start,E.ModuleAttributeDifferentFromType,r.key.name),t.has(r.key.name)&&this.raise(r.key.start,E.ModuleAttributesWithDuplicateKeys,r.key.name),t.add(r.key.name),this.expect(c.colon),!this.match(c.string))throw this.unexpected(this.state.start,E.ModuleAttributeInvalidValue);r.value=this.parseLiteral(this.state.value,"StringLiteral"),this.finishNode(r,"ImportAttribute"),e.push(r)}while(this.eat(c.comma));return e}maybeParseImportAssertions(){if(!this.isContextual("assert")||this.hasPrecedingLineBreak())return this.hasPlugin("importAssertions")?[]:null;this.expectPlugin("importAssertions"),this.next(),this.eat(c.braceL);const e=this.parseAssertEntries();return this.eat(c.braceR),e}maybeParseDefaultImportSpecifier(e){return!!this.shouldParseDefaultImport(e)&&(this.parseImportSpecifierLocal(e,this.startNode(),"ImportDefaultSpecifier","default import specifier"),!0)}maybeParseStarImportSpecifier(e){if(this.match(c.star)){const t=this.startNode();return this.next(),this.expectContextual("as"),this.parseImportSpecifierLocal(e,t,"ImportNamespaceSpecifier","import namespace specifier"),!0}return!1}parseNamedImportSpecifiers(e){let t=!0;for(this.expect(c.braceL);!this.eat(c.braceR);){if(t)t=!1;else{if(this.eat(c.colon))throw this.raise(this.state.start,E.DestructureNamedImport);if(this.expect(c.comma),this.eat(c.braceR))break}this.parseImportSpecifier(e)}}parseImportSpecifier(e){const t=this.startNode();if(t.imported=this.parseModuleExportName(),this.eatContextual("as"))t.local=this.parseIdentifier();else{const{imported:e}=t;if("StringLiteral"===e.type)throw this.raise(t.start,E.ImportBindingIsString,e.value);this.checkReservedWord(e.name,t.start,!0,!0),t.local=e.__clone()}this.checkLVal(t.local,"import specifier",9),e.specifiers.push(this.finishNode(t,"ImportSpecifier"))}isThisParam(e){return"Identifier"===e.type&&"this"===e.name}}class He extends Je{constructor(e,t){super(e=function(e){const t={};for(const r of Object.keys(Pe))t[r]=e&&null!=e[r]?e[r]:Pe[r];return t}(e),t),this.options=e,this.initializeScopes(),this.plugins=function(e){const t=new Map;for(const r of e){const[e,n]=Array.isArray(r)?r:[r,{}];t.has(e)||t.set(e,n||{})}return t}(this.options.plugins),this.filename=e.sourceFilename}getScopeHandler(){return J}parse(){this.enterInitialScopes();const e=this.startNode(),t=this.startNode();return this.nextToken(),e.errors=null,this.parseTopLevel(e,t),e.errors=this.state.errors,e}}function Ge(e,t){let r=He;return null!=e&&e.plugins&&(function(e){if(Te(e,"decorators")){if(Te(e,"decorators-legacy"))throw new Error("Cannot use the decorators and decorators-legacy plugin together");const t=be(e,"decorators","decoratorsBeforeExport");if(null==t)throw new Error("The 'decorators' plugin requires a 'decoratorsBeforeExport' option, whose value must be a boolean. If you are migrating from Babylon/Babel 6 or want to use the old decorators proposal, you should use the 'decorators-legacy' plugin instead of 'decorators'.");if("boolean"!=typeof t)throw new Error("'decoratorsBeforeExport' must be a boolean.")}if(Te(e,"flow")&&Te(e,"typescript"))throw new Error("Cannot combine flow and typescript plugins.");if(Te(e,"placeholders")&&Te(e,"v8intrinsic"))throw new Error("Cannot combine placeholders and v8intrinsic plugins.");if(Te(e,"pipelineOperator")&&!ge.includes(be(e,"pipelineOperator","proposal")))throw new Error("'pipelineOperator' requires 'proposal' option whose value should be one of: "+ge.map((e=>`'${e}'`)).join(", "));if(Te(e,"moduleAttributes")){if(Te(e,"importAssertions"))throw new Error("Cannot combine importAssertions and moduleAttributes plugins.");if("may-2020"!==be(e,"moduleAttributes","version"))throw new Error("The 'moduleAttributes' plugin requires a 'version' option, representing the last proposal update. Currently, the only supported value is 'may-2020'.")}if(Te(e,"recordAndTuple")&&!Se.includes(be(e,"recordAndTuple","syntaxType")))throw new Error("'recordAndTuple' requires 'syntaxType' option whose value should be one of: "+Se.map((e=>`'${e}'`)).join(", "));if(Te(e,"asyncDoExpressions")&&!Te(e,"doExpressions")){const e=new Error("'asyncDoExpressions' requires 'doExpressions', please add 'doExpressions' to parser plugins.");throw e.missingPlugins="doExpressions",e}}(e.plugins),r=function(e){const t=Ee.filter((t=>Te(e,t))),r=t.join("/");let n=ze[r];if(!n){n=He;for(const e of t)n=xe[e](n);ze[r]=n}return n}(e.plugins)),new r(e,t)}const ze={};t.parse=function(e,t){var r;if("unambiguous"!==(null==(r=t)?void 0:r.sourceType))return Ge(t,e).parse();t=Object.assign({},t);try{t.sourceType="module";const r=Ge(t,e),n=r.parse();if(r.sawUnambiguousESM)return n;if(r.ambiguousScriptDifferentAst)try{return t.sourceType="script",Ge(t,e).parse()}catch(e){}else n.program.sourceType="script";return n}catch(r){try{return t.sourceType="script",Ge(t,e).parse()}catch(e){}throw r}},t.parseExpression=function(e,t){const r=Ge(t,e);return r.options.strictMode&&(r.state.strict=!0),r.getExpression()},t.tokTypes=c},1619:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,r){const a=new WeakMap,p=new WeakMap,c=r||(0,n.validate)(null);return Object.assign(((r,...o)=>{if("string"==typeof r){if(o.length>1)throw new Error("Unexpected extra params.");return l((0,i.default)(t,r,(0,n.merge)(c,(0,n.validate)(o[0]))))}if(Array.isArray(r)){let e=a.get(r);return e||(e=(0,s.default)(t,r,c),a.set(r,e)),l(e(o))}if("object"==typeof r&&r){if(o.length>0)throw new Error("Unexpected extra params.");return e(t,(0,n.merge)(c,(0,n.validate)(r)))}throw new Error("Unexpected template param "+typeof r)}),{ast:(e,...r)=>{if("string"==typeof e){if(r.length>1)throw new Error("Unexpected extra params.");return(0,i.default)(t,e,(0,n.merge)((0,n.merge)(c,(0,n.validate)(r[0])),o))()}if(Array.isArray(e)){let i=p.get(e);return i||(i=(0,s.default)(t,e,(0,n.merge)(c,o)),p.set(e,i)),i(r)()}throw new Error("Unexpected template param "+typeof e)}})};var n=r(2123),i=a(r(3046)),s=a(r(3004));function a(e){return e&&e.__esModule?e:{default:e}}const o=(0,n.validate)({placeholderPattern:!1});function l(e){let t="";try{throw new Error}catch(e){e.stack&&(t=e.stack.split("\n").slice(3).join("\n"))}return r=>{try{return e(r)}catch(e){throw e.stack+=`\n    =============\n${t}`,e}}}},8913:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.program=t.expression=t.statement=t.statements=t.smart=void 0;var n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var a=n?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(r,s,a):r[s]=e[s]}return r.default=e,t&&t.set(e,r),r}(r(17));function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}function s(e){return{code:e=>`/* @babel/template */;\n${e}`,validate:()=>{},unwrap:t=>e(t.program.body.slice(1))}}const a=s((e=>e.length>1?e:e[0]));t.smart=a;const o=s((e=>e));t.statements=o;const l=s((e=>{if(0===e.length)throw new Error("Found nothing to return.");if(e.length>1)throw new Error("Found multiple statements but wanted one");return e[0]}));t.statement=l;const p={code:e=>`(\n${e}\n)`,validate:e=>{if(e.program.body.length>1)throw new Error("Found multiple statements but wanted one");if(0===p.unwrap(e).start)throw new Error("Parse result included parens.")},unwrap:({program:e})=>{const[t]=e.body;return n.assertExpressionStatement(t),t.expression}};t.expression=p,t.program={code:e=>e,validate:()=>{},unwrap:e=>e.program}},6205:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.program=t.expression=t.statements=t.statement=t.smart=void 0;var n,i=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var s=n?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(r(8913)),s=(n=r(1619))&&n.__esModule?n:{default:n};function a(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return a=function(){return e},e}const o=(0,s.default)(i.smart);t.smart=o;const l=(0,s.default)(i.statement);t.statement=l;const p=(0,s.default)(i.statements);t.statements=p;const c=(0,s.default)(i.expression);t.expression=c;const u=(0,s.default)(i.program);t.program=u;var d=Object.assign(o.bind(void 0),{smart:o,statement:l,statements:p,expression:c,program:u,ast:o.ast});t.default=d},3004:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){const{metadata:a,names:l}=function(e,t,r){let n,s,a,l="";do{l+="$";const p=o(t,l);n=p.names,s=new Set(n),a=(0,i.default)(e,e.code(p.code),{parser:r.parser,placeholderWhitelist:new Set(p.names.concat(r.placeholderWhitelist?Array.from(r.placeholderWhitelist):[])),placeholderPattern:r.placeholderPattern,preserveComments:r.preserveComments,syntacticPlaceholders:r.syntacticPlaceholders})}while(a.placeholders.some((e=>e.isDuplicate&&s.has(e.name))));return{metadata:a,names:n}}(e,t,r);return t=>{const r={};return t.forEach(((e,t)=>{r[l[t]]=e})),t=>{const i=(0,n.normalizeReplacements)(t);return i&&Object.keys(i).forEach((e=>{if(Object.prototype.hasOwnProperty.call(r,e))throw new Error("Unexpected replacement overlap.")})),e.unwrap((0,s.default)(a,i?Object.assign(i,r):r))}}};var n=r(2123),i=a(r(5008)),s=a(r(5985));function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){const r=[];let n=e[0];for(let i=1;i<e.length;i++){const s=`${t}${i-1}`;r.push(s),n+=s+e[i]}return{names:r,code:n}}},2123:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.merge=function(e,t){const{placeholderWhitelist:r=e.placeholderWhitelist,placeholderPattern:n=e.placeholderPattern,preserveComments:i=e.preserveComments,syntacticPlaceholders:s=e.syntacticPlaceholders}=t;return{parser:Object.assign({},e.parser,t.parser),placeholderWhitelist:r,placeholderPattern:n,preserveComments:i,syntacticPlaceholders:s}},t.validate=function(e){if(null!=e&&"object"!=typeof e)throw new Error("Unknown template options.");const t=e||{},{placeholderWhitelist:r,placeholderPattern:n,preserveComments:i,syntacticPlaceholders:s}=t,a=function(e,t){if(null==e)return{};var r,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(t,["placeholderWhitelist","placeholderPattern","preserveComments","syntacticPlaceholders"]);if(null!=r&&!(r instanceof Set))throw new Error("'.placeholderWhitelist' must be a Set, null, or undefined");if(null!=n&&!(n instanceof RegExp)&&!1!==n)throw new Error("'.placeholderPattern' must be a RegExp, false, null, or undefined");if(null!=i&&"boolean"!=typeof i)throw new Error("'.preserveComments' must be a boolean, null, or undefined");if(null!=s&&"boolean"!=typeof s)throw new Error("'.syntacticPlaceholders' must be a boolean, null, or undefined");if(!0===s&&(null!=r||null!=n))throw new Error("'.placeholderWhitelist' and '.placeholderPattern' aren't compatible with '.syntacticPlaceholders: true'");return{parser:a,placeholderWhitelist:r||void 0,placeholderPattern:null==n?void 0:n,preserveComments:null==i?void 0:i,syntacticPlaceholders:null==s?void 0:s}},t.normalizeReplacements=function(e){if(Array.isArray(e))return e.reduce(((e,t,r)=>(e["$"+r]=t,e)),{});if("object"==typeof e||null==e)return e||void 0;throw new Error("Template replacements must be an array, object, null, or undefined")}},5008:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){const{placeholderWhitelist:a,placeholderPattern:o,preserveComments:p,syntacticPlaceholders:c}=r,u=function(e,t,r){const n=(t.plugins||[]).slice();!1!==r&&n.push("placeholders"),t=Object.assign({allowReturnOutsideFunction:!0,allowSuperOutsideMethod:!0,sourceType:"module"},t,{plugins:n});try{return(0,i.parse)(e,t)}catch(t){const r=t.loc;throw r&&(t.message+="\n"+(0,s.codeFrameColumns)(e,{start:r}),t.code="BABEL_TEMPLATE_PARSE_ERROR"),t}}(t,r.parser,c);n.removePropertiesDeep(u,{preserveComments:p}),e.validate(u);const d={placeholders:[],placeholderNames:new Set},h={placeholders:[],placeholderNames:new Set},f={value:void 0};return n.traverse(u,l,{syntactic:d,legacy:h,isLegacyRef:f,placeholderWhitelist:a,placeholderPattern:o,syntacticPlaceholders:c}),Object.assign({ast:u},f.value?h:d)};var n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=a();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var s=n?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(r,i,s):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(r(17)),i=r(4494),s=r(785);function a(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return a=function(){return e},e}const o=/^[_$A-Z0-9]+$/;function l(e,t,r){var i;let s;if(n.isPlaceholder(e)){if(!1===r.syntacticPlaceholders)throw new Error("%%foo%%-style placeholders can't be used when '.syntacticPlaceholders' is false.");s=e.name.name,r.isLegacyRef.value=!1}else{if(!1===r.isLegacyRef.value||r.syntacticPlaceholders)return;if(n.isIdentifier(e)||n.isJSXIdentifier(e))s=e.name,r.isLegacyRef.value=!0;else{if(!n.isStringLiteral(e))return;s=e.value,r.isLegacyRef.value=!0}}if(!r.isLegacyRef.value&&(null!=r.placeholderPattern||null!=r.placeholderWhitelist))throw new Error("'.placeholderWhitelist' and '.placeholderPattern' aren't compatible with '.syntacticPlaceholders: true'");if(r.isLegacyRef.value&&(!1===r.placeholderPattern||!(r.placeholderPattern||o).test(s))&&!(null==(i=r.placeholderWhitelist)?void 0:i.has(s)))return;t=t.slice();const{node:a,key:l}=t[t.length-1];let p;n.isStringLiteral(e)||n.isPlaceholder(e,{expectedNode:"StringLiteral"})?p="string":n.isNewExpression(a)&&"arguments"===l||n.isCallExpression(a)&&"arguments"===l||n.isFunction(a)&&"params"===l?p="param":n.isExpressionStatement(a)&&!n.isPlaceholder(e)?(p="statement",t=t.slice(0,-1)):p=n.isStatement(e)&&n.isPlaceholder(e)?"statement":"other";const{placeholders:c,placeholderNames:u}=r.isLegacyRef.value?r.legacy:r.syntactic;c.push({name:s,type:p,resolve:e=>function(e,t){let r=e;for(let e=0;e<t.length-1;e++){const{key:n,index:i}=t[e];r=void 0===i?r[n]:r[n][i]}const{key:n,index:i}=t[t.length-1];return{parent:r,key:n,index:i}}(e,t),isDuplicate:u.has(s)}),u.add(s)}},5985:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){const r=n.cloneNode(e.ast);return t&&(e.placeholders.forEach((e=>{if(!Object.prototype.hasOwnProperty.call(t,e.name)){const t=e.name;throw new Error(`Error: No substitution given for "${t}". If this is not meant to be a\n            placeholder you may want to consider passing one of the following options to @babel/template:\n            - { placeholderPattern: false, placeholderWhitelist: new Set(['${t}'])}\n            - { placeholderPattern: /^${t}$/ }`)}})),Object.keys(t).forEach((t=>{if(!e.placeholderNames.has(t))throw new Error(`Unknown substitution "${t}" given`)}))),e.placeholders.slice().reverse().forEach((e=>{try{!function(e,t,r){e.isDuplicate&&(Array.isArray(r)?r=r.map((e=>n.cloneNode(e))):"object"==typeof r&&(r=n.cloneNode(r)));const{parent:i,key:s,index:a}=e.resolve(t);if("string"===e.type){if("string"==typeof r&&(r=n.stringLiteral(r)),!r||!n.isStringLiteral(r))throw new Error("Expected string substitution")}else if("statement"===e.type)void 0===a?r?Array.isArray(r)?r=n.blockStatement(r):"string"==typeof r?r=n.expressionStatement(n.identifier(r)):n.isStatement(r)||(r=n.expressionStatement(r)):r=n.emptyStatement():r&&!Array.isArray(r)&&("string"==typeof r&&(r=n.identifier(r)),n.isStatement(r)||(r=n.expressionStatement(r)));else if("param"===e.type){if("string"==typeof r&&(r=n.identifier(r)),void 0===a)throw new Error("Assertion failure.")}else if("string"==typeof r&&(r=n.identifier(r)),Array.isArray(r))throw new Error("Cannot replace single expression with an array.");if(void 0===a)n.validate(i,s,r),i[s]=r;else{const t=i[s].slice();"statement"===e.type||"param"===e.type?null==r?t.splice(a,1):Array.isArray(r)?t.splice(a,1,...r):t[a]=r:t[a]=r,n.validate(i,s,t),i[s]=t}}(e,r,t&&t[e.name]||null)}catch(t){throw t.message=`@babel/template placeholder "${e.name}": ${t.message}`,t}})),r};var n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var a=n?Object.getOwnPropertyDescriptor(e,s):null;a&&(a.get||a.set)?Object.defineProperty(r,s,a):r[s]=e[s]}return r.default=e,t&&t.set(e,r),r}(r(17));function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}},3046:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let a;return t=e.code(t),o=>{const l=(0,n.normalizeReplacements)(o);return a||(a=(0,i.default)(e,t,r)),e.unwrap((0,s.default)(a,l))}};var n=r(2123),i=a(r(5008)),s=a(r(5985));function a(e){return e&&e.__esModule?e:{default:e}}},9278:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(!(0,n.default)(e)){var t;const r=null!=(t=null==e?void 0:e.type)?t:JSON.stringify(e);throw new TypeError(`Not a valid node of type "${r}"`)}};var n=r(1860)},2937:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertArrayExpression=function(e,t){i("ArrayExpression",e,t)},t.assertAssignmentExpression=function(e,t){i("AssignmentExpression",e,t)},t.assertBinaryExpression=function(e,t){i("BinaryExpression",e,t)},t.assertInterpreterDirective=function(e,t){i("InterpreterDirective",e,t)},t.assertDirective=function(e,t){i("Directive",e,t)},t.assertDirectiveLiteral=function(e,t){i("DirectiveLiteral",e,t)},t.assertBlockStatement=function(e,t){i("BlockStatement",e,t)},t.assertBreakStatement=function(e,t){i("BreakStatement",e,t)},t.assertCallExpression=function(e,t){i("CallExpression",e,t)},t.assertCatchClause=function(e,t){i("CatchClause",e,t)},t.assertConditionalExpression=function(e,t){i("ConditionalExpression",e,t)},t.assertContinueStatement=function(e,t){i("ContinueStatement",e,t)},t.assertDebuggerStatement=function(e,t){i("DebuggerStatement",e,t)},t.assertDoWhileStatement=function(e,t){i("DoWhileStatement",e,t)},t.assertEmptyStatement=function(e,t){i("EmptyStatement",e,t)},t.assertExpressionStatement=function(e,t){i("ExpressionStatement",e,t)},t.assertFile=function(e,t){i("File",e,t)},t.assertForInStatement=function(e,t){i("ForInStatement",e,t)},t.assertForStatement=function(e,t){i("ForStatement",e,t)},t.assertFunctionDeclaration=function(e,t){i("FunctionDeclaration",e,t)},t.assertFunctionExpression=function(e,t){i("FunctionExpression",e,t)},t.assertIdentifier=function(e,t){i("Identifier",e,t)},t.assertIfStatement=function(e,t){i("IfStatement",e,t)},t.assertLabeledStatement=function(e,t){i("LabeledStatement",e,t)},t.assertStringLiteral=function(e,t){i("StringLiteral",e,t)},t.assertNumericLiteral=function(e,t){i("NumericLiteral",e,t)},t.assertNullLiteral=function(e,t){i("NullLiteral",e,t)},t.assertBooleanLiteral=function(e,t){i("BooleanLiteral",e,t)},t.assertRegExpLiteral=function(e,t){i("RegExpLiteral",e,t)},t.assertLogicalExpression=function(e,t){i("LogicalExpression",e,t)},t.assertMemberExpression=function(e,t){i("MemberExpression",e,t)},t.assertNewExpression=function(e,t){i("NewExpression",e,t)},t.assertProgram=function(e,t){i("Program",e,t)},t.assertObjectExpression=function(e,t){i("ObjectExpression",e,t)},t.assertObjectMethod=function(e,t){i("ObjectMethod",e,t)},t.assertObjectProperty=function(e,t){i("ObjectProperty",e,t)},t.assertRestElement=function(e,t){i("RestElement",e,t)},t.assertReturnStatement=function(e,t){i("ReturnStatement",e,t)},t.assertSequenceExpression=function(e,t){i("SequenceExpression",e,t)},t.assertParenthesizedExpression=function(e,t){i("ParenthesizedExpression",e,t)},t.assertSwitchCase=function(e,t){i("SwitchCase",e,t)},t.assertSwitchStatement=function(e,t){i("SwitchStatement",e,t)},t.assertThisExpression=function(e,t){i("ThisExpression",e,t)},t.assertThrowStatement=function(e,t){i("ThrowStatement",e,t)},t.assertTryStatement=function(e,t){i("TryStatement",e,t)},t.assertUnaryExpression=function(e,t){i("UnaryExpression",e,t)},t.assertUpdateExpression=function(e,t){i("UpdateExpression",e,t)},t.assertVariableDeclaration=function(e,t){i("VariableDeclaration",e,t)},t.assertVariableDeclarator=function(e,t){i("VariableDeclarator",e,t)},t.assertWhileStatement=function(e,t){i("WhileStatement",e,t)},t.assertWithStatement=function(e,t){i("WithStatement",e,t)},t.assertAssignmentPattern=function(e,t){i("AssignmentPattern",e,t)},t.assertArrayPattern=function(e,t){i("ArrayPattern",e,t)},t.assertArrowFunctionExpression=function(e,t){i("ArrowFunctionExpression",e,t)},t.assertClassBody=function(e,t){i("ClassBody",e,t)},t.assertClassExpression=function(e,t){i("ClassExpression",e,t)},t.assertClassDeclaration=function(e,t){i("ClassDeclaration",e,t)},t.assertExportAllDeclaration=function(e,t){i("ExportAllDeclaration",e,t)},t.assertExportDefaultDeclaration=function(e,t){i("ExportDefaultDeclaration",e,t)},t.assertExportNamedDeclaration=function(e,t){i("ExportNamedDeclaration",e,t)},t.assertExportSpecifier=function(e,t){i("ExportSpecifier",e,t)},t.assertForOfStatement=function(e,t){i("ForOfStatement",e,t)},t.assertImportDeclaration=function(e,t){i("ImportDeclaration",e,t)},t.assertImportDefaultSpecifier=function(e,t){i("ImportDefaultSpecifier",e,t)},t.assertImportNamespaceSpecifier=function(e,t){i("ImportNamespaceSpecifier",e,t)},t.assertImportSpecifier=function(e,t){i("ImportSpecifier",e,t)},t.assertMetaProperty=function(e,t){i("MetaProperty",e,t)},t.assertClassMethod=function(e,t){i("ClassMethod",e,t)},t.assertObjectPattern=function(e,t){i("ObjectPattern",e,t)},t.assertSpreadElement=function(e,t){i("SpreadElement",e,t)},t.assertSuper=function(e,t){i("Super",e,t)},t.assertTaggedTemplateExpression=function(e,t){i("TaggedTemplateExpression",e,t)},t.assertTemplateElement=function(e,t){i("TemplateElement",e,t)},t.assertTemplateLiteral=function(e,t){i("TemplateLiteral",e,t)},t.assertYieldExpression=function(e,t){i("YieldExpression",e,t)},t.assertAwaitExpression=function(e,t){i("AwaitExpression",e,t)},t.assertImport=function(e,t){i("Import",e,t)},t.assertBigIntLiteral=function(e,t){i("BigIntLiteral",e,t)},t.assertExportNamespaceSpecifier=function(e,t){i("ExportNamespaceSpecifier",e,t)},t.assertOptionalMemberExpression=function(e,t){i("OptionalMemberExpression",e,t)},t.assertOptionalCallExpression=function(e,t){i("OptionalCallExpression",e,t)},t.assertAnyTypeAnnotation=function(e,t){i("AnyTypeAnnotation",e,t)},t.assertArrayTypeAnnotation=function(e,t){i("ArrayTypeAnnotation",e,t)},t.assertBooleanTypeAnnotation=function(e,t){i("BooleanTypeAnnotation",e,t)},t.assertBooleanLiteralTypeAnnotation=function(e,t){i("BooleanLiteralTypeAnnotation",e,t)},t.assertNullLiteralTypeAnnotation=function(e,t){i("NullLiteralTypeAnnotation",e,t)},t.assertClassImplements=function(e,t){i("ClassImplements",e,t)},t.assertDeclareClass=function(e,t){i("DeclareClass",e,t)},t.assertDeclareFunction=function(e,t){i("DeclareFunction",e,t)},t.assertDeclareInterface=function(e,t){i("DeclareInterface",e,t)},t.assertDeclareModule=function(e,t){i("DeclareModule",e,t)},t.assertDeclareModuleExports=function(e,t){i("DeclareModuleExports",e,t)},t.assertDeclareTypeAlias=function(e,t){i("DeclareTypeAlias",e,t)},t.assertDeclareOpaqueType=function(e,t){i("DeclareOpaqueType",e,t)},t.assertDeclareVariable=function(e,t){i("DeclareVariable",e,t)},t.assertDeclareExportDeclaration=function(e,t){i("DeclareExportDeclaration",e,t)},t.assertDeclareExportAllDeclaration=function(e,t){i("DeclareExportAllDeclaration",e,t)},t.assertDeclaredPredicate=function(e,t){i("DeclaredPredicate",e,t)},t.assertExistsTypeAnnotation=function(e,t){i("ExistsTypeAnnotation",e,t)},t.assertFunctionTypeAnnotation=function(e,t){i("FunctionTypeAnnotation",e,t)},t.assertFunctionTypeParam=function(e,t){i("FunctionTypeParam",e,t)},t.assertGenericTypeAnnotation=function(e,t){i("GenericTypeAnnotation",e,t)},t.assertInferredPredicate=function(e,t){i("InferredPredicate",e,t)},t.assertInterfaceExtends=function(e,t){i("InterfaceExtends",e,t)},t.assertInterfaceDeclaration=function(e,t){i("InterfaceDeclaration",e,t)},t.assertInterfaceTypeAnnotation=function(e,t){i("InterfaceTypeAnnotation",e,t)},t.assertIntersectionTypeAnnotation=function(e,t){i("IntersectionTypeAnnotation",e,t)},t.assertMixedTypeAnnotation=function(e,t){i("MixedTypeAnnotation",e,t)},t.assertEmptyTypeAnnotation=function(e,t){i("EmptyTypeAnnotation",e,t)},t.assertNullableTypeAnnotation=function(e,t){i("NullableTypeAnnotation",e,t)},t.assertNumberLiteralTypeAnnotation=function(e,t){i("NumberLiteralTypeAnnotation",e,t)},t.assertNumberTypeAnnotation=function(e,t){i("NumberTypeAnnotation",e,t)},t.assertObjectTypeAnnotation=function(e,t){i("ObjectTypeAnnotation",e,t)},t.assertObjectTypeInternalSlot=function(e,t){i("ObjectTypeInternalSlot",e,t)},t.assertObjectTypeCallProperty=function(e,t){i("ObjectTypeCallProperty",e,t)},t.assertObjectTypeIndexer=function(e,t){i("ObjectTypeIndexer",e,t)},t.assertObjectTypeProperty=function(e,t){i("ObjectTypeProperty",e,t)},t.assertObjectTypeSpreadProperty=function(e,t){i("ObjectTypeSpreadProperty",e,t)},t.assertOpaqueType=function(e,t){i("OpaqueType",e,t)},t.assertQualifiedTypeIdentifier=function(e,t){i("QualifiedTypeIdentifier",e,t)},t.assertStringLiteralTypeAnnotation=function(e,t){i("StringLiteralTypeAnnotation",e,t)},t.assertStringTypeAnnotation=function(e,t){i("StringTypeAnnotation",e,t)},t.assertSymbolTypeAnnotation=function(e,t){i("SymbolTypeAnnotation",e,t)},t.assertThisTypeAnnotation=function(e,t){i("ThisTypeAnnotation",e,t)},t.assertTupleTypeAnnotation=function(e,t){i("TupleTypeAnnotation",e,t)},t.assertTypeofTypeAnnotation=function(e,t){i("TypeofTypeAnnotation",e,t)},t.assertTypeAlias=function(e,t){i("TypeAlias",e,t)},t.assertTypeAnnotation=function(e,t){i("TypeAnnotation",e,t)},t.assertTypeCastExpression=function(e,t){i("TypeCastExpression",e,t)},t.assertTypeParameter=function(e,t){i("TypeParameter",e,t)},t.assertTypeParameterDeclaration=function(e,t){i("TypeParameterDeclaration",e,t)},t.assertTypeParameterInstantiation=function(e,t){i("TypeParameterInstantiation",e,t)},t.assertUnionTypeAnnotation=function(e,t){i("UnionTypeAnnotation",e,t)},t.assertVariance=function(e,t){i("Variance",e,t)},t.assertVoidTypeAnnotation=function(e,t){i("VoidTypeAnnotation",e,t)},t.assertEnumDeclaration=function(e,t){i("EnumDeclaration",e,t)},t.assertEnumBooleanBody=function(e,t){i("EnumBooleanBody",e,t)},t.assertEnumNumberBody=function(e,t){i("EnumNumberBody",e,t)},t.assertEnumStringBody=function(e,t){i("EnumStringBody",e,t)},t.assertEnumSymbolBody=function(e,t){i("EnumSymbolBody",e,t)},t.assertEnumBooleanMember=function(e,t){i("EnumBooleanMember",e,t)},t.assertEnumNumberMember=function(e,t){i("EnumNumberMember",e,t)},t.assertEnumStringMember=function(e,t){i("EnumStringMember",e,t)},t.assertEnumDefaultedMember=function(e,t){i("EnumDefaultedMember",e,t)},t.assertIndexedAccessType=function(e,t){i("IndexedAccessType",e,t)},t.assertOptionalIndexedAccessType=function(e,t){i("OptionalIndexedAccessType",e,t)},t.assertJSXAttribute=function(e,t){i("JSXAttribute",e,t)},t.assertJSXClosingElement=function(e,t){i("JSXClosingElement",e,t)},t.assertJSXElement=function(e,t){i("JSXElement",e,t)},t.assertJSXEmptyExpression=function(e,t){i("JSXEmptyExpression",e,t)},t.assertJSXExpressionContainer=function(e,t){i("JSXExpressionContainer",e,t)},t.assertJSXSpreadChild=function(e,t){i("JSXSpreadChild",e,t)},t.assertJSXIdentifier=function(e,t){i("JSXIdentifier",e,t)},t.assertJSXMemberExpression=function(e,t){i("JSXMemberExpression",e,t)},t.assertJSXNamespacedName=function(e,t){i("JSXNamespacedName",e,t)},t.assertJSXOpeningElement=function(e,t){i("JSXOpeningElement",e,t)},t.assertJSXSpreadAttribute=function(e,t){i("JSXSpreadAttribute",e,t)},t.assertJSXText=function(e,t){i("JSXText",e,t)},t.assertJSXFragment=function(e,t){i("JSXFragment",e,t)},t.assertJSXOpeningFragment=function(e,t){i("JSXOpeningFragment",e,t)},t.assertJSXClosingFragment=function(e,t){i("JSXClosingFragment",e,t)},t.assertNoop=function(e,t){i("Noop",e,t)},t.assertPlaceholder=function(e,t){i("Placeholder",e,t)},t.assertV8IntrinsicIdentifier=function(e,t){i("V8IntrinsicIdentifier",e,t)},t.assertArgumentPlaceholder=function(e,t){i("ArgumentPlaceholder",e,t)},t.assertBindExpression=function(e,t){i("BindExpression",e,t)},t.assertClassProperty=function(e,t){i("ClassProperty",e,t)},t.assertPipelineTopicExpression=function(e,t){i("PipelineTopicExpression",e,t)},t.assertPipelineBareFunction=function(e,t){i("PipelineBareFunction",e,t)},t.assertPipelinePrimaryTopicReference=function(e,t){i("PipelinePrimaryTopicReference",e,t)},t.assertClassPrivateProperty=function(e,t){i("ClassPrivateProperty",e,t)},t.assertClassPrivateMethod=function(e,t){i("ClassPrivateMethod",e,t)},t.assertImportAttribute=function(e,t){i("ImportAttribute",e,t)},t.assertDecorator=function(e,t){i("Decorator",e,t)},t.assertDoExpression=function(e,t){i("DoExpression",e,t)},t.assertExportDefaultSpecifier=function(e,t){i("ExportDefaultSpecifier",e,t)},t.assertPrivateName=function(e,t){i("PrivateName",e,t)},t.assertRecordExpression=function(e,t){i("RecordExpression",e,t)},t.assertTupleExpression=function(e,t){i("TupleExpression",e,t)},t.assertDecimalLiteral=function(e,t){i("DecimalLiteral",e,t)},t.assertStaticBlock=function(e,t){i("StaticBlock",e,t)},t.assertModuleExpression=function(e,t){i("ModuleExpression",e,t)},t.assertTSParameterProperty=function(e,t){i("TSParameterProperty",e,t)},t.assertTSDeclareFunction=function(e,t){i("TSDeclareFunction",e,t)},t.assertTSDeclareMethod=function(e,t){i("TSDeclareMethod",e,t)},t.assertTSQualifiedName=function(e,t){i("TSQualifiedName",e,t)},t.assertTSCallSignatureDeclaration=function(e,t){i("TSCallSignatureDeclaration",e,t)},t.assertTSConstructSignatureDeclaration=function(e,t){i("TSConstructSignatureDeclaration",e,t)},t.assertTSPropertySignature=function(e,t){i("TSPropertySignature",e,t)},t.assertTSMethodSignature=function(e,t){i("TSMethodSignature",e,t)},t.assertTSIndexSignature=function(e,t){i("TSIndexSignature",e,t)},t.assertTSAnyKeyword=function(e,t){i("TSAnyKeyword",e,t)},t.assertTSBooleanKeyword=function(e,t){i("TSBooleanKeyword",e,t)},t.assertTSBigIntKeyword=function(e,t){i("TSBigIntKeyword",e,t)},t.assertTSIntrinsicKeyword=function(e,t){i("TSIntrinsicKeyword",e,t)},t.assertTSNeverKeyword=function(e,t){i("TSNeverKeyword",e,t)},t.assertTSNullKeyword=function(e,t){i("TSNullKeyword",e,t)},t.assertTSNumberKeyword=function(e,t){i("TSNumberKeyword",e,t)},t.assertTSObjectKeyword=function(e,t){i("TSObjectKeyword",e,t)},t.assertTSStringKeyword=function(e,t){i("TSStringKeyword",e,t)},t.assertTSSymbolKeyword=function(e,t){i("TSSymbolKeyword",e,t)},t.assertTSUndefinedKeyword=function(e,t){i("TSUndefinedKeyword",e,t)},t.assertTSUnknownKeyword=function(e,t){i("TSUnknownKeyword",e,t)},t.assertTSVoidKeyword=function(e,t){i("TSVoidKeyword",e,t)},t.assertTSThisType=function(e,t){i("TSThisType",e,t)},t.assertTSFunctionType=function(e,t){i("TSFunctionType",e,t)},t.assertTSConstructorType=function(e,t){i("TSConstructorType",e,t)},t.assertTSTypeReference=function(e,t){i("TSTypeReference",e,t)},t.assertTSTypePredicate=function(e,t){i("TSTypePredicate",e,t)},t.assertTSTypeQuery=function(e,t){i("TSTypeQuery",e,t)},t.assertTSTypeLiteral=function(e,t){i("TSTypeLiteral",e,t)},t.assertTSArrayType=function(e,t){i("TSArrayType",e,t)},t.assertTSTupleType=function(e,t){i("TSTupleType",e,t)},t.assertTSOptionalType=function(e,t){i("TSOptionalType",e,t)},t.assertTSRestType=function(e,t){i("TSRestType",e,t)},t.assertTSNamedTupleMember=function(e,t){i("TSNamedTupleMember",e,t)},t.assertTSUnionType=function(e,t){i("TSUnionType",e,t)},t.assertTSIntersectionType=function(e,t){i("TSIntersectionType",e,t)},t.assertTSConditionalType=function(e,t){i("TSConditionalType",e,t)},t.assertTSInferType=function(e,t){i("TSInferType",e,t)},t.assertTSParenthesizedType=function(e,t){i("TSParenthesizedType",e,t)},t.assertTSTypeOperator=function(e,t){i("TSTypeOperator",e,t)},t.assertTSIndexedAccessType=function(e,t){i("TSIndexedAccessType",e,t)},t.assertTSMappedType=function(e,t){i("TSMappedType",e,t)},t.assertTSLiteralType=function(e,t){i("TSLiteralType",e,t)},t.assertTSExpressionWithTypeArguments=function(e,t){i("TSExpressionWithTypeArguments",e,t)},t.assertTSInterfaceDeclaration=function(e,t){i("TSInterfaceDeclaration",e,t)},t.assertTSInterfaceBody=function(e,t){i("TSInterfaceBody",e,t)},t.assertTSTypeAliasDeclaration=function(e,t){i("TSTypeAliasDeclaration",e,t)},t.assertTSAsExpression=function(e,t){i("TSAsExpression",e,t)},t.assertTSTypeAssertion=function(e,t){i("TSTypeAssertion",e,t)},t.assertTSEnumDeclaration=function(e,t){i("TSEnumDeclaration",e,t)},t.assertTSEnumMember=function(e,t){i("TSEnumMember",e,t)},t.assertTSModuleDeclaration=function(e,t){i("TSModuleDeclaration",e,t)},t.assertTSModuleBlock=function(e,t){i("TSModuleBlock",e,t)},t.assertTSImportType=function(e,t){i("TSImportType",e,t)},t.assertTSImportEqualsDeclaration=function(e,t){i("TSImportEqualsDeclaration",e,t)},t.assertTSExternalModuleReference=function(e,t){i("TSExternalModuleReference",e,t)},t.assertTSNonNullExpression=function(e,t){i("TSNonNullExpression",e,t)},t.assertTSExportAssignment=function(e,t){i("TSExportAssignment",e,t)},t.assertTSNamespaceExportDeclaration=function(e,t){i("TSNamespaceExportDeclaration",e,t)},t.assertTSTypeAnnotation=function(e,t){i("TSTypeAnnotation",e,t)},t.assertTSTypeParameterInstantiation=function(e,t){i("TSTypeParameterInstantiation",e,t)},t.assertTSTypeParameterDeclaration=function(e,t){i("TSTypeParameterDeclaration",e,t)},t.assertTSTypeParameter=function(e,t){i("TSTypeParameter",e,t)},t.assertExpression=function(e,t){i("Expression",e,t)},t.assertBinary=function(e,t){i("Binary",e,t)},t.assertScopable=function(e,t){i("Scopable",e,t)},t.assertBlockParent=function(e,t){i("BlockParent",e,t)},t.assertBlock=function(e,t){i("Block",e,t)},t.assertStatement=function(e,t){i("Statement",e,t)},t.assertTerminatorless=function(e,t){i("Terminatorless",e,t)},t.assertCompletionStatement=function(e,t){i("CompletionStatement",e,t)},t.assertConditional=function(e,t){i("Conditional",e,t)},t.assertLoop=function(e,t){i("Loop",e,t)},t.assertWhile=function(e,t){i("While",e,t)},t.assertExpressionWrapper=function(e,t){i("ExpressionWrapper",e,t)},t.assertFor=function(e,t){i("For",e,t)},t.assertForXStatement=function(e,t){i("ForXStatement",e,t)},t.assertFunction=function(e,t){i("Function",e,t)},t.assertFunctionParent=function(e,t){i("FunctionParent",e,t)},t.assertPureish=function(e,t){i("Pureish",e,t)},t.assertDeclaration=function(e,t){i("Declaration",e,t)},t.assertPatternLike=function(e,t){i("PatternLike",e,t)},t.assertLVal=function(e,t){i("LVal",e,t)},t.assertTSEntityName=function(e,t){i("TSEntityName",e,t)},t.assertLiteral=function(e,t){i("Literal",e,t)},t.assertImmutable=function(e,t){i("Immutable",e,t)},t.assertUserWhitespacable=function(e,t){i("UserWhitespacable",e,t)},t.assertMethod=function(e,t){i("Method",e,t)},t.assertObjectMember=function(e,t){i("ObjectMember",e,t)},t.assertProperty=function(e,t){i("Property",e,t)},t.assertUnaryLike=function(e,t){i("UnaryLike",e,t)},t.assertPattern=function(e,t){i("Pattern",e,t)},t.assertClass=function(e,t){i("Class",e,t)},t.assertModuleDeclaration=function(e,t){i("ModuleDeclaration",e,t)},t.assertExportDeclaration=function(e,t){i("ExportDeclaration",e,t)},t.assertModuleSpecifier=function(e,t){i("ModuleSpecifier",e,t)},t.assertFlow=function(e,t){i("Flow",e,t)},t.assertFlowType=function(e,t){i("FlowType",e,t)},t.assertFlowBaseAnnotation=function(e,t){i("FlowBaseAnnotation",e,t)},t.assertFlowDeclaration=function(e,t){i("FlowDeclaration",e,t)},t.assertFlowPredicate=function(e,t){i("FlowPredicate",e,t)},t.assertEnumBody=function(e,t){i("EnumBody",e,t)},t.assertEnumMember=function(e,t){i("EnumMember",e,t)},t.assertJSX=function(e,t){i("JSX",e,t)},t.assertPrivate=function(e,t){i("Private",e,t)},t.assertTSTypeElement=function(e,t){i("TSTypeElement",e,t)},t.assertTSType=function(e,t){i("TSType",e,t)},t.assertTSBaseType=function(e,t){i("TSBaseType",e,t)},t.assertNumberLiteral=function(e,t){console.trace("The node type NumberLiteral has been renamed to NumericLiteral"),i("NumberLiteral",e,t)},t.assertRegexLiteral=function(e,t){console.trace("The node type RegexLiteral has been renamed to RegExpLiteral"),i("RegexLiteral",e,t)},t.assertRestProperty=function(e,t){console.trace("The node type RestProperty has been renamed to RestElement"),i("RestProperty",e,t)},t.assertSpreadProperty=function(e,t){console.trace("The node type SpreadProperty has been renamed to SpreadElement"),i("SpreadProperty",e,t)};var n=r(5001);function i(e,t,r){if(!(0,n.default)(e,t,r))throw new Error(`Expected type "${e}" with option ${JSON.stringify(r)}, but instead got "${t.type}".`)}},2006:()=>{},9234:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,...t){const r=n.BUILDER_KEYS[e],s=t.length;if(s>r.length)throw new Error(`${e}: Too many arguments passed. Received ${s} but can receive no more than ${r.length}`);const a={type:e};let o=0;r.forEach((r=>{const i=n.NODE_FIELDS[e][r];let l;o<s&&(l=t[o]),void 0===l&&(l=Array.isArray(i.default)?[]:i.default),a[r]=l,o++}));for(const e of Object.keys(a))(0,i.default)(a,e,a[e]);return a};var n=r(1102),i=r(1843)},4660:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){const t=(0,i.default)(e);return 1===t.length?t[0]:(0,n.unionTypeAnnotation)(t)};var n=r(1580),i=r(6337)},7395:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("string"===e)return(0,n.stringTypeAnnotation)();if("number"===e)return(0,n.numberTypeAnnotation)();if("undefined"===e)return(0,n.voidTypeAnnotation)();if("boolean"===e)return(0,n.booleanTypeAnnotation)();if("function"===e)return(0,n.genericTypeAnnotation)((0,n.identifier)("Function"));if("object"===e)return(0,n.genericTypeAnnotation)((0,n.identifier)("Object"));if("symbol"===e)return(0,n.genericTypeAnnotation)((0,n.identifier)("Symbol"));if("bigint"===e)return(0,n.anyTypeAnnotation)();throw new Error("Invalid typeof value: "+e)};var n=r(1580)},1580:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.arrayExpression=function(e){return(0,n.default)("ArrayExpression",...arguments)},t.assignmentExpression=function(e,t,r){return(0,n.default)("AssignmentExpression",...arguments)},t.binaryExpression=function(e,t,r){return(0,n.default)("BinaryExpression",...arguments)},t.interpreterDirective=function(e){return(0,n.default)("InterpreterDirective",...arguments)},t.directive=function(e){return(0,n.default)("Directive",...arguments)},t.directiveLiteral=function(e){return(0,n.default)("DirectiveLiteral",...arguments)},t.blockStatement=function(e,t){return(0,n.default)("BlockStatement",...arguments)},t.breakStatement=function(e){return(0,n.default)("BreakStatement",...arguments)},t.callExpression=function(e,t){return(0,n.default)("CallExpression",...arguments)},t.catchClause=function(e,t){return(0,n.default)("CatchClause",...arguments)},t.conditionalExpression=function(e,t,r){return(0,n.default)("ConditionalExpression",...arguments)},t.continueStatement=function(e){return(0,n.default)("ContinueStatement",...arguments)},t.debuggerStatement=function(){return(0,n.default)("DebuggerStatement",...arguments)},t.doWhileStatement=function(e,t){return(0,n.default)("DoWhileStatement",...arguments)},t.emptyStatement=function(){return(0,n.default)("EmptyStatement",...arguments)},t.expressionStatement=function(e){return(0,n.default)("ExpressionStatement",...arguments)},t.file=function(e,t,r){return(0,n.default)("File",...arguments)},t.forInStatement=function(e,t,r){return(0,n.default)("ForInStatement",...arguments)},t.forStatement=function(e,t,r,i){return(0,n.default)("ForStatem