{"version": 3, "file": "ExponentFileSystem.js", "sourceRoot": "", "sources": ["../src/ExponentFileSystem.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAG9D,IAAI,cAAc,CAAC;AAEnB,IAAI,kBAAkB,CAAC,kBAAkB,EAAE;IACzC,cAAc,GAAG,kBAAkB,CAAC,kBAAkB,CAAC;CACxD;KAAM;IACL,cAAc,GAAG,sBAAsB,CAAC;CACzC;AAED,eAAe,cAA0C,CAAC", "sourcesContent": ["import { NativeModulesProxy } from 'expo-modules-core';\n\nimport ExponentFileSystemShim from './ExponentFileSystemShim';\nimport { ExponentFileSystemModule } from './types';\n\nlet platformModule;\n\nif (NativeModulesProxy.ExponentFileSystem) {\n  platformModule = NativeModulesProxy.ExponentFileSystem;\n} else {\n  platformModule = ExponentFileSystemShim;\n}\n\nexport default platformModule as ExponentFileSystemModule;\n"]}