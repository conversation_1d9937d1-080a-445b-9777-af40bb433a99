{"version": 3, "names": ["getUserAndroidPath", "join", "process", "env", "LOCALAPPDATA", "getAndroidSdkRootInstallation", "ANDROID_SDK_ROOT", "ANDROID_HOME", "installPath", "pathExistsSync", "installComponent", "component", "androidSdkRoot", "Promise", "done", "error", "sdkmanager", "command", "child", "executeCommand", "stderr", "stdout", "on", "data", "includes", "stdin", "write", "toString", "exitStatus", "undefined", "parseHypervisor", "status", "customHypervisor", "hypervisor", "installed", "test", "getEmulatorAccelOutputInformation", "androidSDKRoot", "e", "createAVD", "name", "device", "image", "abi", "tag", "avdmanager", "config<PERSON><PERSON>", "HOMEPATH", "content", "readFile", "updatedContent", "replace", "writeFile", "getBestHypervisor", "getProcessorType", "lines", "split", "line", "enableWHPX", "enableHAXM", "androidSdkInstallPath", "enableAMDH"], "sources": ["../../../src/tools/windows/androidWinHelpers.ts"], "sourcesContent": ["import {readFile, writeFile, pathExistsSync} from 'fs-extra';\nimport {join} from 'path';\nimport {executeCommand} from './executeWinCommand';\nimport {getProcessorType} from './processorType';\n\ntype HypervisorStatus = {\n  hypervisor: 'WHPX' | 'HAXM' | 'AMDH' | 'none';\n  installed: boolean;\n};\n\n/**\n * Returns the path to where all Android related things should be installed\n * locally to the user.\n */\nexport const getUserAndroidPath = () => {\n  return join(process.env.LOCALAPPDATA || '', 'Android');\n};\n\n/**\n * Deals with ANDROID_HOME, ANDROID_SDK_ROOT or generates a new one\n */\nexport const getAndroidSdkRootInstallation = () => {\n  const env = process.env.ANDROID_SDK_ROOT || process.env.ANDROID_HOME;\n  const installPath = env\n    ? // Happens if previous installations or not fully completed\n      env\n    : // All Android zip files have a root folder, using `Android` as the common place\n      join(getUserAndroidPath(), 'Sdk');\n\n  if (pathExistsSync(installPath)) {\n    return installPath;\n  } else {\n    return '';\n  }\n};\n\n/**\n * Installs an Android component (e.g.: `platform-tools`, `emulator`)\n * using the `sdkmanager` tool and automatically accepting the licenses.\n */\nexport const installComponent = (component: string, androidSdkRoot: string) => {\n  return new Promise((done, error) => {\n    const sdkmanager = join(androidSdkRoot, 'tools', 'bin', 'sdkmanager.bat');\n\n    const command = `\"${sdkmanager}\" --sdk_root=\"${androidSdkRoot}\" \"${component}\"`;\n\n    const child = executeCommand(command);\n    let stderr = '';\n\n    child.stdout?.on('data', (data) => {\n      if (data.includes('(y/N)')) {\n        child.stdin?.write('y\\n');\n      }\n    });\n\n    child.stderr?.on('data', (data) => {\n      stderr += data.toString('utf-8');\n    });\n\n    child.on('close', (exitStatus) => {\n      if (exitStatus === 0) {\n        done(undefined);\n      } else {\n        error({stderr});\n      }\n    });\n    child.on('error', error);\n  });\n};\n\n/**\n * For the given custom Hypervisor and the output of `emulator-check accel`\n * returns the preferred Hypervisor to use and its installation status.\n * The recommendation order is:\n * 1. WHPX\n * 2. HAXM if Intel\n * 3. AMDH if AMD\n */\nconst parseHypervisor = (\n  status: string,\n  customHypervisor: 'HAXM' | 'AMDH',\n): HypervisorStatus | null => {\n  /**\n   * Messages:\n   * Android Emulator requires an Intel processor with VT-x and NX support.  Your CPU: 'AuthenticAMD'\n   * HAXM is not installed, but Windows Hypervisor Platform is available.\n   * WHPX (10.0.19041) is installed and usable.\n   * * This message outputs for WHPX and when the AMD Hypervisor is installed\n   * HAXM version 6.2.1 (4) is installed and usable.\n   * HAXM is not installed on this machine\n   */\n\n  if (\n    status.includes(\n      'is not installed, but Windows Hypervisor Platform is available.',\n    )\n  ) {\n    return {\n      hypervisor: 'WHPX',\n      installed: false,\n    };\n  }\n\n  if (/WHPX \\((\\d|\\.)+\\) is installed and usable\\./.test(status)) {\n    return {\n      hypervisor: 'WHPX',\n      installed: true,\n    };\n  }\n\n  if (/is installed and usable\\./.test(status)) {\n    return {\n      hypervisor: customHypervisor,\n      installed: true,\n    };\n  }\n\n  if (status.includes(\"Your CPU: 'AuthenticAMD'\")) {\n    return {\n      hypervisor: customHypervisor,\n      installed: false,\n    };\n  }\n\n  if (status.includes('is not installed on this machine')) {\n    return {\n      hypervisor: 'none',\n      installed: false,\n    };\n  }\n\n  return null;\n};\n\nconst getEmulatorAccelOutputInformation = async (androidSDKRoot: string) => {\n  /**\n   * The output of the following command is something like:\n   *\n   * ```\n   * accel:\n   * 0\n   * WHPX (10.0.19041) is installed and usable.\n   * accel\n   * ```\n   *\n   * If it fails it will still output to stdout with a similar format:\n   *\n   * ```\n   * accel:\n   * 1\n   * Android Emulator does not support nested virtualization.  Your VM host: 'Microsoft Hv' (Hyper-V)\n   * accel\n   * ```\n   *\n   */\n\n  try {\n    const {stdout} = await executeCommand(\n      `\"${join(androidSDKRoot, 'emulator', 'emulator-check.exe')}\" accel`,\n    );\n\n    return stdout;\n  } catch (e) {\n    const {stdout} = e as any;\n\n    return stdout;\n  }\n};\n\n/**\n * Creates a new Android Virtual Device in the default folder with the\n * name, device and system image passed by parameter.\n */\nexport const createAVD = async (\n  androidSDKRoot: string,\n  name: string,\n  device: string,\n  image: string,\n) => {\n  try {\n    const abi = image.includes('x86_64') ? 'x86_64' : 'x86';\n    const tag = image.includes('google_apis') ? 'google_apis' : 'generic';\n    const avdmanager = join(androidSDKRoot, 'tools', 'bin', 'avdmanager.bat');\n\n    const {stdout} = await executeCommand(\n      `${avdmanager} -s create avd --force --name \"${name}\" --device \"${device}\" --package \"${image}\" --tag \"${tag}\" --abi \"${abi}\"`,\n    );\n\n    // For some reason `image.sysdir.1` in `config.ini` points to the wrong location and needs to be updated\n    const configPath = join(\n      process.env.HOMEPATH || '',\n      '.android',\n      'avd',\n      `${name}.avd`,\n      'config.ini',\n    );\n\n    const content = await readFile(configPath, 'utf-8');\n    const updatedContent = content.replace(\n      /Sdk\\\\system-images/g,\n      'system-images',\n    );\n\n    await writeFile(configPath, updatedContent, 'utf-8');\n\n    return stdout;\n  } catch (e) {\n    const {stderr} = e as any;\n\n    return stderr;\n  }\n};\n\n/**\n * Returns what hypervisor should be installed for the Android emulator\n * using [Microsoft's official\n * documentation](https://docs.microsoft.com/en-us/xamarin/android/get-started/installation/android-emulator/hardware-acceleration?pivots=windows)\n * as a reference.\n */\nexport const getBestHypervisor = async (\n  androidSDKRoot: string,\n): Promise<HypervisorStatus> => {\n  const customHypervisor = getProcessorType() === 'Intel' ? 'HAXM' : 'AMDH';\n\n  const stdout = await getEmulatorAccelOutputInformation(androidSDKRoot);\n\n  const lines = stdout.split('\\n');\n\n  for (const line of lines) {\n    const hypervisor = parseHypervisor(line, customHypervisor);\n\n    if (hypervisor) {\n      return hypervisor;\n    }\n  }\n\n  // Couldn't identify the best one to run so not doing anything\n  return {\n    hypervisor: 'none',\n    installed: false,\n  };\n};\n\n/**\n * Enables the Windows HypervisorPlatform and Hyper-V features.\n * Will prompt the User Account Control (UAC)\n */\nexport const enableWHPX = () => {\n  return executeCommand(\n    'DISM /Quiet /NoRestart /Online /Enable-Feature /All /FeatureName:Microsoft-Hyper-V /FeatureName:HypervisorPlatform',\n    true,\n  );\n};\n\n/**\n * Installs and enables the [HAXM](https://github.com/intel/haxm)\n * version available through the Android SDK manager.\n * @param androidSdkInstallPath The path to the Android SDK installation\n */\nexport const enableHAXM = async (androidSdkInstallPath: string) => {\n  await installComponent(\n    'extras;intel;Hardware_Accelerated_Execution_Manager',\n    androidSdkInstallPath,\n  );\n\n  await executeCommand(\n    join(\n      androidSdkInstallPath,\n      'Sdk',\n      'extras',\n      'intel',\n      'Hardware_Accelerated_Execution_Manager',\n      'silent_install.bat',\n    ),\n  );\n};\n\n/**\n * Installs and enables the\n * [Hypervisor Driver for AMD Processors](https://androidstudio.googleblog.com/2019/10/android-emulator-hypervisor-driver-for.html)\n * version available through the Android SDK manager.\n * @param androidSdkInstallPath The path to the Android SDK installation\n */\nexport const enableAMDH = async (androidSdkInstallPath: string) => {\n  await installComponent(\n    'extras;google;Android_Emulator_Hypervisor_Driver',\n    androidSdkInstallPath,\n  );\n\n  await executeCommand(\n    join(\n      androidSdkInstallPath,\n      'Sdk',\n      'extras',\n      'google',\n      'Android_Emulator_Hypervisor_Driver',\n      'silent_install.bat',\n    ),\n  );\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAOA;AACA;AACA;AACA;AACO,MAAMA,kBAAkB,GAAG,MAAM;EACtC,OAAO,IAAAC,YAAI,EAACC,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,EAAE,EAAE,SAAS,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AAFA;AAGO,MAAMC,6BAA6B,GAAG,MAAM;EACjD,MAAMF,GAAG,GAAGD,OAAO,CAACC,GAAG,CAACG,gBAAgB,IAAIJ,OAAO,CAACC,GAAG,CAACI,YAAY;EACpE,MAAMC,WAAW,GAAGL,GAAG;EACnB;EACAA,GAAG;EACH;EACA,IAAAF,YAAI,EAACD,kBAAkB,EAAE,EAAE,KAAK,CAAC;EAErC,IAAI,IAAAS,yBAAc,EAACD,WAAW,CAAC,EAAE;IAC/B,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AAHA;AAIO,MAAME,gBAAgB,GAAG,CAACC,SAAiB,EAAEC,cAAsB,KAAK;EAC7E,OAAO,IAAIC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAAA;IAClC,MAAMC,UAAU,GAAG,IAAAf,YAAI,EAACW,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,CAAC;IAEzE,MAAMK,OAAO,GAAI,IAAGD,UAAW,iBAAgBJ,cAAe,MAAKD,SAAU,GAAE;IAE/E,MAAMO,KAAK,GAAG,IAAAC,iCAAc,EAACF,OAAO,CAAC;IACrC,IAAIG,MAAM,GAAG,EAAE;IAEf,iBAAAF,KAAK,CAACG,MAAM,kDAAZ,cAAcC,EAAE,CAAC,MAAM,EAAGC,IAAI,IAAK;MACjC,IAAIA,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAAA;QAC1B,gBAAAN,KAAK,CAACO,KAAK,iDAAX,aAAaC,KAAK,CAAC,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC;IAEF,iBAAAR,KAAK,CAACE,MAAM,kDAAZ,cAAcE,EAAE,CAAC,MAAM,EAAGC,IAAI,IAAK;MACjCH,MAAM,IAAIG,IAAI,CAACI,QAAQ,CAAC,OAAO,CAAC;IAClC,CAAC,CAAC;IAEFT,KAAK,CAACI,EAAE,CAAC,OAAO,EAAGM,UAAU,IAAK;MAChC,IAAIA,UAAU,KAAK,CAAC,EAAE;QACpBd,IAAI,CAACe,SAAS,CAAC;MACjB,CAAC,MAAM;QACLd,KAAK,CAAC;UAACK;QAAM,CAAC,CAAC;MACjB;IACF,CAAC,CAAC;IACFF,KAAK,CAACI,EAAE,CAAC,OAAO,EAAEP,KAAK,CAAC;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AAQA,MAAMe,eAAe,GAAG,CACtBC,MAAc,EACdC,gBAAiC,KACL;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IACED,MAAM,CAACP,QAAQ,CACb,iEAAiE,CAClE,EACD;IACA,OAAO;MACLS,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,IAAI,6CAA6C,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAE;IAC9D,OAAO;MACLE,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,IAAI,2BAA2B,CAACC,IAAI,CAACJ,MAAM,CAAC,EAAE;IAC5C,OAAO;MACLE,UAAU,EAAED,gBAAgB;MAC5BE,SAAS,EAAE;IACb,CAAC;EACH;EAEA,IAAIH,MAAM,CAACP,QAAQ,CAAC,0BAA0B,CAAC,EAAE;IAC/C,OAAO;MACLS,UAAU,EAAED,gBAAgB;MAC5BE,SAAS,EAAE;IACb,CAAC;EACH;EAEA,IAAIH,MAAM,CAACP,QAAQ,CAAC,kCAAkC,CAAC,EAAE;IACvD,OAAO;MACLS,UAAU,EAAE,MAAM;MAClBC,SAAS,EAAE;IACb,CAAC;EACH;EAEA,OAAO,IAAI;AACb,CAAC;AAED,MAAME,iCAAiC,GAAG,MAAOC,cAAsB,IAAK;EAC1E;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,IAAI;IACF,MAAM;MAAChB;IAAM,CAAC,GAAG,MAAM,IAAAF,iCAAc,EAClC,IAAG,IAAAlB,YAAI,EAACoC,cAAc,EAAE,UAAU,EAAE,oBAAoB,CAAE,SAAQ,CACpE;IAED,OAAOhB,MAAM;EACf,CAAC,CAAC,OAAOiB,CAAC,EAAE;IACV,MAAM;MAACjB;IAAM,CAAC,GAAGiB,CAAQ;IAEzB,OAAOjB,MAAM;EACf;AACF,CAAC;;AAED;AACA;AACA;AACA;AACO,MAAMkB,SAAS,GAAG,OACvBF,cAAsB,EACtBG,IAAY,EACZC,MAAc,EACdC,KAAa,KACV;EACH,IAAI;IACF,MAAMC,GAAG,GAAGD,KAAK,CAAClB,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,KAAK;IACvD,MAAMoB,GAAG,GAAGF,KAAK,CAAClB,QAAQ,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,SAAS;IACrE,MAAMqB,UAAU,GAAG,IAAA5C,YAAI,EAACoC,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,CAAC;IAEzE,MAAM;MAAChB;IAAM,CAAC,GAAG,MAAM,IAAAF,iCAAc,EAClC,GAAE0B,UAAW,kCAAiCL,IAAK,eAAcC,MAAO,gBAAeC,KAAM,YAAWE,GAAI,YAAWD,GAAI,GAAE,CAC/H;;IAED;IACA,MAAMG,UAAU,GAAG,IAAA7C,YAAI,EACrBC,OAAO,CAACC,GAAG,CAAC4C,QAAQ,IAAI,EAAE,EAC1B,UAAU,EACV,KAAK,EACJ,GAAEP,IAAK,MAAK,EACb,YAAY,CACb;IAED,MAAMQ,OAAO,GAAG,MAAM,IAAAC,mBAAQ,EAACH,UAAU,EAAE,OAAO,CAAC;IACnD,MAAMI,cAAc,GAAGF,OAAO,CAACG,OAAO,CACpC,qBAAqB,EACrB,eAAe,CAChB;IAED,MAAM,IAAAC,oBAAS,EAACN,UAAU,EAAEI,cAAc,EAAE,OAAO,CAAC;IAEpD,OAAO7B,MAAM;EACf,CAAC,CAAC,OAAOiB,CAAC,EAAE;IACV,MAAM;MAAClB;IAAM,CAAC,GAAGkB,CAAQ;IAEzB,OAAOlB,MAAM;EACf;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA;AAMO,MAAMiC,iBAAiB,GAAG,MAC/BhB,cAAsB,IACQ;EAC9B,MAAML,gBAAgB,GAAG,IAAAsB,+BAAgB,GAAE,KAAK,OAAO,GAAG,MAAM,GAAG,MAAM;EAEzE,MAAMjC,MAAM,GAAG,MAAMe,iCAAiC,CAACC,cAAc,CAAC;EAEtE,MAAMkB,KAAK,GAAGlC,MAAM,CAACmC,KAAK,CAAC,IAAI,CAAC;EAEhC,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;IACxB,MAAMtB,UAAU,GAAGH,eAAe,CAAC2B,IAAI,EAAEzB,gBAAgB,CAAC;IAE1D,IAAIC,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;EACF;;EAEA;EACA,OAAO;IACLA,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE;EACb,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AAHA;AAIO,MAAMwB,UAAU,GAAG,MAAM;EAC9B,OAAO,IAAAvC,iCAAc,EACnB,oHAAoH,EACpH,IAAI,CACL;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJA;AAKO,MAAMwC,UAAU,GAAG,MAAOC,qBAA6B,IAAK;EACjE,MAAMlD,gBAAgB,CACpB,qDAAqD,EACrDkD,qBAAqB,CACtB;EAED,MAAM,IAAAzC,iCAAc,EAClB,IAAAlB,YAAI,EACF2D,qBAAqB,EACrB,KAAK,EACL,QAAQ,EACR,OAAO,EACP,wCAAwC,EACxC,oBAAoB,CACrB,CACF;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA;AAMO,MAAMC,UAAU,GAAG,MAAOD,qBAA6B,IAAK;EACjE,MAAMlD,gBAAgB,CACpB,kDAAkD,EAClDkD,qBAAqB,CACtB;EAED,MAAM,IAAAzC,iCAAc,EAClB,IAAAlB,YAAI,EACF2D,qBAAqB,EACrB,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,oCAAoC,EACpC,oBAAoB,CACrB,CACF;AACH,CAAC;AAAC"}