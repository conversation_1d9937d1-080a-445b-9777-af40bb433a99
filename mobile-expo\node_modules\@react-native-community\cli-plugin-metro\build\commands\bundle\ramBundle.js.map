{"version": 3, "names": ["ramBundle", "argv", "config", "args", "bundleWithOutput", "outputUnbundle", "name", "description", "func", "options", "bundleCommandLineArgs", "concat", "default"], "sources": ["../../../src/commands/bundle/ramBundle.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n// @ts-ignore - no typed definition for the package\nimport outputUnbundle from 'metro/src/shared/output/RamBundle';\nimport {withOutput as bundleWithOutput} from './bundle';\nimport bundleCommandLineArgs, {CommandLineArgs} from './bundleCommandLineArgs';\nimport type {Config} from '@react-native-community/cli-types';\n\n/**\n * Builds the bundle starting to look for dependencies at the given entry path.\n */\nfunction ramBundle(argv: Array<string>, config: Config, args: CommandLineArgs) {\n  return bundleWithOutput(argv, config, args, outputUnbundle);\n}\n\nexport default {\n  name: 'ram-bundle',\n  description:\n    'builds javascript as a \"Random Access Module\" bundle for offline use',\n  func: ramBundle,\n  options: bundleCommandLineArgs.concat({\n    name: '--indexed-ram-bundle',\n    description:\n      'Force the \"Indexed RAM\" bundle file format, even when building for android',\n    default: false,\n  }),\n};\n\nexport {ramBundle};\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA+E;AAV/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA,SAASA,SAAS,CAACC,IAAmB,EAAEC,MAAc,EAAEC,IAAqB,EAAE;EAC7E,OAAO,IAAAC,kBAAgB,EAACH,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEE,oBAAc,CAAC;AAC7D;AAAC,eAEc;EACbC,IAAI,EAAE,YAAY;EAClBC,WAAW,EACT,sEAAsE;EACxEC,IAAI,EAAER,SAAS;EACfS,OAAO,EAAEC,8BAAqB,CAACC,MAAM,CAAC;IACpCL,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EACT,4EAA4E;IAC9EK,OAAO,EAAE;EACX,CAAC;AACH,CAAC;AAAA"}