{"version": 3, "file": "getWatchFolders.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_glob", "_path", "_getModulesPaths", "obj", "__esModule", "default", "readJsonFile", "filePath", "file", "fs", "readFileSync", "JSON", "parse", "isValidJsonFile", "globAllPackageJsonPaths", "workspaceProjectRoot", "linkedPackages", "map", "glob", "globSync", "path", "join", "replace", "cwd", "absolute", "ignore", "pkgPath", "flat", "filter", "Boolean", "p", "getWorkspacePackagesArray", "workspaces", "Array", "isArray", "assert", "packages", "resolveAllWorkspacePackageJsonPaths", "rootPackageJsonFilePath", "rootPackageJson", "getWatchFolders", "projectRoot", "workspaceRoot", "getWorkspaceRoot", "resolve", "length", "uniqueItems", "pkg", "dirname", "items", "Set"], "sources": ["../src/getWatchFolders.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\n\nimport { getWorkspaceRoot } from './getModulesPaths';\n\nfunction readJsonFile(filePath: string) {\n  // Read with fs\n  const file = fs.readFileSync(filePath, 'utf8');\n  // Parse with JSON.parse\n  return JSON.parse(file);\n}\n\nfunction isValidJsonFile(filePath: string): boolean {\n  try {\n    // Throws if invalid or unable to read.\n    readJsonFile(filePath);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * @param workspaceProjectRoot Root file path for the yarn workspace\n * @param linkedPackages List of folders that contain linked node modules, ex: `['packages/*', 'apps/*']`\n * @returns List of valid package.json file paths, ex: `['/Users/<USER>/app/apps/my-app/package.json', '/Users/<USER>/app/packages/my-package/package.json']`\n */\nexport function globAllPackageJsonPaths(\n  workspaceProjectRoot: string,\n  linkedPackages: string[]\n): string[] {\n  return linkedPackages\n    .map((glob) => {\n      return globSync(path.join(glob, 'package.json').replace(/\\\\/g, '/'), {\n        cwd: workspaceProjectRoot,\n        absolute: true,\n        ignore: ['**/@(Carthage|Pods|node_modules)/**'],\n      }).map((pkgPath) => {\n        return isValidJsonFile(pkgPath) ? pkgPath : null;\n      });\n    })\n    .flat()\n    .filter(Boolean)\n    .map((p) => path.join(p as string));\n}\n\nfunction getWorkspacePackagesArray({ workspaces }: any): string[] {\n  if (Array.isArray(workspaces)) {\n    return workspaces;\n  }\n\n  assert(workspaces?.packages, 'Could not find a `workspaces` object in the root package.json');\n\n  return workspaces.packages;\n}\n\n/**\n * @param workspaceProjectRoot root file path for a yarn workspace.\n * @returns list of package.json file paths that are linked to the yarn workspace.\n */\nexport function resolveAllWorkspacePackageJsonPaths(workspaceProjectRoot: string) {\n  try {\n    const rootPackageJsonFilePath = path.join(workspaceProjectRoot, 'package.json');\n    // Could throw if package.json is invalid.\n    const rootPackageJson = readJsonFile(rootPackageJsonFilePath);\n\n    // Extract the \"packages\" array or use \"workspaces\" as packages array (yarn workspaces spec).\n    const packages = getWorkspacePackagesArray(rootPackageJson);\n\n    // Glob all package.json files and return valid paths.\n    return globAllPackageJsonPaths(workspaceProjectRoot, packages);\n  } catch {\n    return [];\n  }\n}\n\n/**\n * @param projectRoot file path to app's project root\n * @returns list of node module paths to watch in Metro bundler, ex: `['/Users/<USER>/app/node_modules/', '/Users/<USER>/app/apps/my-app/', '/Users/<USER>/app/packages/my-package/']`\n */\nexport function getWatchFolders(projectRoot: string): string[] {\n  const workspaceRoot = getWorkspaceRoot(path.resolve(projectRoot));\n  // Rely on default behavior in standard projects.\n  if (!workspaceRoot) {\n    return [];\n  }\n\n  const packages = resolveAllWorkspacePackageJsonPaths(workspaceRoot);\n  if (!packages.length) {\n    return [];\n  }\n\n  return uniqueItems([\n    path.join(workspaceRoot, 'node_modules'),\n    ...packages.map((pkg) => path.dirname(pkg)),\n  ]);\n}\n\nfunction uniqueItems(items: string[]): string[] {\n  return [...new Set(items)];\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,iBAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,gBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqD,SAAAC,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErD,SAASG,YAAYA,CAACC,QAAgB,EAAE;EACtC;EACA,MAAMC,IAAI,GAAGC,aAAE,CAACC,YAAY,CAACH,QAAQ,EAAE,MAAM,CAAC;EAC9C;EACA,OAAOI,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;AACzB;AAEA,SAASK,eAAeA,CAACN,QAAgB,EAAW;EAClD,IAAI;IACF;IACAD,YAAY,CAACC,QAAQ,CAAC;IACtB,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASO,uBAAuBA,CACrCC,oBAA4B,EAC5BC,cAAwB,EACd;EACV,OAAOA,cAAc,CAClBC,GAAG,CAAEC,IAAI,IAAK;IACb,OAAO,IAAAC,YAAQ,EAACC,eAAI,CAACC,IAAI,CAACH,IAAI,EAAE,cAAc,CAAC,CAACI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;MACnEC,GAAG,EAAER,oBAAoB;MACzBS,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC,CAACR,GAAG,CAAES,OAAO,IAAK;MAClB,OAAOb,eAAe,CAACa,OAAO,CAAC,GAAGA,OAAO,GAAG,IAAI;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC,CACDC,IAAI,EAAE,CACNC,MAAM,CAACC,OAAO,CAAC,CACfZ,GAAG,CAAEa,CAAC,IAAKV,eAAI,CAACC,IAAI,CAACS,CAAC,CAAW,CAAC;AACvC;AAEA,SAASC,yBAAyBA,CAAC;EAAEC;AAAgB,CAAC,EAAY;EAChE,IAAIC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;IAC7B,OAAOA,UAAU;EACnB;EAEA,IAAAG,iBAAM,EAACH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEI,QAAQ,EAAE,+DAA+D,CAAC;EAE7F,OAAOJ,UAAU,CAACI,QAAQ;AAC5B;;AAEA;AACA;AACA;AACA;AACO,SAASC,mCAAmCA,CAACtB,oBAA4B,EAAE;EAChF,IAAI;IACF,MAAMuB,uBAAuB,GAAGlB,eAAI,CAACC,IAAI,CAACN,oBAAoB,EAAE,cAAc,CAAC;IAC/E;IACA,MAAMwB,eAAe,GAAGjC,YAAY,CAACgC,uBAAuB,CAAC;;IAE7D;IACA,MAAMF,QAAQ,GAAGL,yBAAyB,CAACQ,eAAe,CAAC;;IAE3D;IACA,OAAOzB,uBAAuB,CAACC,oBAAoB,EAAEqB,QAAQ,CAAC;EAChE,CAAC,CAAC,MAAM;IACN,OAAO,EAAE;EACX;AACF;;AAEA;AACA;AACA;AACA;AACO,SAASI,eAAeA,CAACC,WAAmB,EAAY;EAC7D,MAAMC,aAAa,GAAG,IAAAC,mCAAgB,EAACvB,eAAI,CAACwB,OAAO,CAACH,WAAW,CAAC,CAAC;EACjE;EACA,IAAI,CAACC,aAAa,EAAE;IAClB,OAAO,EAAE;EACX;EAEA,MAAMN,QAAQ,GAAGC,mCAAmC,CAACK,aAAa,CAAC;EACnE,IAAI,CAACN,QAAQ,CAACS,MAAM,EAAE;IACpB,OAAO,EAAE;EACX;EAEA,OAAOC,WAAW,CAAC,CACjB1B,eAAI,CAACC,IAAI,CAACqB,aAAa,EAAE,cAAc,CAAC,EACxC,GAAGN,QAAQ,CAACnB,GAAG,CAAE8B,GAAG,IAAK3B,eAAI,CAAC4B,OAAO,CAACD,GAAG,CAAC,CAAC,CAC5C,CAAC;AACJ;AAEA,SAASD,WAAWA,CAACG,KAAe,EAAY;EAC9C,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACD,KAAK,CAAC,CAAC;AAC5B"}