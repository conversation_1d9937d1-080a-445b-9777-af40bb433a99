{"version": 3, "file": "resolveAssetSource.js", "sourceRoot": "", "sources": ["../src/resolveAssetSource.ts"], "names": [], "mappings": "AAAA,OAAO,kBAAkB,MAAM,iDAAiD,CAAC;AACjF,eAAe,kBAAkB,CAAC;AAClC,cAAc,iDAAiD,CAAC,CAAC,oCAAoC", "sourcesContent": ["import resolveAssetSource from 'react-native/Libraries/Image/resolveAssetSource';\nexport default resolveAssetSource;\nexport * from 'react-native/Libraries/Image/resolveAssetSource'; // eslint-disable-line import/export\n"]}