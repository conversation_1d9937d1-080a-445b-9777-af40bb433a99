{"version": 3, "file": "hash.js", "names": ["_crypto", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "hashString", "str", "crypto", "createHash", "update", "digest"], "sources": ["../../src/utils/hash.ts"], "sourcesContent": ["import crypto from 'crypto';\n\nexport function hashString(str: string) {\n  return crypto.createHash('md5').update(str).digest('hex');\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAErB,SAASG,UAAUA,CAACC,GAAW,EAAE;EACtC,OAAOC,iBAAM,CAACC,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC;AAC3D"}