{"version": 3, "file": "metro-expo-babel-transformer.js", "names": ["_resolveFrom", "data", "_interopRequireDefault", "require", "_get<PERSON><PERSON><PERSON><PERSON>", "obj", "__esModule", "default", "transformer", "resolveTransformer", "projectRoot", "<PERSON><PERSON><PERSON>", "resolveFrom", "silent", "Error", "transform", "props", "options", "extendsBabelConfigPath", "module", "exports", "get<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../src/transformer/metro-expo-babel-transformer.ts"], "sourcesContent": ["// Copyright 2021-present 650 Industries (Expo). All rights reserved.\n\nimport resolveFrom from 'resolve-from';\n\nimport { getCacheKey } from './getCacheKey';\n\nlet transformer: any = null;\n\nfunction resolveTransformer(projectRoot: string) {\n  if (transformer) {\n    return transformer;\n  }\n  const resolvedPath = resolveFrom.silent(projectRoot, 'metro-react-native-babel-transformer');\n  if (!resolvedPath) {\n    throw new Error(\n      'Missing package \"metro-react-native-babel-transformer\" in the project. ' +\n        'This usually means `react-native` is not installed. ' +\n        'Please verify that dependencies in package.json include \"react-native\" ' +\n        'and run `yarn` or `npm install`.'\n    );\n  }\n  transformer = require(resolvedPath);\n  return transformer;\n}\n\n/**\n * Extends the default `metro-react-native-babel-transformer`\n * and uses babel-preset-expo as the default instead of metro-react-native-babel-preset.\n * This enables users to safely transpile an Expo project without\n * needing to explicitly define a `babel.config.js`\n *\n * @param filename string\n * @param options BabelTransformerOptions\n * @param plugins $PropertyType<BabelCoreOptions, 'plugins'>\n * @param src string\n *\n * @returns\n */\nfunction transform(props: {\n  filename: string;\n  options: Record<string, any> & { projectRoot: string };\n  plugins?: unknown;\n  src: string;\n}) {\n  // Use babel-preset-expo by default if available...\n  props.options.extendsBabelConfigPath = resolveFrom.silent(\n    props.options.projectRoot,\n    'babel-preset-expo'\n  );\n  return resolveTransformer(props.options.projectRoot).transform(props);\n}\n\nmodule.exports = {\n  getCacheKey,\n  transform,\n};\n"], "mappings": ";;AAEA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,aAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4C,SAAAC,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAJ5C;;AAMA,IAAIG,WAAgB,GAAG,IAAI;AAE3B,SAASC,kBAAkBA,CAACC,WAAmB,EAAE;EAC/C,IAAIF,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EACA,MAAMG,YAAY,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,sCAAsC,CAAC;EAC5F,IAAI,CAACC,YAAY,EAAE;IACjB,MAAM,IAAIG,KAAK,CACb,yEAAyE,GACvE,sDAAsD,GACtD,yEAAyE,GACzE,kCAAkC,CACrC;EACH;EACAN,WAAW,GAAGL,OAAO,CAACQ,YAAY,CAAC;EACnC,OAAOH,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,SAASA,CAACC,KAKlB,EAAE;EACD;EACAA,KAAK,CAACC,OAAO,CAACC,sBAAsB,GAAGN,sBAAW,CAACC,MAAM,CACvDG,KAAK,CAACC,OAAO,CAACP,WAAW,EACzB,mBAAmB,CACpB;EACD,OAAOD,kBAAkB,CAACO,KAAK,CAACC,OAAO,CAACP,WAAW,CAAC,CAACK,SAAS,CAACC,KAAK,CAAC;AACvE;AAEAG,MAAM,CAACC,OAAO,GAAG;EACfC,WAAW,EAAXA,0BAAW;EACXN;AACF,CAAC"}