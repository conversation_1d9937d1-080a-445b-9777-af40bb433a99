{"name": "expo", "version": "49.0.23", "description": "The Expo SDK", "main": "build/Expo.js", "module": "build/Expo.js", "types": "build/Expo.d.ts", "sideEffects": ["*.fx.js", "*.fx.web.js"], "bin": "bin/cli", "files": ["android", "bin", "build", "ios", "scripts", "tools", "AppEntry.js", "Expo.podspec", "bundledNativeModules.json", "expo-module.config.json", "metro-config.js", "metro-config.d.ts", "config.js", "config.d.ts", "config-plugins.js", "config-plugins.d.ts", "react-native.config.js", "requiresExtraSetup.json", "tsconfig.base.json"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "jest": {"preset": "expo-module-scripts"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo"}, "keywords": ["expo"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/expo", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/cli": "0.10.17", "@expo/vector-icons": "^13.0.0", "@expo/config-plugins": "7.2.5", "@expo/config": "8.1.2", "babel-preset-expo": "~9.5.2", "expo-application": "~5.3.0", "expo-asset": "~8.10.1", "expo-constants": "~14.4.2", "expo-file-system": "~15.4.5", "expo-font": "~11.4.0", "expo-keep-awake": "~12.3.0", "expo-modules-autolinking": "1.5.1", "expo-modules-core": "1.5.13", "fbemitter": "^3.0.0", "invariant": "^2.2.4", "md5-file": "^3.2.3", "node-fetch": "^2.6.7", "pretty-format": "^26.5.2", "uuid": "^3.4.0"}, "devDependencies": {"@types/fbemitter": "^2.0.32", "@types/invariant": "^2.2.33", "@types/react": "~18.0.14", "@types/react-test-renderer": "^18.0.0", "@types/uuid": "^3.4.7", "expo-module-scripts": "^3.0.11", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10"}, "gitHead": "91484b15d549e5d65acfee9b87c4adefc9e77159"}