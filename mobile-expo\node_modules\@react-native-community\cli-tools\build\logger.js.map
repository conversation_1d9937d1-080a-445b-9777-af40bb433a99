{"version": 3, "names": ["SEPARATOR", "verbose", "disabled", "hidden", "formatMessages", "messages", "chalk", "reset", "join", "success", "console", "log", "green", "bold", "info", "cyan", "warn", "yellow", "error", "red", "debug", "gray", "setVerbose", "level", "isVerbose", "disable", "enable", "hasDebugMessages"], "sources": ["../src/logger.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nconst SEPARATOR = ', ';\n\nlet verbose = false;\nlet disabled = false;\nlet hidden = false;\n\nconst formatMessages = (messages: Array<string>) =>\n  chalk.reset(messages.join(SEPARATOR));\n\nconst success = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${chalk.green.bold('success')} ${formatMessages(messages)}`);\n  }\n};\n\nconst info = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${chalk.cyan.bold('info')} ${formatMessages(messages)}`);\n  }\n};\n\nconst warn = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.warn(`${chalk.yellow.bold('warn')} ${formatMessages(messages)}`);\n  }\n};\n\nconst error = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.error(`${chalk.red.bold('error')} ${formatMessages(messages)}`);\n  }\n};\n\nconst debug = (...messages: Array<string>) => {\n  if (verbose && !disabled) {\n    console.log(`${chalk.gray.bold('debug')} ${formatMessages(messages)}`);\n  } else {\n    hidden = true;\n  }\n};\n\nconst log = (...messages: Array<string>) => {\n  if (!disabled) {\n    console.log(`${formatMessages(messages)}`);\n  }\n};\n\nconst setVerbose = (level: boolean) => {\n  verbose = level;\n};\n\nconst isVerbose = () => verbose;\n\nconst disable = () => {\n  disabled = true;\n};\n\nconst enable = () => {\n  disabled = false;\n};\n\nconst hasDebugMessages = () => hidden;\n\nexport default {\n  success,\n  info,\n  warn,\n  error,\n  debug,\n  log,\n  setVerbose,\n  isVerbose,\n  hasDebugMessages,\n  disable,\n  enable,\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAE1B,MAAMA,SAAS,GAAG,IAAI;AAEtB,IAAIC,OAAO,GAAG,KAAK;AACnB,IAAIC,QAAQ,GAAG,KAAK;AACpB,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,cAAc,GAAIC,QAAuB,IAC7CC,gBAAK,CAACC,KAAK,CAACF,QAAQ,CAACG,IAAI,CAACR,SAAS,CAAC,CAAC;AAEvC,MAAMS,OAAO,GAAG,CAAC,GAAGJ,QAAuB,KAAK;EAC9C,IAAI,CAACH,QAAQ,EAAE;IACbQ,OAAO,CAACC,GAAG,CAAE,GAAEL,gBAAK,CAACM,KAAK,CAACC,IAAI,CAAC,SAAS,CAAE,IAAGT,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EAC3E;AACF,CAAC;AAED,MAAMS,IAAI,GAAG,CAAC,GAAGT,QAAuB,KAAK;EAC3C,IAAI,CAACH,QAAQ,EAAE;IACbQ,OAAO,CAACC,GAAG,CAAE,GAAEL,gBAAK,CAACS,IAAI,CAACF,IAAI,CAAC,MAAM,CAAE,IAAGT,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EACvE;AACF,CAAC;AAED,MAAMW,IAAI,GAAG,CAAC,GAAGX,QAAuB,KAAK;EAC3C,IAAI,CAACH,QAAQ,EAAE;IACbQ,OAAO,CAACM,IAAI,CAAE,GAAEV,gBAAK,CAACW,MAAM,CAACJ,IAAI,CAAC,MAAM,CAAE,IAAGT,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EAC1E;AACF,CAAC;AAED,MAAMa,KAAK,GAAG,CAAC,GAAGb,QAAuB,KAAK;EAC5C,IAAI,CAACH,QAAQ,EAAE;IACbQ,OAAO,CAACQ,KAAK,CAAE,GAAEZ,gBAAK,CAACa,GAAG,CAACN,IAAI,CAAC,OAAO,CAAE,IAAGT,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EACzE;AACF,CAAC;AAED,MAAMe,KAAK,GAAG,CAAC,GAAGf,QAAuB,KAAK;EAC5C,IAAIJ,OAAO,IAAI,CAACC,QAAQ,EAAE;IACxBQ,OAAO,CAACC,GAAG,CAAE,GAAEL,gBAAK,CAACe,IAAI,CAACR,IAAI,CAAC,OAAO,CAAE,IAAGT,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EACxE,CAAC,MAAM;IACLF,MAAM,GAAG,IAAI;EACf;AACF,CAAC;AAED,MAAMQ,GAAG,GAAG,CAAC,GAAGN,QAAuB,KAAK;EAC1C,IAAI,CAACH,QAAQ,EAAE;IACbQ,OAAO,CAACC,GAAG,CAAE,GAAEP,cAAc,CAACC,QAAQ,CAAE,EAAC,CAAC;EAC5C;AACF,CAAC;AAED,MAAMiB,UAAU,GAAIC,KAAc,IAAK;EACrCtB,OAAO,GAAGsB,KAAK;AACjB,CAAC;AAED,MAAMC,SAAS,GAAG,MAAMvB,OAAO;AAE/B,MAAMwB,OAAO,GAAG,MAAM;EACpBvB,QAAQ,GAAG,IAAI;AACjB,CAAC;AAED,MAAMwB,MAAM,GAAG,MAAM;EACnBxB,QAAQ,GAAG,KAAK;AAClB,CAAC;AAED,MAAMyB,gBAAgB,GAAG,MAAMxB,MAAM;AAAC,eAEvB;EACbM,OAAO;EACPK,IAAI;EACJE,IAAI;EACJE,KAAK;EACLE,KAAK;EACLT,GAAG;EACHW,UAAU;EACVE,SAAS;EACTG,gBAAgB;EAChBF,OAAO;EACPC;AACF,CAAC;AAAA"}