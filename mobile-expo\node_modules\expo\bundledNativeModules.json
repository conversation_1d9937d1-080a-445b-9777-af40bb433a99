{"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/datetimepicker": "7.2.0", "@react-native-masked-view/masked-view": "0.2.9", "@react-native-community/netinfo": "9.3.10", "@react-native-community/slider": "4.4.2", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "2.4.10", "@react-native-segmented-control/segmented-control": "2.4.1", "@stripe/stripe-react-native": "0.28.0", "expo-analytics-amplitude": "~11.3.0", "expo-app-auth": "~11.1.0", "expo-app-loader-provider": "~8.0.0", "expo-apple-authentication": "~6.1.0", "expo-application": "~5.3.0", "expo-asset": "~8.10.1", "expo-auth-session": "~5.0.2", "expo-av": "~13.4.1", "expo-background-fetch": "~11.3.0", "expo-barcode-scanner": "~12.5.3", "expo-battery": "~7.3.0", "expo-blur": "~12.4.1", "expo-brightness": "~11.4.1", "expo-build-properties": "~0.8.3", "expo-calendar": "~11.3.2", "expo-camera": "~13.4.4", "expo-cellular": "~5.3.0", "expo-checkbox": "~2.4.0", "expo-clipboard": "~4.3.1", "expo-constants": "~14.4.2", "expo-contacts": "~12.2.0", "expo-crypto": "~12.4.1", "expo-dev-client": "~2.4.12", "expo-device": "~5.4.0", "expo-document-picker": "~11.5.4", "expo-face-detector": "~12.2.0", "expo-file-system": "~15.4.5", "expo-font": "~11.4.0", "expo-gl": "~13.0.1", "expo-google-app-auth": "~8.3.0", "expo-haptics": "~12.4.0", "expo-image": "~1.3.5", "expo-image-loader": "~4.3.0", "expo-image-manipulator": "~11.3.0", "expo-image-picker": "~14.3.2", "expo-in-app-purchases": "~14.3.0", "expo-insights": "~0.2.0", "expo-intent-launcher": "~10.7.0", "expo-keep-awake": "~12.3.0", "expo-linear-gradient": "~12.3.0", "expo-linking": "~5.0.2", "expo-local-authentication": "~13.4.1", "expo-localization": "~14.3.0", "expo-location": "~16.1.0", "expo-mail-composer": "~12.3.0", "expo-media-library": "~15.4.1", "expo-module-template": "~10.9.7", "expo-modules-core": "~1.5.13", "expo-navigation-bar": "~2.3.0", "expo-network": "~5.4.0", "expo-notifications": "~0.20.1", "expo-permissions": "~14.2.1", "expo-print": "~12.4.2", "expo-random": "~13.2.0", "expo-router": "^2.0.0", "expo-screen-capture": "~5.3.0", "expo-screen-orientation": "~6.0.6", "expo-secure-store": "~12.3.1", "expo-sensors": "~12.3.0", "expo-sharing": "~11.5.0", "expo-sms": "~11.4.0", "expo-speech": "~11.3.0", "expo-splash-screen": "~0.20.5", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "expo-store-review": "~6.4.0", "expo-system-ui": "~2.4.0", "expo-task-manager": "~11.3.0", "expo-tracking-transparency": "~3.1.0", "expo-updates": "~0.18.19", "expo-video-thumbnails": "~7.4.0", "expo-web-browser": "~12.3.2", "jest-expo": "~49.0.0", "lottie-react-native": "5.1.6", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.10", "react-native-web": "~0.19.6", "react-native-gesture-handler": "~2.12.0", "react-native-get-random-values": "~1.9.0", "react-native-maps": "1.7.1", "react-native-pager-view": "6.2.0", "react-native-reanimated": "~3.3.0", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-svg": "13.9.0", "react-native-view-shot": "3.7.0", "react-native-webview": "13.2.2", "sentry-expo": "~7.1.0", "unimodules-app-loader": "~4.2.0", "unimodules-image-loader-interface": "~6.1.0", "@shopify/react-native-skia": "0.1.196", "@shopify/flash-list": "1.4.3", "@sentry/react-native": "5.10.0"}