{"version": 3, "names": ["isPackagerRunning", "packagerPort", "process", "env", "RCT_METRO_PORT", "data", "fetch", "_error"], "sources": ["../src/isPackagerRunning.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {fetch} from './fetch';\n\n/**\n * Indicates whether or not the packager is running. It returns a promise that\n * returns one of these possible values:\n *   - `running`: the packager is running\n *   - `not_running`: the packager nor any process is running on the expected port.\n *   - `unrecognized`: one other process is running on the port we expect the packager to be running.\n */\nasync function isPackagerRunning(\n  packagerPort: string | number = process.env.RCT_METRO_PORT || '8081',\n): Promise<'running' | 'not_running' | 'unrecognized'> {\n  try {\n    const {data} = await fetch(`http://localhost:${packagerPort}/status`);\n\n    return data === 'packager-status:running' ? 'running' : 'unrecognized';\n  } catch (_error) {\n    return 'not_running';\n  }\n}\n\nexport default isPackagerRunning;\n"], "mappings": ";;;;;;AAQA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeA,iBAAiB,CAC9BC,YAA6B,GAAGC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAI,MAAM,EACf;EACrD,IAAI;IACF,MAAM;MAACC;IAAI,CAAC,GAAG,MAAM,IAAAC,YAAK,EAAE,oBAAmBL,YAAa,SAAQ,CAAC;IAErE,OAAOI,IAAI,KAAK,yBAAyB,GAAG,SAAS,GAAG,cAAc;EACxE,CAAC,CAAC,OAAOE,MAAM,EAAE;IACf,OAAO,aAAa;EACtB;AACF;AAAC,eAEcP,iBAAiB;AAAA"}