{"version": 3, "file": "rewriteRequestUrl.js", "names": ["_config", "data", "require", "_paths", "_chalk", "_interopRequireDefault", "_path", "_getModulesPaths", "obj", "__esModule", "default", "debug", "getRewriteRequestUrl", "projectRoot", "rewriteExpoRequestUrl", "url", "includes", "_searchParams$get", "ensured", "startsWith", "URL", "search", "searchParams", "platform", "get", "entry", "resolveEntryPoint", "projectConfig", "pkg", "getPackageJson", "Error", "chalk", "serverRoot", "getServerRoot", "relativeEntry", "path", "relative", "replace", "pathname", "toString"], "sources": ["../src/rewriteRequestUrl.ts"], "sourcesContent": ["// Copyright 2023-present 650 Industries (Expo). All rights reserved.\nimport { getPackageJson } from '@expo/config';\nimport { resolveEntryPoint } from '@expo/config/paths';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { getServerRoot } from './getModulesPaths';\n\nconst debug = require('debug')('expo:metro:config:rewriteRequestUrl');\n\nexport function getRewriteRequestUrl(projectRoot: string) {\n  function rewriteExpoRequestUrl(url: string): string {\n    // Like: `/.expo/.virtual-metro-entry.bundle?platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.bacon.test-custom-entry`\n    // Sometimes a fully qualified URL is passed in, e.g. `http://localhost:19001/.expo/.virtual-metro-entry.bundle?platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.bacon.test-custom-entry`\n    if (url.includes('/.expo/.virtual-metro-entry.bundle?')) {\n      const ensured = url.startsWith('/') ? new URL(url, 'https://acme.dev') : new URL(url);\n      // TODO: Maybe this function could be memoized in some capacity?\n      const { search, searchParams } = ensured;\n\n      const platform = searchParams.get('platform') ?? 'web';\n\n      debug('Rewriting magic request url to entry point', { url, platform });\n\n      const entry = resolveEntryPoint(projectRoot, {\n        platform,\n        // @ts-ignore\n        projectConfig: {\n          pkg: getPackageJson(projectRoot),\n        },\n      });\n\n      if (!entry) {\n        throw new Error(\n          chalk`The project entry file could not be resolved (platform: ${platform}, root: ${projectRoot}). Define it in the {bold package.json} \"main\" field.`\n        );\n      }\n\n      const serverRoot = getServerRoot(projectRoot);\n      const relativeEntry = path.relative(serverRoot, entry).replace(/\\.[tj]sx?$/, '');\n      debug('Resolved entry point', { entry, relativeEntry, serverRoot });\n\n      // Only return the pathname when url is relative\n      if (url.startsWith('/')) {\n        // Like: `/index.bundle?platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.bacon.test-custom-entry`\n        return '/' + relativeEntry + '.bundle' + search;\n      }\n\n      // Modify the pathname within the URL and return the full URL\n      ensured.pathname = '/' + relativeEntry + '.bundle';\n      // Like: `http://localhost:19001/index.bundle?platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true&app=com.bacon.test-custom-entry`\n      return ensured.toString();\n    }\n\n    return url;\n  }\n  return rewriteExpoRequestUrl;\n}\n"], "mappings": ";;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,iBAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,gBAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkD,SAAAI,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AANlD;;AAQA,MAAMG,KAAK,GAAGT,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC;AAE9D,SAASU,oBAAoBA,CAACC,WAAmB,EAAE;EACxD,SAASC,qBAAqBA,CAACC,GAAW,EAAU;IAClD;IACA;IACA,IAAIA,GAAG,CAACC,QAAQ,CAAC,qCAAqC,CAAC,EAAE;MAAA,IAAAC,iBAAA;MACvD,MAAMC,OAAO,GAAGH,GAAG,CAACI,UAAU,CAAC,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACL,GAAG,EAAE,kBAAkB,CAAC,GAAG,IAAIK,GAAG,CAACL,GAAG,CAAC;MACrF;MACA,MAAM;QAAEM,MAAM;QAAEC;MAAa,CAAC,GAAGJ,OAAO;MAExC,MAAMK,QAAQ,IAAAN,iBAAA,GAAGK,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,cAAAP,iBAAA,cAAAA,iBAAA,GAAI,KAAK;MAEtDN,KAAK,CAAC,4CAA4C,EAAE;QAAEI,GAAG;QAAEQ;MAAS,CAAC,CAAC;MAEtE,MAAME,KAAK,GAAG,IAAAC,0BAAiB,EAACb,WAAW,EAAE;QAC3CU,QAAQ;QACR;QACAI,aAAa,EAAE;UACbC,GAAG,EAAE,IAAAC,wBAAc,EAAChB,WAAW;QACjC;MACF,CAAC,CAAC;MAEF,IAAI,CAACY,KAAK,EAAE;QACV,MAAM,IAAIK,KAAK,CACb,IAAAC,gBAAK,CAAC,2DAA0DR,QAAS,WAAUV,WAAY,uDAAsD,CACtJ;MACH;MAEA,MAAMmB,UAAU,GAAG,IAAAC,gCAAa,EAACpB,WAAW,CAAC;MAC7C,MAAMqB,aAAa,GAAGC,eAAI,CAACC,QAAQ,CAACJ,UAAU,EAAEP,KAAK,CAAC,CAACY,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;MAChF1B,KAAK,CAAC,sBAAsB,EAAE;QAAEc,KAAK;QAAES,aAAa;QAAEF;MAAW,CAAC,CAAC;;MAEnE;MACA,IAAIjB,GAAG,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;QACvB;QACA,OAAO,GAAG,GAAGe,aAAa,GAAG,SAAS,GAAGb,MAAM;MACjD;;MAEA;MACAH,OAAO,CAACoB,QAAQ,GAAG,GAAG,GAAGJ,aAAa,GAAG,SAAS;MAClD;MACA,OAAOhB,OAAO,CAACqB,QAAQ,EAAE;IAC3B;IAEA,OAAOxB,GAAG;EACZ;EACA,OAAOD,qBAAqB;AAC9B"}