{"version": 3, "names": ["getConfigurationScheme", "scheme", "mode", "sourceDir", "configuration", "getBuildConfigurationFromXcScheme"], "sources": ["../../src/tools/getConfigurationScheme.ts"], "sourcesContent": ["import {getBuildConfigurationFromXcScheme} from './getBuildConfigurationFromXcScheme';\n\ninterface Args {\n  scheme?: string;\n  mode: string;\n}\n\nexport function getConfigurationScheme(\n  {scheme, mode}: Args,\n  sourceDir: string,\n) {\n  if (scheme && mode) {\n    return mode;\n  }\n\n  const configuration = mode || 'Debug';\n  if (scheme) {\n    return getBuildConfigurationFromXcScheme(scheme, configuration, sourceDir);\n  }\n\n  return configuration;\n}\n"], "mappings": ";;;;;;AAAA;AAOO,SAASA,sBAAsB,CACpC;EAACC,MAAM;EAAEC;AAAU,CAAC,EACpBC,SAAiB,EACjB;EACA,IAAIF,MAAM,IAAIC,IAAI,EAAE;IAClB,OAAOA,IAAI;EACb;EAEA,MAAME,aAAa,GAAGF,IAAI,IAAI,OAAO;EACrC,IAAID,MAAM,EAAE;IACV,OAAO,IAAAI,oEAAiC,EAACJ,MAAM,EAAEG,aAAa,EAAED,SAAS,CAAC;EAC5E;EAEA,OAAOC,aAAa;AACtB"}