{"version": 3, "sources": ["../../src/ios/constants.ts"], "names": ["codes", "DEBUG", "DEFAULT", "INFO", "ERROR", "Priority", "fromName", "name", "value", "toUpperCase", "to<PERSON>ame", "code", "Object", "keys", "find", "key", "fromLetter", "letter", "toLetter"], "mappings": ";;;;;;AAAA,MAAMA,KAAK,GAAG;AACZC,EAAAA,KAAK,EAAE,CADK;AAEZC,EAAAA,OAAO,EAAE,CAFG;AAGZC,EAAAA,IAAI,EAAE,CAHM;AAIZC,EAAAA,KAAK,EAAE;AAJK,CAAd;AASO,MAAMC,QAAQ,GAAG,EACtB,GAAGL,KADmB;;AAEtBM,EAAAA,QAAQ,CAACC,IAAD,EAA8B;AACpC,UAAMC,KAAK,GAAGR,KAAK,CAACO,IAAI,CAACE,WAAL,EAAD,CAAnB;AACA,WAAOD,KAAK,GAAGA,KAAH,GAAW,CAAvB;AACD,GALqB;;AAMtBE,EAAAA,MAAM,CAACC,IAAD,EAA8B;AAClC,WACGC,MAAM,CAACC,IAAP,CAAYb,KAAZ,CAAD,CAAwCc,IAAxC,CACGC,GAAD,IAAwBf,KAAK,CAACe,GAAD,CAAL,KAAeJ,IADzC,KAEK,SAHP;AAKD,GAZqB;;AAatBK,EAAAA,UAAU,CAACC,MAAD,EAAyB;AACjC,WAAOjB,KAAK,CACTY,MAAM,CAACC,IAAP,CAAYb,KAAZ,CAAD,CAAwCc,IAAxC,CACGC,GAAD,IAAwBA,GAAG,CAAC,CAAD,CAAH,KAAWE,MAAM,CAACR,WAAP,EADrC,KAEK,SAHK,CAAZ;AAKD,GAnBqB;;AAoBtBS,EAAAA,QAAQ,CAACP,IAAD,EAAuB;AAC7B,WAAON,QAAQ,CAACK,MAAT,CAAgBC,IAAhB,EAAsB,CAAtB,CAAP;AACD;;AAtBqB,CAAjB", "sourcesContent": ["const codes = {\n  DEBUG: 0,\n  DEFAULT: 1,\n  INFO: 2,\n  ERROR: 3,\n};\n\nexport type PriorityNames = keyof typeof codes;\n\nexport const Priority = {\n  ...codes,\n  fromName(name: PriorityNames): number {\n    const value = codes[name.toUpperCase() as PriorityNames];\n    return value ? value : 0;\n  },\n  toName(code: number): PriorityNames {\n    return (\n      (Object.keys(codes) as PriorityNames[]).find(\n        (key: PriorityNames) => codes[key] === code\n      ) || 'DEFAULT'\n    );\n  },\n  fromLetter(letter: string): number {\n    return codes[\n      (Object.keys(codes) as PriorityNames[]).find(\n        (key: PriorityNames) => key[0] === letter.toUpperCase()\n      ) || 'DEFAULT'\n    ];\n  },\n  toLetter(code: number): string {\n    return Priority.toName(code)[0];\n  },\n};\n"], "file": "constants.js"}