/// <reference types="node" />
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import http from 'http';
export default function securityHeadersMiddleware(req: http.IncomingMessage, res: http.ServerResponse, next: (err?: any) => void): void;
//# sourceMappingURL=securityHeadersMiddleware.d.ts.map