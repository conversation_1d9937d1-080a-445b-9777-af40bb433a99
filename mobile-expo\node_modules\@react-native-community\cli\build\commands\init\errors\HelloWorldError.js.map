{"version": 3, "names": ["HelloWorldError", "CLIError", "constructor"], "sources": ["../../../../src/commands/init/errors/HelloWorldError.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\n\nexport default class HelloWorldError extends CLIError {\n  constructor() {\n    super(\n      'Project name shouldn\\'t contain \"HelloWorld\" name in it, because it is CLI\\'s default placeholder name.',\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEe,MAAMA,eAAe,SAASC,oBAAQ,CAAC;EACpDC,WAAW,GAAG;IACZ,KAAK,CACH,yGAAyG,CAC1G;EACH;AACF;AAAC"}