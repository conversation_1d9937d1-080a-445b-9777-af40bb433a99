{"version":3,"names":["packageManagers","yarn","init","install","installDev","uninstall","installAll","npm","configurePackageManager","packageNames","action","options","pm","shouldUseYarn","executable","flags","args","executeCommand","command","execa","stdio","silent","logger","isVerbose","cwd","root","preferYarn","undefined","getYarnVersionIfAvailable","isProjectUsingYarn"],"sources":["../../src/tools/packageManager.ts"],"sourcesContent":["import execa from 'execa';\nimport {logger} from '@react-native-community/cli-tools';\nimport {getYarnVersionIfAvailable, isProjectUsingYarn} from './yarn';\n\ntype Options = {\n  preferYarn?: boolean;\n  silent?: boolean;\n  root: string;\n};\n\nconst packageManagers = {\n  yarn: {\n    init: ['init', '-y'],\n    install: ['add'],\n    installDev: ['add', '-D'],\n    uninstall: ['remove'],\n    installAll: ['install'],\n  },\n  npm: {\n    init: ['init', '-y'],\n    install: ['install', '--save', '--save-exact'],\n    installDev: ['install', '--save-dev', '--save-exact'],\n    uninstall: ['uninstall', '--save'],\n    installAll: ['install'],\n  },\n};\n\nfunction configurePackageManager(\n  packageNames: Array<string>,\n  action: 'init' | 'install' | 'installDev' | 'installAll' | 'uninstall',\n  options: Options,\n) {\n  const pm = shouldUseYarn(options) ? 'yarn' : 'npm';\n  const [executable, ...flags] = packageManagers[pm][action];\n  const args = [executable, ...flags, ...packageNames];\n  return executeCommand(pm, args, options);\n}\n\nfunction executeCommand(\n  command: string,\n  