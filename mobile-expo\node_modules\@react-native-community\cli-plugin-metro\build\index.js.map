{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["export type {MetroConfig} from 'metro-config';\nexport {\n  Config,\n  ConfigLoadingContext,\n  default as loadMetroConfig,\n} from './tools/loadMetroConfig';\nexport {\n  default as commands,\n  buildBundleWithConfig,\n  CommandLineArgs,\n} from './commands';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAKA;AAIoB;AAAA"}