{"name": "semver", "version": "7.3.2", "description": "The semantic version parser used by npm.", "main": "index.js", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.10.7"}, "license": "ISC", "repository": "https://github.com/npm/node-semver", "bin": {"semver": "./bin/semver.js"}, "files": ["bin/**/*.js", "range.bnf", "classes/**/*.js", "functions/**/*.js", "internal/**/*.js", "ranges/**/*.js", "index.js", "preload.js"], "tap": {"check-coverage": true, "coverage-map": "map.js"}, "engines": {"node": ">=10"}}