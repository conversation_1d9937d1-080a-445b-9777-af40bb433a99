{"version": 3, "file": "metro-expo-exotic-babel-transformer.js", "names": ["_createExoticTransformer", "data", "require", "module", "exports", "createExoticTransformer", "nodeModulesPaths"], "sources": ["../../src/transformer/metro-expo-exotic-babel-transformer.ts"], "sourcesContent": ["// Copyright 2021-present 650 Industries (Expo). All rights reserved.\n\nimport { createExoticTransformer } from './createExoticTransformer';\n\nmodule.exports = createExoticTransformer({ nodeModulesPaths: ['node_modules'] });\n"], "mappings": ";;AAEA,SAAAA,yBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,wBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAFA;;AAIAE,MAAM,CAACC,OAAO,GAAG,IAAAC,kDAAuB,EAAC;EAAEC,gBAAgB,EAAE,CAAC,cAAc;AAAE,CAAC,CAAC"}