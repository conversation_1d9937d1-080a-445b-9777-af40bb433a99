{"version": 3, "names": ["getSimulators", "simulators", "JSON", "parse", "child_process", "execFileSync", "encoding", "error", "CLIError"], "sources": ["../../src/tools/getSimulators.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport child_process from 'child_process';\nimport {Device} from '../types';\n\nconst getSimulators = () => {\n  let simulators: {devices: {[index: string]: Array<Device>}};\n\n  try {\n    simulators = JSON.parse(\n      child_process.execFileSync(\n        'xcrun',\n        ['simctl', 'list', '--json', 'devices'],\n        {encoding: 'utf8'},\n      ),\n    );\n  } catch (error) {\n    throw new CLIError(\n      'Could not get the simulator list from Xcode. Please open Xcode and try running project directly from there to resolve the remaining issues.',\n    );\n  }\n  return simulators;\n};\n\nexport default getSimulators;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0C;AAG1C,MAAMA,aAAa,GAAG,MAAM;EAC1B,IAAIC,UAAuD;EAE3D,IAAI;IACFA,UAAU,GAAGC,IAAI,CAACC,KAAK,CACrBC,wBAAa,CAACC,YAAY,CACxB,OAAO,EACP,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,EACvC;MAACC,QAAQ,EAAE;IAAM,CAAC,CACnB,CACF;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,6IAA6I,CAC9I;EACH;EACA,OAAOP,UAAU;AACnB,CAAC;AAAC,eAEaD,aAAa;AAAA"}