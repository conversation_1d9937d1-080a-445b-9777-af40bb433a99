{"version": 3, "names": ["getReactNativeVersion", "projectRoot", "require", "path", "join", "resolveNodeModuleDir", "version", "logIfUpdateAvailable", "hasUpdate", "latest", "printNewRelease", "name", "upgrade", "current", "currentVersion", "latestRelease", "getLatestRelease", "e", "logger", "debug", "undefined", "found", "semver", "parse", "UnknownProjectError"], "sources": ["../../src/releaseChecker/index.ts"], "sourcesContent": ["import path from 'path';\nimport semver, {SemVer} from 'semver';\n\nimport {UnknownProjectError} from '../errors';\nimport logger from '../logger';\nimport resolveNodeModuleDir from '../resolveNodeModuleDir';\nimport getLatestRelease, {Release} from './getLatestRelease';\nimport printNewRelease from './printNewRelease';\n\nconst getReactNativeVersion = (projectRoot: string): string | undefined =>\n  require(path.join(\n    resolveNodeModuleDir(projectRoot, 'react-native'),\n    'package.json',\n  ))?.version;\n\n/**\n * Logs out a message if the user's version is behind a stable version of React Native\n */\nexport async function logIfUpdateAvailable(projectRoot: string): Promise<void> {\n  const hasUpdate = await latest(projectRoot);\n  if (hasUpdate) {\n    printNewRelease(hasUpdate.name, hasUpdate.upgrade, hasUpdate.current);\n  }\n}\n\ntype Update = {\n  upgrade: Release;\n  current: string;\n  name: string;\n};\n\n/**\n * Finds the latest stables version of React Native > current version\n */\nexport async function latest(projectRoot: string): Promise<Update | undefined> {\n  try {\n    const currentVersion = getReactNativeVersion(projectRoot);\n    if (!currentVersion) {\n      return;\n    }\n    const {name} = require(path.join(projectRoot, 'package.json'));\n    const latestRelease = await getLatestRelease(name, currentVersion);\n\n    if (latestRelease) {\n      return {\n        name,\n        current: currentVersion,\n        upgrade: latestRelease,\n      };\n    }\n  } catch (e) {\n    // We let the flow continue as this component is not vital for the rest of\n    // the CLI.\n    logger.debug(\n      'Cannot detect current version of React Native, ' +\n        'skipping check for a newer release',\n    );\n    logger.debug(e as any);\n  }\n  return undefined;\n}\n\n/**\n * Gets the current project's version parsed as Semver\n */\nexport function current(projectRoot: string): SemVer | undefined {\n  try {\n    const found = semver.parse(getReactNativeVersion(projectRoot));\n    if (found) {\n      return found;\n    }\n  } catch {\n    throw new UnknownProjectError(projectRoot);\n  }\n  return undefined;\n}\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAgD;AAEhD,MAAMA,qBAAqB,GAAIC,WAAmB;EAAA;EAAA,mBAChDC,OAAO,CAACC,eAAI,CAACC,IAAI,CACf,IAAAC,6BAAoB,EAACJ,WAAW,EAAE,cAAc,CAAC,EACjD,cAAc,CACf,CAAC,6CAHF,SAGIK,OAAO;AAAA;;AAEb;AACA;AACA;AACO,eAAeC,oBAAoB,CAACN,WAAmB,EAAiB;EAC7E,MAAMO,SAAS,GAAG,MAAMC,MAAM,CAACR,WAAW,CAAC;EAC3C,IAAIO,SAAS,EAAE;IACb,IAAAE,wBAAe,EAACF,SAAS,CAACG,IAAI,EAAEH,SAAS,CAACI,OAAO,EAAEJ,SAAS,CAACK,OAAO,CAAC;EACvE;AACF;AAQA;AACA;AACA;AACO,eAAeJ,MAAM,CAACR,WAAmB,EAA+B;EAC7E,IAAI;IACF,MAAMa,cAAc,GAAGd,qBAAqB,CAACC,WAAW,CAAC;IACzD,IAAI,CAACa,cAAc,EAAE;MACnB;IACF;IACA,MAAM;MAACH;IAAI,CAAC,GAAGT,OAAO,CAACC,eAAI,CAACC,IAAI,CAACH,WAAW,EAAE,cAAc,CAAC,CAAC;IAC9D,MAAMc,aAAa,GAAG,MAAM,IAAAC,yBAAgB,EAACL,IAAI,EAAEG,cAAc,CAAC;IAElE,IAAIC,aAAa,EAAE;MACjB,OAAO;QACLJ,IAAI;QACJE,OAAO,EAAEC,cAAc;QACvBF,OAAO,EAAEG;MACX,CAAC;IACH;EACF,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV;IACA;IACAC,eAAM,CAACC,KAAK,CACV,iDAAiD,GAC/C,oCAAoC,CACvC;IACDD,eAAM,CAACC,KAAK,CAACF,CAAC,CAAQ;EACxB;EACA,OAAOG,SAAS;AAClB;;AAEA;AACA;AACA;AACO,SAASP,OAAO,CAACZ,WAAmB,EAAsB;EAC/D,IAAI;IACF,MAAMoB,KAAK,GAAGC,iBAAM,CAACC,KAAK,CAACvB,qBAAqB,CAACC,WAAW,CAAC,CAAC;IAC9D,IAAIoB,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;EACF,CAAC,CAAC,MAAM;IACN,MAAM,IAAIG,2BAAmB,CAACvB,WAAW,CAAC;EAC5C;EACA,OAAOmB,SAAS;AAClB"}