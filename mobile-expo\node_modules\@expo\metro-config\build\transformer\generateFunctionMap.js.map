{"version": 3, "file": "generateFunctionMap.js", "names": ["_env", "data", "require", "generateFunctionMap", "props", "env", "EXPO_USE_FB_SOURCES"], "sources": ["../../src/transformer/generateFunctionMap.ts"], "sourcesContent": ["import type { generateFunctionMap as generateFunctionMapType } from 'metro-source-map';\n\nimport { env } from '../env';\n\ntype GenerateFunctionMapParams = Parameters<typeof generateFunctionMapType>;\n\nexport function generateFunctionMap(\n  ...props: GenerateFunctionMapParams\n): ReturnType<typeof generateFunctionMapType> | null {\n  //  `x_facebook_sources` is a source map feature that we disable by default since it isn't documented\n  // and doesn't appear to add much value to the DX, it also increases bundle time, and source map size.\n  // The feature supposedly provides improved function names for anonymous functions, but we will opt towards\n  // linting to prevent users from adding anonymous functions for important features like React components.\n  //\n  // Here is an example stack trace for a component that throws an error\n  // in the root component (which is an anonymous function):\n  //\n  // Before:\n  // - <anonymous> App.js:5:9\n  // - renderApplication renderApplication.js:54:5\n  // - runnables.appKey.run AppRegistry.js:117:26\n  //\n  // After:\n  // - _default App.js:5:9\n  // - renderApplication renderApplication.js:54:5\n  // - run AppRegistry.js:117:26\n  //\n  if (env.EXPO_USE_FB_SOURCES) {\n    return (require('metro-source-map') as typeof import('metro-source-map')).generateFunctionMap(\n      ...props\n    );\n  }\n  return null;\n}\n"], "mappings": ";;;;;;AAEA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIO,SAASE,mBAAmBA,CACjC,GAAGC,KAAgC,EACgB;EACnD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,UAAG,CAACC,mBAAmB,EAAE;IAC3B,OAAQJ,OAAO,CAAC,kBAAkB,CAAC,CAAuCC,mBAAmB,CAC3F,GAAGC,KAAK,CACT;EACH;EACA,OAAO,IAAI;AACb"}