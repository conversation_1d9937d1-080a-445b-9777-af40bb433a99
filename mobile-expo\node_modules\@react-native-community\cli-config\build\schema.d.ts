/**
 * This schema is used by `cli-config` to validate the structure. Make sure
 * this file stays up to date with `cli-types` package.
 *
 * In the future, it would be great to generate this file automatically from the
 * Typescript types.
 */
import t from 'joi';
/**
 * Schema for UserDependencyConfig
 */
export declare const dependencyConfig: t.ObjectSchema<any>;
/**
 * Schema for ProjectConfig
 */
export declare const projectConfig: t.ObjectSchema<any>;
//# sourceMappingURL=schema.d.ts.map