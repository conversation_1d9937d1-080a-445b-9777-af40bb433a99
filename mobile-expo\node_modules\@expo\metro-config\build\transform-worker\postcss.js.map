{"version": 3, "file": "postcss.js", "names": ["_jsonFile", "data", "_interopRequireDefault", "require", "_fs", "_path", "_resolveFrom", "_require", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "_interopRequireWildcard", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "CONFIG_FILE_NAME", "debug", "transformPostCssModule", "projectRoot", "src", "filename", "inputConfig", "resolvePostcssConfig", "processWithPostcssInputConfigAsync", "plugins", "processOptions", "parsePostcssConfigAsync", "config", "resourcePath", "postcss", "Promise", "resolve", "then", "processor", "content", "process", "file", "inputPlugins", "map", "parser", "stringifier", "syntax", "factory", "pluginFactory", "item", "plugin", "options", "loadPlugin", "from", "path", "to", "_resolveFrom$silent", "tryRequireThenImport", "resolveFrom", "silent", "error", "Error", "message", "_resolveFrom$silent2", "_resolveFrom$silent3", "inline", "loadedPlugin", "keys", "length", "listOfPlugins", "Map", "Array", "isArray", "name", "undefined", "delete", "objectPlugins", "entries", "jsConfigPath", "join", "fs", "existsSync", "requireUncachedFile", "jsonConfigPath", "JsonFile", "read", "json5", "getPostcssConfigHash", "stableHash", "readFileSync", "toString"], "sources": ["../../src/transform-worker/postcss.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright JS Foundation and other contributors\n *\n * https://github.com/webpack-contrib/postcss-loader/\n */\nimport JsonFile from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\nimport type { AcceptedPlugin, ProcessOptions } from 'postcss';\nimport resolveFrom from 'resolve-from';\n\nimport { requireUncachedFile, tryRequireThenImport } from './utils/require';\n\ntype PostCSSInputConfig = {\n  plugins?: any[];\n  from?: string;\n  to?: string;\n  syntax?: string;\n  map?: boolean;\n  parser?: string;\n  stringifier?: string;\n};\n\nconst CONFIG_FILE_NAME = 'postcss.config';\n\nconst debug = require('debug')('expo:metro:transformer:postcss');\n\nexport async function transformPostCssModule(\n  projectRoot: string,\n  { src, filename }: { src: string; filename: string }\n): Promise<string> {\n  const inputConfig = resolvePostcssConfig(projectRoot);\n\n  if (!inputConfig) {\n    return src;\n  }\n\n  return await processWithPostcssInputConfigAsync(projectRoot, {\n    inputConfig,\n    src,\n    filename,\n  });\n}\n\nasync function processWithPostcssInputConfigAsync(\n  projectRoot: string,\n  { src, filename, inputConfig }: { src: string; filename: string; inputConfig: PostCSSInputConfig }\n) {\n  const { plugins, processOptions } = await parsePostcssConfigAsync(projectRoot, {\n    config: inputConfig,\n    resourcePath: filename,\n  });\n\n  debug('options:', processOptions);\n  debug('plugins:', plugins);\n\n  // TODO: Surely this can be cached...\n  const postcss = await import('postcss');\n  const processor = postcss.default(plugins);\n  const { content } = await processor.process(src, processOptions);\n\n  return content;\n}\n\nasync function parsePostcssConfigAsync(\n  projectRoot: string,\n  {\n    resourcePath: file,\n    config: { plugins: inputPlugins, map, parser, stringifier, syntax, ...config } = {},\n  }: {\n    resourcePath: string;\n    config: PostCSSInputConfig;\n  }\n): Promise<{ plugins: AcceptedPlugin[]; processOptions: ProcessOptions }> {\n  const factory = pluginFactory();\n\n  factory(inputPlugins);\n  // delete config.plugins;\n\n  const plugins = [...factory()].map((item) => {\n    const [plugin, options] = item;\n\n    if (typeof plugin === 'string') {\n      return loadPlugin(projectRoot, plugin, options, file);\n    }\n\n    return plugin;\n  });\n\n  if (config.from) {\n    config.from = path.resolve(projectRoot, config.from);\n  }\n\n  if (config.to) {\n    config.to = path.resolve(projectRoot, config.to);\n  }\n\n  const processOptions: Partial<ProcessOptions> = {\n    from: file,\n    to: file,\n    map: false,\n  };\n\n  if (typeof parser === 'string') {\n    try {\n      processOptions.parser = await tryRequireThenImport(\n        resolveFrom.silent(projectRoot, parser) ?? parser\n      );\n    } catch (error: unknown) {\n      if (error instanceof Error) {\n        throw new Error(\n          `Loading PostCSS \"${parser}\" parser failed: ${error.message}\\n\\n(@${file})`\n        );\n      }\n      throw error;\n    }\n  }\n\n  if (typeof stringifier === 'string') {\n    try {\n      processOptions.stringifier = await tryRequireThenImport(\n        resolveFrom.silent(projectRoot, stringifier) ?? stringifier\n      );\n    } catch (error: unknown) {\n      if (error instanceof Error) {\n        throw new Error(\n          `Loading PostCSS \"${stringifier}\" stringifier failed: ${error.message}\\n\\n(@${file})`\n        );\n      }\n      throw error;\n    }\n  }\n\n  if (typeof syntax === 'string') {\n    try {\n      processOptions.syntax = await tryRequireThenImport(\n        resolveFrom.silent(projectRoot, syntax) ?? syntax\n      );\n    } catch (error: any) {\n      throw new Error(`Loading PostCSS \"${syntax}\" syntax failed: ${error.message}\\n\\n(@${file})`);\n    }\n  }\n\n  if (map === true) {\n    // https://github.com/postcss/postcss/blob/master/docs/source-maps.md\n    processOptions.map = { inline: true };\n  }\n\n  return { plugins, processOptions };\n}\n\nfunction loadPlugin(projectRoot: string, plugin: string, options: unknown, file: string) {\n  try {\n    debug('load plugin:', plugin);\n\n    // e.g. `tailwindcss`\n    let loadedPlugin = require(resolveFrom(projectRoot, plugin));\n\n    if (loadedPlugin.default) {\n      loadedPlugin = loadedPlugin.default;\n    }\n\n    if (!options || !Object.keys(options).length) {\n      return loadedPlugin;\n    }\n\n    return loadedPlugin(options);\n  } catch (error: unknown) {\n    if (error instanceof Error) {\n      throw new Error(`Loading PostCSS \"${plugin}\" plugin failed: ${error.message}\\n\\n(@${file})`);\n    }\n    throw error;\n  }\n}\n\nexport function pluginFactory() {\n  const listOfPlugins = new Map<string, any>();\n\n  return (plugins?: any) => {\n    if (typeof plugins === 'undefined') {\n      return listOfPlugins;\n    }\n\n    if (Array.isArray(plugins)) {\n      for (const plugin of plugins) {\n        if (Array.isArray(plugin)) {\n          const [name, options] = plugin;\n\n          if (typeof name !== 'string') {\n            throw new Error(\n              `PostCSS plugin must be a string, but \"${name}\" was found. Please check your configuration.`\n            );\n          }\n\n          listOfPlugins.set(name, options);\n        } else if (plugin && typeof plugin === 'function') {\n          listOfPlugins.set(plugin, undefined);\n        } else if (\n          plugin &&\n          Object.keys(plugin).length === 1 &&\n          (typeof plugin[Object.keys(plugin)[0]] === 'object' ||\n            typeof plugin[Object.keys(plugin)[0]] === 'boolean') &&\n          plugin[Object.keys(plugin)[0]] !== null\n        ) {\n          const [name] = Object.keys(plugin);\n          const options = plugin[name];\n\n          if (options === false) {\n            listOfPlugins.delete(name);\n          } else {\n            listOfPlugins.set(name, options);\n          }\n        } else if (plugin) {\n          listOfPlugins.set(plugin, undefined);\n        }\n      }\n    } else {\n      const objectPlugins = Object.entries(plugins);\n\n      for (const [name, options] of objectPlugins) {\n        if (options === false) {\n          listOfPlugins.delete(name);\n        } else {\n          listOfPlugins.set(name, options);\n        }\n      }\n    }\n\n    return listOfPlugins;\n  };\n}\n\nexport function resolvePostcssConfig(projectRoot: string): PostCSSInputConfig | null {\n  // TODO: Maybe support platform-specific postcss config files in the future.\n  const jsConfigPath = path.join(projectRoot, CONFIG_FILE_NAME + '.js');\n\n  if (fs.existsSync(jsConfigPath)) {\n    debug('load file:', jsConfigPath);\n    return requireUncachedFile(jsConfigPath);\n  }\n\n  const jsonConfigPath = path.join(projectRoot, CONFIG_FILE_NAME + '.json');\n\n  if (fs.existsSync(jsonConfigPath)) {\n    debug('load file:', jsonConfigPath);\n    return JsonFile.read(jsonConfigPath, { json5: true });\n  }\n\n  return null;\n}\n\nexport function getPostcssConfigHash(projectRoot: string): string | null {\n  // TODO: Maybe recurse plugins and add versions to the hash in the future.\n  const { stableHash } = require('metro-cache');\n\n  const jsConfigPath = path.join(projectRoot, CONFIG_FILE_NAME + '.js');\n  if (fs.existsSync(jsConfigPath)) {\n    return stableHash(fs.readFileSync(jsConfigPath, 'utf8')).toString('hex');\n  }\n\n  const jsonConfigPath = path.join(projectRoot, CONFIG_FILE_NAME + '.json');\n  if (fs.existsSync(jsonConfigPath)) {\n    return stableHash(fs.readFileSync(jsonConfigPath, 'utf8')).toString('hex');\n  }\n  return null;\n}\n"], "mappings": ";;;;;;;;;AAMA,SAAAA,UAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,SAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,aAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,YAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,SAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4E,SAAAC,uBAAAM,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAAI,wBAAAR,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAS,KAAA,GAAAN,wBAAA,CAAAC,WAAA,OAAAK,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAV,GAAA,YAAAS,KAAA,CAAAE,GAAA,CAAAX,GAAA,SAAAY,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAjB,GAAA,QAAAiB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAApB,GAAA,EAAAiB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAhB,GAAA,EAAAiB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAjB,GAAA,CAAAiB,GAAA,SAAAL,MAAA,CAAAV,OAAA,GAAAF,GAAA,MAAAS,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAAtB,GAAA,EAAAY,MAAA,YAAAA,MAAA;AAY5E,MAAMW,gBAAgB,GAAG,gBAAgB;AAEzC,MAAMC,KAAK,GAAG7B,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC;AAEzD,eAAe8B,sBAAsBA,CAC1CC,WAAmB,EACnB;EAAEC,GAAG;EAAEC;AAA4C,CAAC,EACnC;EACjB,MAAMC,WAAW,GAAGC,oBAAoB,CAACJ,WAAW,CAAC;EAErD,IAAI,CAACG,WAAW,EAAE;IAChB,OAAOF,GAAG;EACZ;EAEA,OAAO,MAAMI,kCAAkC,CAACL,WAAW,EAAE;IAC3DG,WAAW;IACXF,GAAG;IACHC;EACF,CAAC,CAAC;AACJ;AAEA,eAAeG,kCAAkCA,CAC/CL,WAAmB,EACnB;EAAEC,GAAG;EAAEC,QAAQ;EAAEC;AAAgF,CAAC,EAClG;EACA,MAAM;IAAEG,OAAO;IAAEC;EAAe,CAAC,GAAG,MAAMC,uBAAuB,CAACR,WAAW,EAAE;IAC7ES,MAAM,EAAEN,WAAW;IACnBO,YAAY,EAAER;EAChB,CAAC,CAAC;EAEFJ,KAAK,CAAC,UAAU,EAAES,cAAc,CAAC;EACjCT,KAAK,CAAC,UAAU,EAAEQ,OAAO,CAAC;;EAE1B;EACA,MAAMK,OAAO,GAAG,MAAAC,OAAA,CAAAC,OAAA,GAAAC,IAAA,OAAAhC,uBAAA,CAAAb,OAAA,CAAa,SAAS,GAAC;EACvC,MAAM8C,SAAS,GAAGJ,OAAO,CAACnC,OAAO,CAAC8B,OAAO,CAAC;EAC1C,MAAM;IAAEU;EAAQ,CAAC,GAAG,MAAMD,SAAS,CAACE,OAAO,CAAChB,GAAG,EAAEM,cAAc,CAAC;EAEhE,OAAOS,OAAO;AAChB;AAEA,eAAeR,uBAAuBA,CACpCR,WAAmB,EACnB;EACEU,YAAY,EAAEQ,IAAI;EAClBT,MAAM,EAAE;IAAEH,OAAO,EAAEa,YAAY;IAAEC,GAAG;IAAEC,MAAM;IAAEC,WAAW;IAAEC,MAAM;IAAE,GAAGd;EAAO,CAAC,GAAG,CAAC;AAIpF,CAAC,EACuE;EACxE,MAAMe,OAAO,GAAGC,aAAa,EAAE;EAE/BD,OAAO,CAACL,YAAY,CAAC;EACrB;;EAEA,MAAMb,OAAO,GAAG,CAAC,GAAGkB,OAAO,EAAE,CAAC,CAACJ,GAAG,CAAEM,IAAI,IAAK;IAC3C,MAAM,CAACC,MAAM,EAAEC,OAAO,CAAC,GAAGF,IAAI;IAE9B,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAOE,UAAU,CAAC7B,WAAW,EAAE2B,MAAM,EAAEC,OAAO,EAAEV,IAAI,CAAC;IACvD;IAEA,OAAOS,MAAM;EACf,CAAC,CAAC;EAEF,IAAIlB,MAAM,CAACqB,IAAI,EAAE;IACfrB,MAAM,CAACqB,IAAI,GAAGC,eAAI,CAAClB,OAAO,CAACb,WAAW,EAAES,MAAM,CAACqB,IAAI,CAAC;EACtD;EAEA,IAAIrB,MAAM,CAACuB,EAAE,EAAE;IACbvB,MAAM,CAACuB,EAAE,GAAGD,eAAI,CAAClB,OAAO,CAACb,WAAW,EAAES,MAAM,CAACuB,EAAE,CAAC;EAClD;EAEA,MAAMzB,cAAuC,GAAG;IAC9CuB,IAAI,EAAEZ,IAAI;IACVc,EAAE,EAAEd,IAAI;IACRE,GAAG,EAAE;EACP,CAAC;EAED,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAI;MAAA,IAAAY,mBAAA;MACF1B,cAAc,CAACc,MAAM,GAAG,MAAM,IAAAa,+BAAoB,GAAAD,mBAAA,GAChDE,sBAAW,CAACC,MAAM,CAACpC,WAAW,EAAEqB,MAAM,CAAC,cAAAY,mBAAA,cAAAA,mBAAA,GAAIZ,MAAM,CAClD;IACH,CAAC,CAAC,OAAOgB,KAAc,EAAE;MACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;QAC1B,MAAM,IAAIA,KAAK,CACZ,oBAAmBjB,MAAO,oBAAmBgB,KAAK,CAACE,OAAQ,SAAQrB,IAAK,GAAE,CAC5E;MACH;MACA,MAAMmB,KAAK;IACb;EACF;EAEA,IAAI,OAAOf,WAAW,KAAK,QAAQ,EAAE;IACnC,IAAI;MAAA,IAAAkB,oBAAA;MACFjC,cAAc,CAACe,WAAW,GAAG,MAAM,IAAAY,+BAAoB,GAAAM,oBAAA,GACrDL,sBAAW,CAACC,MAAM,CAACpC,WAAW,EAAEsB,WAAW,CAAC,cAAAkB,oBAAA,cAAAA,oBAAA,GAAIlB,WAAW,CAC5D;IACH,CAAC,CAAC,OAAOe,KAAc,EAAE;MACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;QAC1B,MAAM,IAAIA,KAAK,CACZ,oBAAmBhB,WAAY,yBAAwBe,KAAK,CAACE,OAAQ,SAAQrB,IAAK,GAAE,CACtF;MACH;MACA,MAAMmB,KAAK;IACb;EACF;EAEA,IAAI,OAAOd,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAI;MAAA,IAAAkB,oBAAA;MACFlC,cAAc,CAACgB,MAAM,GAAG,MAAM,IAAAW,+BAAoB,GAAAO,oBAAA,GAChDN,sBAAW,CAACC,MAAM,CAACpC,WAAW,EAAEuB,MAAM,CAAC,cAAAkB,oBAAA,cAAAA,oBAAA,GAAIlB,MAAM,CAClD;IACH,CAAC,CAAC,OAAOc,KAAU,EAAE;MACnB,MAAM,IAAIC,KAAK,CAAE,oBAAmBf,MAAO,oBAAmBc,KAAK,CAACE,OAAQ,SAAQrB,IAAK,GAAE,CAAC;IAC9F;EACF;EAEA,IAAIE,GAAG,KAAK,IAAI,EAAE;IAChB;IACAb,cAAc,CAACa,GAAG,GAAG;MAAEsB,MAAM,EAAE;IAAK,CAAC;EACvC;EAEA,OAAO;IAAEpC,OAAO;IAAEC;EAAe,CAAC;AACpC;AAEA,SAASsB,UAAUA,CAAC7B,WAAmB,EAAE2B,MAAc,EAAEC,OAAgB,EAAEV,IAAY,EAAE;EACvF,IAAI;IACFpB,KAAK,CAAC,cAAc,EAAE6B,MAAM,CAAC;;IAE7B;IACA,IAAIgB,YAAY,GAAG1E,OAAO,CAAC,IAAAkE,sBAAW,EAACnC,WAAW,EAAE2B,MAAM,CAAC,CAAC;IAE5D,IAAIgB,YAAY,CAACnE,OAAO,EAAE;MACxBmE,YAAY,GAAGA,YAAY,CAACnE,OAAO;IACrC;IAEA,IAAI,CAACoD,OAAO,IAAI,CAACxC,MAAM,CAACwD,IAAI,CAAChB,OAAO,CAAC,CAACiB,MAAM,EAAE;MAC5C,OAAOF,YAAY;IACrB;IAEA,OAAOA,YAAY,CAACf,OAAO,CAAC;EAC9B,CAAC,CAAC,OAAOS,KAAc,EAAE;IACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;MAC1B,MAAM,IAAIA,KAAK,CAAE,oBAAmBX,MAAO,oBAAmBU,KAAK,CAACE,OAAQ,SAAQrB,IAAK,GAAE,CAAC;IAC9F;IACA,MAAMmB,KAAK;EACb;AACF;AAEO,SAASZ,aAAaA,CAAA,EAAG;EAC9B,MAAMqB,aAAa,GAAG,IAAIC,GAAG,EAAe;EAE5C,OAAQzC,OAAa,IAAK;IACxB,IAAI,OAAOA,OAAO,KAAK,WAAW,EAAE;MAClC,OAAOwC,aAAa;IACtB;IAEA,IAAIE,KAAK,CAACC,OAAO,CAAC3C,OAAO,CAAC,EAAE;MAC1B,KAAK,MAAMqB,MAAM,IAAIrB,OAAO,EAAE;QAC5B,IAAI0C,KAAK,CAACC,OAAO,CAACtB,MAAM,CAAC,EAAE;UACzB,MAAM,CAACuB,IAAI,EAAEtB,OAAO,CAAC,GAAGD,MAAM;UAE9B,IAAI,OAAOuB,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIZ,KAAK,CACZ,yCAAwCY,IAAK,+CAA8C,CAC7F;UACH;UAEAJ,aAAa,CAAClD,GAAG,CAACsD,IAAI,EAAEtB,OAAO,CAAC;QAClC,CAAC,MAAM,IAAID,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;UACjDmB,aAAa,CAAClD,GAAG,CAAC+B,MAAM,EAAEwB,SAAS,CAAC;QACtC,CAAC,MAAM,IACLxB,MAAM,IACNvC,MAAM,CAACwD,IAAI,CAACjB,MAAM,CAAC,CAACkB,MAAM,KAAK,CAAC,KAC/B,OAAOlB,MAAM,CAACvC,MAAM,CAACwD,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,IACjD,OAAOA,MAAM,CAACvC,MAAM,CAACwD,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,IACtDA,MAAM,CAACvC,MAAM,CAACwD,IAAI,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EACvC;UACA,MAAM,CAACuB,IAAI,CAAC,GAAG9D,MAAM,CAACwD,IAAI,CAACjB,MAAM,CAAC;UAClC,MAAMC,OAAO,GAAGD,MAAM,CAACuB,IAAI,CAAC;UAE5B,IAAItB,OAAO,KAAK,KAAK,EAAE;YACrBkB,aAAa,CAACM,MAAM,CAACF,IAAI,CAAC;UAC5B,CAAC,MAAM;YACLJ,aAAa,CAAClD,GAAG,CAACsD,IAAI,EAAEtB,OAAO,CAAC;UAClC;QACF,CAAC,MAAM,IAAID,MAAM,EAAE;UACjBmB,aAAa,CAAClD,GAAG,CAAC+B,MAAM,EAAEwB,SAAS,CAAC;QACtC;MACF;IACF,CAAC,MAAM;MACL,MAAME,aAAa,GAAGjE,MAAM,CAACkE,OAAO,CAAChD,OAAO,CAAC;MAE7C,KAAK,MAAM,CAAC4C,IAAI,EAAEtB,OAAO,CAAC,IAAIyB,aAAa,EAAE;QAC3C,IAAIzB,OAAO,KAAK,KAAK,EAAE;UACrBkB,aAAa,CAACM,MAAM,CAACF,IAAI,CAAC;QAC5B,CAAC,MAAM;UACLJ,aAAa,CAAClD,GAAG,CAACsD,IAAI,EAAEtB,OAAO,CAAC;QAClC;MACF;IACF;IAEA,OAAOkB,aAAa;EACtB,CAAC;AACH;AAEO,SAAS1C,oBAAoBA,CAACJ,WAAmB,EAA6B;EACnF;EACA,MAAMuD,YAAY,GAAGxB,eAAI,CAACyB,IAAI,CAACxD,WAAW,EAAEH,gBAAgB,GAAG,KAAK,CAAC;EAErE,IAAI4D,aAAE,CAACC,UAAU,CAACH,YAAY,CAAC,EAAE;IAC/BzD,KAAK,CAAC,YAAY,EAAEyD,YAAY,CAAC;IACjC,OAAO,IAAAI,8BAAmB,EAACJ,YAAY,CAAC;EAC1C;EAEA,MAAMK,cAAc,GAAG7B,eAAI,CAACyB,IAAI,CAACxD,WAAW,EAAEH,gBAAgB,GAAG,OAAO,CAAC;EAEzE,IAAI4D,aAAE,CAACC,UAAU,CAACE,cAAc,CAAC,EAAE;IACjC9D,KAAK,CAAC,YAAY,EAAE8D,cAAc,CAAC;IACnC,OAAOC,mBAAQ,CAACC,IAAI,CAACF,cAAc,EAAE;MAAEG,KAAK,EAAE;IAAK,CAAC,CAAC;EACvD;EAEA,OAAO,IAAI;AACb;AAEO,SAASC,oBAAoBA,CAAChE,WAAmB,EAAiB;EACvE;EACA,MAAM;IAAEiE;EAAW,CAAC,GAAGhG,OAAO,CAAC,aAAa,CAAC;EAE7C,MAAMsF,YAAY,GAAGxB,eAAI,CAACyB,IAAI,CAACxD,WAAW,EAAEH,gBAAgB,GAAG,KAAK,CAAC;EACrE,IAAI4D,aAAE,CAACC,UAAU,CAACH,YAAY,CAAC,EAAE;IAC/B,OAAOU,UAAU,CAACR,aAAE,CAACS,YAAY,CAACX,YAAY,EAAE,MAAM,CAAC,CAAC,CAACY,QAAQ,CAAC,KAAK,CAAC;EAC1E;EAEA,MAAMP,cAAc,GAAG7B,eAAI,CAACyB,IAAI,CAACxD,WAAW,EAAEH,gBAAgB,GAAG,OAAO,CAAC;EACzE,IAAI4D,aAAE,CAACC,UAAU,CAACE,cAAc,CAAC,EAAE;IACjC,OAAOK,UAAU,CAACR,aAAE,CAACS,YAAY,CAACN,cAAc,EAAE,MAAM,CAAC,CAAC,CAACO,QAAQ,CAAC,KAAK,CAAC;EAC5E;EACA,OAAO,IAAI;AACb"}