{"version": 3, "file": "PlatformUtils.web.js", "sourceRoot": "", "sources": ["../src/PlatformUtils.web.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC;AACpC,MAAM,CAAC,MAAM,2BAA2B,GAAG,KAAK,CAAC;AACjD,MAAM,CAAC,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEpD,yCAAyC;AACzC,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,CAAC;AAEpC,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IACvD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,UAAU,YAAY;IAC1B,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["export const IS_MANAGED_ENV = false;\nexport const IS_ENV_WITH_UPDATES_ENABLED = false;\nexport const IS_ENV_WITHOUT_UPDATES_ENABLED = false;\n\n// Compute manifest base URL if available\nexport const manifestBaseUrl = null;\n\nexport async function downloadAsync(uri, hash, type, name): Promise<string> {\n  return uri;\n}\n\nexport function getManifest() {\n  return {};\n}\n\nexport function getManifest2() {\n  return {};\n}\n"]}