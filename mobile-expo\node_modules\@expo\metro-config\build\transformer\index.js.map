{"version": 3, "file": "index.js", "names": ["_createExoticTransformer", "data", "require", "_get<PERSON><PERSON><PERSON><PERSON>", "_createMultiRuleTransformer", "_createMatcher"], "sources": ["../../src/transformer/index.ts"], "sourcesContent": ["export { createExoticTransformer } from './createExoticTransformer';\nexport { getCacheKey } from './getCacheKey';\nexport { loaders, createMultiRuleTransformer } from './createMultiRuleTransformer';\nexport { createModuleMatcher } from './createMatcher';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,yBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,wBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,aAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,4BAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,2BAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,eAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,cAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA"}