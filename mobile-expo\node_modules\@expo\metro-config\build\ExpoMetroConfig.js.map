{"version": 3, "file": "ExpoMetroConfig.js", "names": ["_config", "data", "require", "_paths", "runtimeEnv", "_interopRequireWildcard", "_jsonFile", "_interopRequireDefault", "_chalk", "_metroCache", "_metroConfig", "_path", "_resolveFrom", "_customizeFrame", "_env2", "_getModulesPaths", "_getWatchFolders", "_rewriteRequestUrl", "_withExpoSerializers", "_postcss", "_metroConfig2", "obj", "__esModule", "default", "_getRequireWildcardCache", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "cache", "has", "get", "newObj", "hasPropertyDescriptor", "Object", "defineProperty", "getOwnPropertyDescriptor", "key", "prototype", "hasOwnProperty", "call", "desc", "set", "debug", "getProjectBabelConfigFile", "projectRoot", "resolveFrom", "silent", "getAssetPlugins", "hashAssetFilesPath", "Error", "hasWarnedAboutExotic", "getDefaultConfig", "options", "getDefaultMetroConfig", "mergeConfig", "importMetroConfig", "isExotic", "mode", "env", "EXPO_USE_EXOTIC", "console", "log", "chalk", "gray", "bold", "reactNativePath", "path", "dirname", "babelPresetFbjsPath", "process", "EXPO_METRO_CACHE_KEY_VERSION", "String", "version", "sourceExtsConfig", "isTS", "isReact", "isModern", "sourceExts", "getBareExtensions", "push", "sassVersion", "isCSSEnabled", "getSassVersion", "envFiles", "getFiles", "NODE_ENV", "babelConfigPath", "isCustomBabelConfigDefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pkg", "getPackageJson", "watchFolders", "getWatchFolders", "nodeModulesPaths", "getModulesPaths", "EXPO_DEBUG", "join", "reporter", "metroDefaultValues", "getDefaultValues", "metroConfig", "resolver", "platforms", "assetExts", "concat", "filter", "assetExt", "includes", "watcher", "additionalExts", "map", "file", "replace", "serializer", "getModulesRunBeforeMainModule", "preModules", "resolve", "metroRuntime", "getPolyfills", "server", "rewriteRequestUrl", "getRewriteRequestUrl", "port", "Number", "RCT_METRO_PORT", "unstable_serverRoot", "getServerRoot", "symbolicator", "customizeFrame", "getDefaultCustomizeFrame", "transformerPath", "transformer", "postcssHash", "getPostcssConfigHash", "browserslistHash", "browserslist", "stableHash", "JSON", "stringify", "toString", "unstable_allowRequireContext", "allowOptionalDependencies", "babelTransformerPath", "assetRegistryPath", "assetPlugins", "withExpoSerializers", "loadAsync", "metroOptions", "defaultConfig", "loadConfig", "cwd", "exports", "sassPkg", "sassPkgJson", "findUpPackageJson", "JsonFile", "read", "sep", "found"], "sources": ["../src/ExpoMetroConfig.ts"], "sourcesContent": ["// Copyright 2023-present 650 Industries (Expo). All rights reserved.\nimport { getPackageJson } from '@expo/config';\nimport { getBareExtensions } from '@expo/config/paths';\nimport * as runtimeEnv from '@expo/env';\nimport JsonFile from '@expo/json-file';\nimport chalk from 'chalk';\nimport { Reporter } from 'metro';\nimport { stableHash } from 'metro-cache';\nimport { ConfigT as MetroConfig, InputConfigT } from 'metro-config';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { getDefaultCustomizeFrame, INTERNAL_CALLSITES_REGEX } from './customizeFrame';\nimport { env } from './env';\nimport { getModulesPaths, getServerRoot } from './getModulesPaths';\nimport { getWatchFolders } from './getWatchFolders';\nimport { getRewriteRequestUrl } from './rewriteRequestUrl';\nimport { withExpoSerializers } from './serializer/withExpoSerializers';\nimport { getPostcssConfigHash } from './transform-worker/postcss';\nimport { importMetroConfig } from './traveling/metro-config';\n\nconst debug = require('debug')('expo:metro:config') as typeof console.log;\n\nexport interface LoadOptions {\n  config?: string;\n  maxWorkers?: number;\n  port?: number;\n  reporter?: Reporter;\n  resetCache?: boolean;\n}\n\nexport interface DefaultConfigOptions {\n  mode?: 'exotic';\n  /**\n   * **Experimental:** Enable CSS support for Metro web, and shim on native.\n   *\n   * This is an experimental feature and may change in the future. The underlying implementation\n   * is subject to change, and native support for CSS Modules may be added in the future during a non-major SDK release.\n   */\n  isCSSEnabled?: boolean;\n}\n\nfunction getProjectBabelConfigFile(projectRoot: string): string | undefined {\n  return (\n    resolveFrom.silent(projectRoot, './babel.config.js') ||\n    resolveFrom.silent(projectRoot, './.babelrc') ||\n    resolveFrom.silent(projectRoot, './.babelrc.js')\n  );\n}\n\nfunction getAssetPlugins(projectRoot: string): string[] {\n  const hashAssetFilesPath = resolveFrom.silent(projectRoot, 'expo-asset/tools/hashAssetFiles');\n\n  if (!hashAssetFilesPath) {\n    throw new Error(`The required package \\`expo-asset\\` cannot be found`);\n  }\n\n  return [hashAssetFilesPath];\n}\n\nlet hasWarnedAboutExotic = false;\n\nexport function getDefaultConfig(\n  projectRoot: string,\n  options: DefaultConfigOptions = {}\n): InputConfigT {\n  const { getDefaultConfig: getDefaultMetroConfig, mergeConfig } = importMetroConfig(projectRoot);\n\n  const isExotic = options.mode === 'exotic' || env.EXPO_USE_EXOTIC;\n\n  if (isExotic && !hasWarnedAboutExotic) {\n    hasWarnedAboutExotic = true;\n    console.log(\n      chalk.gray(\n        `\\u203A Unstable feature ${chalk.bold`EXPO_USE_EXOTIC`} is enabled. Bundling may not work as expected, and is subject to breaking changes.`\n      )\n    );\n  }\n\n  const reactNativePath = path.dirname(resolveFrom(projectRoot, 'react-native/package.json'));\n\n  try {\n    // Set the `EXPO_METRO_CACHE_KEY_VERSION` variable for use in the custom babel transformer.\n    // This hack is used because there doesn't appear to be anyway to resolve\n    // `babel-preset-fbjs` relative to the project root later (in `metro-expo-babel-transformer`).\n    const babelPresetFbjsPath = resolveFrom(projectRoot, 'babel-preset-fbjs/package.json');\n    process.env.EXPO_METRO_CACHE_KEY_VERSION = String(require(babelPresetFbjsPath).version);\n  } catch {\n    // noop -- falls back to a hardcoded value.\n  }\n\n  const sourceExtsConfig = { isTS: true, isReact: true, isModern: false };\n  const sourceExts = getBareExtensions([], sourceExtsConfig);\n\n  // Add support for cjs (without platform extensions).\n  sourceExts.push('cjs');\n\n  let sassVersion: string | null = null;\n  if (options.isCSSEnabled) {\n    sassVersion = getSassVersion(projectRoot);\n    // Enable SCSS by default so we can provide a better error message\n    // when sass isn't installed.\n    sourceExts.push('scss', 'sass', 'css');\n  }\n\n  const envFiles = runtimeEnv.getFiles(process.env.NODE_ENV, { silent: true });\n\n  const babelConfigPath = getProjectBabelConfigFile(projectRoot);\n  const isCustomBabelConfigDefined = !!babelConfigPath;\n\n  const resolverMainFields: string[] = [];\n\n  // Disable `react-native` in exotic mode, since library authors\n  // use it to ship raw application code to the project.\n  if (!isExotic) {\n    resolverMainFields.push('react-native');\n  }\n  resolverMainFields.push('browser', 'main');\n\n  const pkg = getPackageJson(projectRoot);\n  const watchFolders = getWatchFolders(projectRoot);\n  // TODO: nodeModulesPaths does not work with the new Node.js package.json exports API, this causes packages like uuid to fail. Disabling for now.\n  const nodeModulesPaths = getModulesPaths(projectRoot);\n  if (env.EXPO_DEBUG) {\n    console.log();\n    console.log(`Expo Metro config:`);\n    try {\n      console.log(`- Version: ${require('../package.json').version}`);\n    } catch {}\n    console.log(`- Extensions: ${sourceExts.join(', ')}`);\n    console.log(`- React Native: ${reactNativePath}`);\n    console.log(`- Babel config: ${babelConfigPath || 'babel-preset-expo (default)'}`);\n    console.log(`- Resolver Fields: ${resolverMainFields.join(', ')}`);\n    console.log(`- Watch Folders: ${watchFolders.join(', ')}`);\n    console.log(`- Node Module Paths: ${nodeModulesPaths.join(', ')}`);\n    console.log(`- Exotic: ${isExotic}`);\n    console.log(`- Env Files: ${envFiles}`);\n    console.log(`- Sass: ${sassVersion}`);\n    console.log();\n  }\n  const {\n    // Remove the default reporter which metro always resolves to be the react-native-community/cli reporter.\n    // This prints a giant React logo which is less accessible to users on smaller terminals.\n    reporter,\n    ...metroDefaultValues\n  } = getDefaultMetroConfig.getDefaultValues(projectRoot);\n\n  // Merge in the default config from Metro here, even though loadConfig uses it as defaults.\n  // This is a convenience for getDefaultConfig use in metro.config.js, e.g. to modify assetExts.\n  const metroConfig: Partial<MetroConfig> = mergeConfig(metroDefaultValues, {\n    watchFolders,\n    resolver: {\n      resolverMainFields,\n      platforms: ['ios', 'android'],\n      assetExts: metroDefaultValues.resolver.assetExts\n        .concat(\n          // Add default support for `expo-image` file types.\n          ['heic', 'avif']\n        )\n        .filter((assetExt) => !sourceExts.includes(assetExt)),\n      sourceExts,\n      nodeModulesPaths,\n    },\n    watcher: {\n      // strip starting dot from env files\n      additionalExts: envFiles.map((file: string) => file.replace(/^\\./, '')),\n    },\n    serializer: {\n      getModulesRunBeforeMainModule: () => {\n        const preModules: string[] = [\n          // MUST be first\n          require.resolve(path.join(reactNativePath, 'Libraries/Core/InitializeCore')),\n        ];\n\n        // We need to shift this to be the first module so web Fast Refresh works as expected.\n        // This will only be applied if the module is installed and imported somewhere in the bundle already.\n        const metroRuntime = resolveFrom.silent(projectRoot, '@expo/metro-runtime');\n        if (metroRuntime) {\n          preModules.push(metroRuntime);\n        }\n\n        return preModules;\n      },\n      getPolyfills: () => require(path.join(reactNativePath, 'rn-get-polyfills'))(),\n    },\n    server: {\n      rewriteRequestUrl: getRewriteRequestUrl(projectRoot),\n      port: Number(env.RCT_METRO_PORT) || 8081,\n      // NOTE(EvanBacon): Moves the server root down to the monorepo root.\n      // This enables proper monorepo support for web.\n      unstable_serverRoot: getServerRoot(projectRoot),\n    },\n    symbolicator: {\n      customizeFrame: getDefaultCustomizeFrame(),\n    },\n    transformerPath: options.isCSSEnabled\n      ? // Custom worker that adds CSS support for Metro web.\n        require.resolve('./transform-worker/transform-worker')\n      : metroDefaultValues.transformerPath,\n\n    transformer: {\n      // Custom: These are passed to `getCacheKey` and ensure invalidation when the version changes.\n      // @ts-expect-error: not on type.\n      postcssHash: getPostcssConfigHash(projectRoot),\n      browserslistHash: pkg.browserslist\n        ? stableHash(JSON.stringify(pkg.browserslist)).toString('hex')\n        : null,\n      sassVersion,\n\n      // `require.context` support\n      unstable_allowRequireContext: true,\n      allowOptionalDependencies: true,\n      babelTransformerPath: isExotic\n        ? require.resolve('./transformer/metro-expo-exotic-babel-transformer')\n        : isCustomBabelConfigDefined\n        ? // If the user defined a babel config file in their project,\n          // then use the default transformer.\n          // Try to use the project copy before falling back on the global version\n          resolveFrom.silent(projectRoot, 'metro-react-native-babel-transformer')\n        : // Otherwise, use a custom transformer that uses `babel-preset-expo` by default for projects.\n          require.resolve('./transformer/metro-expo-babel-transformer'),\n      assetRegistryPath: 'react-native/Libraries/Image/AssetRegistry',\n      assetPlugins: getAssetPlugins(projectRoot),\n    },\n  });\n\n  return withExpoSerializers(metroConfig);\n}\n\nexport async function loadAsync(\n  projectRoot: string,\n  { reporter, ...metroOptions }: LoadOptions = {}\n): Promise<MetroConfig> {\n  let defaultConfig = getDefaultConfig(projectRoot);\n  if (reporter) {\n    defaultConfig = { ...defaultConfig, reporter };\n  }\n\n  const { loadConfig } = importMetroConfig(projectRoot);\n\n  return await loadConfig({ cwd: projectRoot, projectRoot, ...metroOptions }, defaultConfig);\n}\n\n// re-export for use in config files.\nexport { MetroConfig, INTERNAL_CALLSITES_REGEX };\n\n// re-export for legacy cases.\nexport const EXPO_DEBUG = env.EXPO_DEBUG;\n\nfunction getSassVersion(projectRoot: string): string | null {\n  const sassPkg = resolveFrom.silent(projectRoot, 'sass');\n  if (!sassPkg) return null;\n  const sassPkgJson = findUpPackageJson(sassPkg);\n  if (!sassPkgJson) return null;\n  const pkg = JsonFile.read(sassPkgJson);\n\n  debug('sass package.json:', sassPkgJson);\n  const sassVersion = pkg.version;\n  if (typeof sassVersion === 'string') {\n    return sassVersion;\n  }\n\n  return null;\n}\n\nfunction findUpPackageJson(cwd: string): string | null {\n  if (['.', path.sep].includes(cwd)) return null;\n\n  const found = resolveFrom.silent(cwd, './package.json');\n  if (found) {\n    return found;\n  }\n  return findUpPackageJson(path.dirname(cwd));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,UAAA;EAAA,MAAAL,IAAA,GAAAM,sBAAA,CAAAL,OAAA;EAAAI,SAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,OAAA;EAAA,MAAAP,IAAA,GAAAM,sBAAA,CAAAL,OAAA;EAAAM,MAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAQ,YAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,WAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,aAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,YAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,MAAA;EAAA,MAAAV,IAAA,GAAAM,sBAAA,CAAAL,OAAA;EAAAS,KAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,aAAA;EAAA,MAAAX,IAAA,GAAAM,sBAAA,CAAAL,OAAA;EAAAU,YAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAY,gBAAA;EAAA,MAAAZ,IAAA,GAAAC,OAAA;EAAAW,eAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAa,MAAA;EAAA,MAAAb,IAAA,GAAAC,OAAA;EAAAY,KAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAc,iBAAA;EAAA,MAAAd,IAAA,GAAAC,OAAA;EAAAa,gBAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAe,iBAAA;EAAA,MAAAf,IAAA,GAAAC,OAAA;EAAAc,gBAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAgB,mBAAA;EAAA,MAAAhB,IAAA,GAAAC,OAAA;EAAAe,kBAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAiB,qBAAA;EAAA,MAAAjB,IAAA,GAAAC,OAAA;EAAAgB,oBAAA,YAAAA,CAAA;IAAA,OAAAjB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAkB,SAAA;EAAA,MAAAlB,IAAA,GAAAC,OAAA;EAAAiB,QAAA,YAAAA,CAAA;IAAA,OAAAlB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAmB,cAAA;EAAA,MAAAnB,IAAA,GAAAC,OAAA;EAAAkB,aAAA,YAAAA,CAAA;IAAA,OAAAnB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6D,SAAAM,uBAAAc,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,WAAA,eAAAC,OAAA,kCAAAC,iBAAA,OAAAD,OAAA,QAAAE,gBAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,WAAA,WAAAA,WAAA,GAAAG,gBAAA,GAAAD,iBAAA,KAAAF,WAAA;AAAA,SAAApB,wBAAAgB,GAAA,EAAAI,WAAA,SAAAA,WAAA,IAAAJ,GAAA,IAAAA,GAAA,CAAAC,UAAA,WAAAD,GAAA,QAAAA,GAAA,oBAAAA,GAAA,wBAAAA,GAAA,4BAAAE,OAAA,EAAAF,GAAA,UAAAQ,KAAA,GAAAL,wBAAA,CAAAC,WAAA,OAAAI,KAAA,IAAAA,KAAA,CAAAC,GAAA,CAAAT,GAAA,YAAAQ,KAAA,CAAAE,GAAA,CAAAV,GAAA,SAAAW,MAAA,WAAAC,qBAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,GAAA,IAAAhB,GAAA,QAAAgB,GAAA,kBAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAnB,GAAA,EAAAgB,GAAA,SAAAI,IAAA,GAAAR,qBAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAf,GAAA,EAAAgB,GAAA,cAAAI,IAAA,KAAAA,IAAA,CAAAV,GAAA,IAAAU,IAAA,CAAAC,GAAA,KAAAR,MAAA,CAAAC,cAAA,CAAAH,MAAA,EAAAK,GAAA,EAAAI,IAAA,YAAAT,MAAA,CAAAK,GAAA,IAAAhB,GAAA,CAAAgB,GAAA,SAAAL,MAAA,CAAAT,OAAA,GAAAF,GAAA,MAAAQ,KAAA,IAAAA,KAAA,CAAAa,GAAA,CAAArB,GAAA,EAAAW,MAAA,YAAAA,MAAA;AAnB7D;;AAqBA,MAAMW,KAAK,GAAGzC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAuB;AAqBzE,SAAS0C,yBAAyBA,CAACC,WAAmB,EAAsB;EAC1E,OACEC,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,mBAAmB,CAAC,IACpDC,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,YAAY,CAAC,IAC7CC,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,eAAe,CAAC;AAEpD;AAEA,SAASG,eAAeA,CAACH,WAAmB,EAAY;EACtD,MAAMI,kBAAkB,GAAGH,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,iCAAiC,CAAC;EAE7F,IAAI,CAACI,kBAAkB,EAAE;IACvB,MAAM,IAAIC,KAAK,CAAE,qDAAoD,CAAC;EACxE;EAEA,OAAO,CAACD,kBAAkB,CAAC;AAC7B;AAEA,IAAIE,oBAAoB,GAAG,KAAK;AAEzB,SAASC,gBAAgBA,CAC9BP,WAAmB,EACnBQ,OAA6B,GAAG,CAAC,CAAC,EACpB;EACd,MAAM;IAAED,gBAAgB,EAAEE,qBAAqB;IAAEC;EAAY,CAAC,GAAG,IAAAC,iCAAiB,EAACX,WAAW,CAAC;EAE/F,MAAMY,QAAQ,GAAGJ,OAAO,CAACK,IAAI,KAAK,QAAQ,IAAIC,WAAG,CAACC,eAAe;EAEjE,IAAIH,QAAQ,IAAI,CAACN,oBAAoB,EAAE;IACrCA,oBAAoB,GAAG,IAAI;IAC3BU,OAAO,CAACC,GAAG,CACTC,gBAAK,CAACC,IAAI,CACP,2BAA0BD,gBAAK,CAACE,IAAK,iBAAiB,qFAAoF,CAC5I,CACF;EACH;EAEA,MAAMC,eAAe,GAAGC,eAAI,CAACC,OAAO,CAAC,IAAAtB,sBAAW,EAACD,WAAW,EAAE,2BAA2B,CAAC,CAAC;EAE3F,IAAI;IACF;IACA;IACA;IACA,MAAMwB,mBAAmB,GAAG,IAAAvB,sBAAW,EAACD,WAAW,EAAE,gCAAgC,CAAC;IACtFyB,OAAO,CAACX,GAAG,CAACY,4BAA4B,GAAGC,MAAM,CAACtE,OAAO,CAACmE,mBAAmB,CAAC,CAACI,OAAO,CAAC;EACzF,CAAC,CAAC,MAAM;IACN;EAAA;EAGF,MAAMC,gBAAgB,GAAG;IAAEC,IAAI,EAAE,IAAI;IAAEC,OAAO,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAM,CAAC;EACvE,MAAMC,UAAU,GAAG,IAAAC,0BAAiB,EAAC,EAAE,EAAEL,gBAAgB,CAAC;;EAE1D;EACAI,UAAU,CAACE,IAAI,CAAC,KAAK,CAAC;EAEtB,IAAIC,WAA0B,GAAG,IAAI;EACrC,IAAI5B,OAAO,CAAC6B,YAAY,EAAE;IACxBD,WAAW,GAAGE,cAAc,CAACtC,WAAW,CAAC;IACzC;IACA;IACAiC,UAAU,CAACE,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EACxC;EAEA,MAAMI,QAAQ,GAAGhF,UAAU,GAACiF,QAAQ,CAACf,OAAO,CAACX,GAAG,CAAC2B,QAAQ,EAAE;IAAEvC,MAAM,EAAE;EAAK,CAAC,CAAC;EAE5E,MAAMwC,eAAe,GAAG3C,yBAAyB,CAACC,WAAW,CAAC;EAC9D,MAAM2C,0BAA0B,GAAG,CAAC,CAACD,eAAe;EAEpD,MAAME,kBAA4B,GAAG,EAAE;;EAEvC;EACA;EACA,IAAI,CAAChC,QAAQ,EAAE;IACbgC,kBAAkB,CAACT,IAAI,CAAC,cAAc,CAAC;EACzC;EACAS,kBAAkB,CAACT,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC;EAE1C,MAAMU,GAAG,GAAG,IAAAC,wBAAc,EAAC9C,WAAW,CAAC;EACvC,MAAM+C,YAAY,GAAG,IAAAC,kCAAe,EAAChD,WAAW,CAAC;EACjD;EACA,MAAMiD,gBAAgB,GAAG,IAAAC,kCAAe,EAAClD,WAAW,CAAC;EACrD,IAAIc,WAAG,CAACqC,UAAU,EAAE;IAClBnC,OAAO,CAACC,GAAG,EAAE;IACbD,OAAO,CAACC,GAAG,CAAE,oBAAmB,CAAC;IACjC,IAAI;MACFD,OAAO,CAACC,GAAG,CAAE,cAAa5D,OAAO,CAAC,iBAAiB,CAAC,CAACuE,OAAQ,EAAC,CAAC;IACjE,CAAC,CAAC,MAAM,CAAC;IACTZ,OAAO,CAACC,GAAG,CAAE,iBAAgBgB,UAAU,CAACmB,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IACrDpC,OAAO,CAACC,GAAG,CAAE,mBAAkBI,eAAgB,EAAC,CAAC;IACjDL,OAAO,CAACC,GAAG,CAAE,mBAAkByB,eAAe,IAAI,6BAA8B,EAAC,CAAC;IAClF1B,OAAO,CAACC,GAAG,CAAE,sBAAqB2B,kBAAkB,CAACQ,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IAClEpC,OAAO,CAACC,GAAG,CAAE,oBAAmB8B,YAAY,CAACK,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IAC1DpC,OAAO,CAACC,GAAG,CAAE,wBAAuBgC,gBAAgB,CAACG,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;IAClEpC,OAAO,CAACC,GAAG,CAAE,aAAYL,QAAS,EAAC,CAAC;IACpCI,OAAO,CAACC,GAAG,CAAE,gBAAesB,QAAS,EAAC,CAAC;IACvCvB,OAAO,CAACC,GAAG,CAAE,WAAUmB,WAAY,EAAC,CAAC;IACrCpB,OAAO,CAACC,GAAG,EAAE;EACf;EACA,MAAM;IACJ;IACA;IACAoC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAG7C,qBAAqB,CAAC8C,gBAAgB,CAACvD,WAAW,CAAC;;EAEvD;EACA;EACA,MAAMwD,WAAiC,GAAG9C,WAAW,CAAC4C,kBAAkB,EAAE;IACxEP,YAAY;IACZU,QAAQ,EAAE;MACRb,kBAAkB;MAClBc,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;MAC7BC,SAAS,EAAEL,kBAAkB,CAACG,QAAQ,CAACE,SAAS,CAC7CC,MAAM;MACL;MACA,CAAC,MAAM,EAAE,MAAM,CAAC,CACjB,CACAC,MAAM,CAAEC,QAAQ,IAAK,CAAC7B,UAAU,CAAC8B,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACvD7B,UAAU;MACVgB;IACF,CAAC;IACDe,OAAO,EAAE;MACP;MACAC,cAAc,EAAE1B,QAAQ,CAAC2B,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IACxE,CAAC;IACDC,UAAU,EAAE;MACVC,6BAA6B,EAAEA,CAAA,KAAM;QACnC,MAAMC,UAAoB,GAAG;QAC3B;QACAlH,OAAO,CAACmH,OAAO,CAAClD,eAAI,CAAC8B,IAAI,CAAC/B,eAAe,EAAE,+BAA+B,CAAC,CAAC,CAC7E;;QAED;QACA;QACA,MAAMoD,YAAY,GAAGxE,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,qBAAqB,CAAC;QAC3E,IAAIyE,YAAY,EAAE;UAChBF,UAAU,CAACpC,IAAI,CAACsC,YAAY,CAAC;QAC/B;QAEA,OAAOF,UAAU;MACnB,CAAC;MACDG,YAAY,EAAEA,CAAA,KAAMrH,OAAO,CAACiE,eAAI,CAAC8B,IAAI,CAAC/B,eAAe,EAAE,kBAAkB,CAAC,CAAC;IAC7E,CAAC;IACDsD,MAAM,EAAE;MACNC,iBAAiB,EAAE,IAAAC,yCAAoB,EAAC7E,WAAW,CAAC;MACpD8E,IAAI,EAAEC,MAAM,CAACjE,WAAG,CAACkE,cAAc,CAAC,IAAI,IAAI;MACxC;MACA;MACAC,mBAAmB,EAAE,IAAAC,gCAAa,EAAClF,WAAW;IAChD,CAAC;IACDmF,YAAY,EAAE;MACZC,cAAc,EAAE,IAAAC,0CAAwB;IAC1C,CAAC;IACDC,eAAe,EAAE9E,OAAO,CAAC6B,YAAY;IACjC;IACAhF,OAAO,CAACmH,OAAO,CAAC,qCAAqC,CAAC,GACtDlB,kBAAkB,CAACgC,eAAe;IAEtCC,WAAW,EAAE;MACX;MACA;MACAC,WAAW,EAAE,IAAAC,+BAAoB,EAACzF,WAAW,CAAC;MAC9C0F,gBAAgB,EAAE7C,GAAG,CAAC8C,YAAY,GAC9B,IAAAC,wBAAU,EAACC,IAAI,CAACC,SAAS,CAACjD,GAAG,CAAC8C,YAAY,CAAC,CAAC,CAACI,QAAQ,CAAC,KAAK,CAAC,GAC5D,IAAI;MACR3D,WAAW;MAEX;MACA4D,4BAA4B,EAAE,IAAI;MAClCC,yBAAyB,EAAE,IAAI;MAC/BC,oBAAoB,EAAEtF,QAAQ,GAC1BvD,OAAO,CAACmH,OAAO,CAAC,mDAAmD,CAAC,GACpE7B,0BAA0B;MAC1B;MACA;MACA;MACA1C,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,sCAAsC,CAAC;MACvE;MACA3C,OAAO,CAACmH,OAAO,CAAC,4CAA4C,CAAC;MACjE2B,iBAAiB,EAAE,4CAA4C;MAC/DC,YAAY,EAAEjG,eAAe,CAACH,WAAW;IAC3C;EACF,CAAC,CAAC;EAEF,OAAO,IAAAqG,0CAAmB,EAAC7C,WAAW,CAAC;AACzC;AAEO,eAAe8C,SAASA,CAC7BtG,WAAmB,EACnB;EAAEqD,QAAQ;EAAE,GAAGkD;AAA0B,CAAC,GAAG,CAAC,CAAC,EACzB;EACtB,IAAIC,aAAa,GAAGjG,gBAAgB,CAACP,WAAW,CAAC;EACjD,IAAIqD,QAAQ,EAAE;IACZmD,aAAa,GAAG;MAAE,GAAGA,aAAa;MAAEnD;IAAS,CAAC;EAChD;EAEA,MAAM;IAAEoD;EAAW,CAAC,GAAG,IAAA9F,iCAAiB,EAACX,WAAW,CAAC;EAErD,OAAO,MAAMyG,UAAU,CAAC;IAAEC,GAAG,EAAE1G,WAAW;IAAEA,WAAW;IAAE,GAAGuG;EAAa,CAAC,EAAEC,aAAa,CAAC;AAC5F;;AAEA;;AAGA;AACO,MAAMrD,UAAU,GAAGrC,WAAG,CAACqC,UAAU;AAACwD,OAAA,CAAAxD,UAAA,GAAAA,UAAA;AAEzC,SAASb,cAAcA,CAACtC,WAAmB,EAAiB;EAC1D,MAAM4G,OAAO,GAAG3G,sBAAW,CAACC,MAAM,CAACF,WAAW,EAAE,MAAM,CAAC;EACvD,IAAI,CAAC4G,OAAO,EAAE,OAAO,IAAI;EACzB,MAAMC,WAAW,GAAGC,iBAAiB,CAACF,OAAO,CAAC;EAC9C,IAAI,CAACC,WAAW,EAAE,OAAO,IAAI;EAC7B,MAAMhE,GAAG,GAAGkE,mBAAQ,CAACC,IAAI,CAACH,WAAW,CAAC;EAEtC/G,KAAK,CAAC,oBAAoB,EAAE+G,WAAW,CAAC;EACxC,MAAMzE,WAAW,GAAGS,GAAG,CAACjB,OAAO;EAC/B,IAAI,OAAOQ,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOA,WAAW;EACpB;EAEA,OAAO,IAAI;AACb;AAEA,SAAS0E,iBAAiBA,CAACJ,GAAW,EAAiB;EACrD,IAAI,CAAC,GAAG,EAAEpF,eAAI,CAAC2F,GAAG,CAAC,CAAClD,QAAQ,CAAC2C,GAAG,CAAC,EAAE,OAAO,IAAI;EAE9C,MAAMQ,KAAK,GAAGjH,sBAAW,CAACC,MAAM,CAACwG,GAAG,EAAE,gBAAgB,CAAC;EACvD,IAAIQ,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EACA,OAAOJ,iBAAiB,CAACxF,eAAI,CAACC,OAAO,CAACmF,GAAG,CAAC,CAAC;AAC7C"}