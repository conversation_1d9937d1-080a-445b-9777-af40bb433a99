{"version": 3, "file": "getModulesPaths.js", "names": ["_findYarn<PERSON>paceRoot", "data", "_interopRequireDefault", "require", "_path", "_env", "obj", "__esModule", "default", "getWorkspaceRoot", "projectRoot", "findWorkspaceRoot", "error", "message", "includes", "getModulesPaths", "paths", "workspaceRoot", "path", "resolve", "push", "getServerRoot", "_getWorkspaceRoot", "env", "EXPO_USE_METRO_WORKSPACE_ROOT"], "sources": ["../src/getModulesPaths.ts"], "sourcesContent": ["import findWorkspaceRoot from 'find-yarn-workspace-root';\nimport path from 'path';\n\nimport { env } from './env';\n\n/** Wraps `findWorkspaceRoot` and guards against having an empty `package.json` file in an upper directory. */\nexport function getWorkspaceRoot(projectRoot: string): string | null {\n  try {\n    return findWorkspaceRoot(projectRoot);\n  } catch (error: any) {\n    if (error.message.includes('Unexpected end of JSON input')) {\n      return null;\n    }\n    throw error;\n  }\n}\n\nexport function getModulesPaths(projectRoot: string): string[] {\n  const paths: string[] = [];\n\n  // Only add the project root if it's not the current working directory\n  // this minimizes the chance of Metro resolver breaking on new Node.js versions.\n  const workspaceRoot = getWorkspaceRoot(path.resolve(projectRoot)); // Absolute path or null\n  if (workspaceRoot) {\n    paths.push(path.resolve(projectRoot));\n    paths.push(path.resolve(workspaceRoot, 'node_modules'));\n  }\n\n  return paths;\n}\n\nexport function getServerRoot(projectRoot: string) {\n  return env.EXPO_USE_METRO_WORKSPACE_ROOT\n    ? getWorkspaceRoot(projectRoot) ?? projectRoot\n    : projectRoot;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,uBAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,sBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE5B;AACO,SAASG,gBAAgBA,CAACC,WAAmB,EAAiB;EACnE,IAAI;IACF,OAAO,IAAAC,gCAAiB,EAACD,WAAW,CAAC;EACvC,CAAC,CAAC,OAAOE,KAAU,EAAE;IACnB,IAAIA,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,MAAMF,KAAK;EACb;AACF;AAEO,SAASG,eAAeA,CAACL,WAAmB,EAAY;EAC7D,MAAMM,KAAe,GAAG,EAAE;;EAE1B;EACA;EACA,MAAMC,aAAa,GAAGR,gBAAgB,CAACS,eAAI,CAACC,OAAO,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC;EACnE,IAAIO,aAAa,EAAE;IACjBD,KAAK,CAACI,IAAI,CAACF,eAAI,CAACC,OAAO,CAACT,WAAW,CAAC,CAAC;IACrCM,KAAK,CAACI,IAAI,CAACF,eAAI,CAACC,OAAO,CAACF,aAAa,EAAE,cAAc,CAAC,CAAC;EACzD;EAEA,OAAOD,KAAK;AACd;AAEO,SAASK,aAAaA,CAACX,WAAmB,EAAE;EAAA,IAAAY,iBAAA;EACjD,OAAOC,UAAG,CAACC,6BAA6B,IAAAF,iBAAA,GACpCb,gBAAgB,CAACC,WAAW,CAAC,cAAAY,iBAAA,cAAAA,iBAAA,GAAIZ,WAAW,GAC5CA,WAAW;AACjB"}