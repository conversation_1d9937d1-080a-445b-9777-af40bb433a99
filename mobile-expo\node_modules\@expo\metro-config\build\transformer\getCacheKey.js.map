{"version": 3, "file": "getCacheKey.js", "names": ["_crypto", "data", "_interopRequireDefault", "require", "_fs", "obj", "__esModule", "default", "cacheKeyParts", "readFileSync", "__filename", "process", "env", "EXPO_METRO_CACHE_KEY_VERSION", "exports", "get<PERSON><PERSON><PERSON><PERSON>", "key", "crypto", "createHash", "for<PERSON>ach", "part", "update", "digest"], "sources": ["../../src/transformer/getCacheKey.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport { readFileSync } from 'fs';\n\nexport const cacheKeyParts = [\n  readFileSync(__filename),\n  // Since babel-preset-fbjs cannot be safely resolved relative to the\n  // project root, use this environment variable that we define earlier.\n  process.env.EXPO_METRO_CACHE_KEY_VERSION || '3.3.0',\n  //   require('babel-preset-fbjs/package.json').version,\n];\n\n// Matches upstream\nexport function getCacheKey(): string {\n  const key = crypto.createHash('md5');\n  cacheKeyParts.forEach((part) => key.update(part));\n  return key.digest('hex');\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAkC,SAAAC,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAE3B,MAAMG,aAAa,GAAG,CAC3B,IAAAC,kBAAY,EAACC,UAAU,CAAC;AACxB;AACA;AACAC,OAAO,CAACC,GAAG,CAACC,4BAA4B,IAAI;AAC5C;AAAA,CACD;;AAED;AAAAC,OAAA,CAAAN,aAAA,GAAAA,aAAA;AACO,SAASO,WAAWA,CAAA,EAAW;EACpC,MAAMC,GAAG,GAAGC,iBAAM,CAACC,UAAU,CAAC,KAAK,CAAC;EACpCV,aAAa,CAACW,OAAO,CAAEC,IAAI,IAAKJ,GAAG,CAACK,MAAM,CAACD,IAAI,CAAC,CAAC;EACjD,OAAOJ,GAAG,CAACM,MAAM,CAAC,KAAK,CAAC;AAC1B"}