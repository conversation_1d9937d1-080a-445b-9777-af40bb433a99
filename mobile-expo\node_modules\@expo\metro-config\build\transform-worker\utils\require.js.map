{"version": 3, "file": "require.js", "names": ["tryRequireThenImport", "moduleId", "require", "requireError", "importESM", "Function", "code", "default", "requireUncachedFile", "error", "Error", "message"], "sources": ["../../../src/transform-worker/utils/require.ts"], "sourcesContent": ["export async function tryRequireThenImport<TModule>(moduleId: string): Promise<TModule> {\n  try {\n    return require(moduleId);\n  } catch (requireError: any) {\n    let importESM;\n    try {\n      // eslint-disable-next-line no-new-func\n      importESM = new Function('id', 'return import(id);');\n    } catch {\n      importESM = null;\n    }\n\n    if (requireError?.code === 'ERR_REQUIRE_ESM' && importESM) {\n      return (await importESM(moduleId)).default;\n    }\n\n    throw requireError;\n  }\n}\n\nexport function requireUncachedFile(moduleId: string) {\n  try {\n    // delete require.cache[require.resolve(moduleId)];\n  } catch {}\n  try {\n    return require(moduleId);\n  } catch (error: unknown) {\n    if (error instanceof Error) {\n      error.message = `Cannot load file ${moduleId}: ${error.message}`;\n    }\n    throw error;\n  }\n}\n"], "mappings": ";;;;;;;AAAO,eAAeA,oBAAoBA,CAAUC,QAAgB,EAAoB;EACtF,IAAI;IACF,OAAOC,OAAO,CAACD,QAAQ,CAAC;EAC1B,CAAC,CAAC,OAAOE,YAAiB,EAAE;IAC1B,IAAIC,SAAS;IACb,IAAI;MACF;MACAA,SAAS,GAAG,IAAIC,QAAQ,CAAC,IAAI,EAAE,oBAAoB,CAAC;IACtD,CAAC,CAAC,MAAM;MACND,SAAS,GAAG,IAAI;IAClB;IAEA,IAAI,CAAAD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,MAAK,iBAAiB,IAAIF,SAAS,EAAE;MACzD,OAAO,CAAC,MAAMA,SAAS,CAACH,QAAQ,CAAC,EAAEM,OAAO;IAC5C;IAEA,MAAMJ,YAAY;EACpB;AACF;AAEO,SAASK,mBAAmBA,CAACP,QAAgB,EAAE;EACpD,IAAI;IACF;EAAA,CACD,CAAC,MAAM,CAAC;EACT,IAAI;IACF,OAAOC,OAAO,CAACD,QAAQ,CAAC;EAC1B,CAAC,CAAC,OAAOQ,KAAc,EAAE;IACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;MAC1BD,KAAK,CAACE,OAAO,GAAI,oBAAmBV,QAAS,KAAIQ,KAAK,CAACE,OAAQ,EAAC;IAClE;IACA,MAAMF,KAAK;EACb;AACF"}