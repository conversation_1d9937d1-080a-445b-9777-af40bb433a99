{"version": 3, "file": "metro-config.js", "names": ["_resolveFrom", "data", "_interopRequireDefault", "require", "obj", "__esModule", "default", "importMetroConfig", "projectRoot", "modulePath", "resolveFrom", "silent"], "sources": ["../../src/traveling/metro-config.ts"], "sourcesContent": ["// This package needs to be imported from within the project to\n\nimport resolveFrom from 'resolve-from';\n\n// ensure that Metro can bundle the project's assets (see: `watchFolders`).\nexport function importMetroConfig(projectRoot: string): typeof import('metro-config') & {\n  getDefaultConfig: import('metro-config/src/defaults/index').default;\n} {\n  const modulePath = resolveFrom.silent(projectRoot, 'metro-config');\n\n  if (!modulePath) {\n    return require('metro-config');\n  }\n  return require(modulePath);\n}\n"], "mappings": ";;;;;;AAEA,SAAAA,aAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,YAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAC,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAFvC;;AAIA;AACO,SAASG,iBAAiBA,CAACC,WAAmB,EAEnD;EACA,MAAMC,UAAU,GAAGC,sBAAW,CAACC,MAAM,CAACH,WAAW,EAAE,cAAc,CAAC;EAElE,IAAI,CAACC,UAAU,EAAE;IACf,OAAON,OAAO,CAAC,cAAc,CAAC;EAChC;EACA,OAAOA,OAAO,CAACM,UAAU,CAAC;AAC5B"}