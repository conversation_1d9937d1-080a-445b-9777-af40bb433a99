{"version": 3, "file": "getBabelConfig.js", "names": ["_fs", "data", "_interopRequireDefault", "require", "_path", "_resolveFrom", "obj", "__esModule", "default", "getBabelRC", "babelRC", "_getBabelRC", "projectRoot", "options", "plugins", "projectBabelRCPath", "path", "resolve", "fs", "existsSync", "extends", "_ref", "_resolveFrom$silent", "experimentalImportSupport", "presetOptions", "presetPath", "resolveFrom", "silent", "presets", "jsxRuntime", "disableImportExportTransform", "enableBabelRuntime", "getBabelConfig", "filename", "extraConfig", "babelrc", "enableBabelRCLookup", "code", "highlightCode", "config", "extraPlugins", "inlineRequires", "inlineRequiresPlugin", "push", "concat", "dev", "hot", "mayContainEditableReactComponents", "indexOf"], "sources": ["../../src/transformer/getBabelConfig.ts"], "sourcesContent": ["/**\n * Copyright (c) Expo.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Forks the default metro-react-native-babel-transformer and adds support for known transforms.\n */\n\nimport type { PluginItem as BabelPlugins, PluginItem } from '@babel/core';\nimport fs from 'fs';\nimport type { BabelTransformerOptions } from 'metro-babel-transformer';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\n/**\n * Return a memoized function that checks for the existence of a\n * project level .babelrc file, and if it doesn't exist, reads the\n * default RN babelrc file and uses that.\n */\nconst getBabelRC = (function () {\n  let babelRC: {\n    // `any` to avoid flow type mismatch with Babel 7's internal type of\n    // `Array<string>` even though it correctly accepts the usage below.\n    presets?: any;\n    extends?: string;\n    plugins: BabelPlugins;\n  } | null = null;\n\n  return function _getBabelRC(projectRoot: string, options: BabelTransformerOptions) {\n    if (babelRC != null) {\n      return babelRC;\n    }\n\n    babelRC = { plugins: [] };\n\n    // Let's look for a babel config file in the project root.\n    // TODO look into adding a command line option to specify this location\n    let projectBabelRCPath;\n\n    // .babelrc\n    if (projectRoot) {\n      projectBabelRCPath = path.resolve(projectRoot, '.babelrc');\n    }\n\n    if (projectBabelRCPath) {\n      // .babelrc.js\n      if (!fs.existsSync(projectBabelRCPath)) {\n        projectBabelRCPath = path.resolve(projectRoot, '.babelrc.js');\n      }\n\n      // babel.config.js\n      if (!fs.existsSync(projectBabelRCPath)) {\n        projectBabelRCPath = path.resolve(projectRoot, 'babel.config.js');\n      }\n\n      // If we found a babel config file, extend our config off of it\n      // otherwise the default config will be used\n      if (fs.existsSync(projectBabelRCPath)) {\n        babelRC.extends = projectBabelRCPath;\n      }\n    }\n\n    // If a babel config file doesn't exist in the project then\n    // the default preset for react-native will be used instead.\n    if (!babelRC.extends) {\n      const { experimentalImportSupport, ...presetOptions } = options;\n\n      // Use `babel-preset-expo` instead of `metro-react-native-babel-preset`.\n      const presetPath =\n        resolveFrom.silent(projectRoot, 'babel-preset-expo') ??\n        resolveFrom.silent(projectRoot, 'metro-react-native-babel-preset') ??\n        require.resolve('babel-preset-expo');\n\n      babelRC.presets = [\n        [\n          require(presetPath),\n          {\n            // Default to React 17 automatic JSX transform.\n            jsxRuntime: 'automatic',\n            ...presetOptions,\n            disableImportExportTransform: experimentalImportSupport,\n            enableBabelRuntime: options.enableBabelRuntime,\n          },\n        ],\n      ];\n    }\n\n    return babelRC;\n  };\n})();\n\n/**\n * Given a filename and options, build a Babel\n * config object with the appropriate plugins.\n */\nexport function getBabelConfig(\n  filename: string,\n  options: BabelTransformerOptions,\n  plugins: BabelPlugins = []\n) {\n  const babelRC = getBabelRC(options.projectRoot, options);\n\n  const extraConfig = {\n    babelrc: typeof options.enableBabelRCLookup === 'boolean' ? options.enableBabelRCLookup : true,\n    code: false,\n    filename,\n    highlightCode: true,\n  };\n\n  const config: any = { ...babelRC, ...extraConfig };\n\n  // Add extra plugins\n  const extraPlugins: (string | PluginItem)[] = [];\n\n  // TODO: This probably can be removed\n  if (options.inlineRequires) {\n    const inlineRequiresPlugin = resolveFrom(\n      options.projectRoot,\n      'babel-preset-fbjs/plugins/inline-requires'\n    );\n    extraPlugins.push(inlineRequiresPlugin);\n  }\n\n  config.plugins = extraPlugins.concat(config.plugins, plugins);\n\n  if (options.dev && options.hot) {\n    // Note: this intentionally doesn't include the path separator because\n    // I'm not sure which one it should use on Windows, and false positives\n    // are unlikely anyway. If you later decide to include the separator,\n    // don't forget that the string usually *starts* with \"node_modules\" so\n    // the first one often won't be there.\n    // TODO: Support monorepos\n    const mayContainEditableReactComponents = filename.indexOf('node_modules') === -1;\n\n    if (mayContainEditableReactComponents) {\n      if (!config.plugins) {\n        config.plugins = [];\n      }\n      // Add react refresh runtime.\n      // NOTICE: keep in sync with 'metro-react-native-babel-preset/src/configs/hmr'.\n      config.plugins.push(resolveFrom.silent(options.projectRoot, 'react-refresh/babel'));\n    }\n  }\n\n  return { ...babelRC, ...config };\n}\n"], "mappings": ";;;;;;AAWA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,aAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,YAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAC,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAdvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA,MAAMG,UAAU,GAAI,YAAY;EAC9B,IAAIC,OAMI,GAAG,IAAI;EAEf,OAAO,SAASC,WAAWA,CAACC,WAAmB,EAAEC,OAAgC,EAAE;IACjF,IAAIH,OAAO,IAAI,IAAI,EAAE;MACnB,OAAOA,OAAO;IAChB;IAEAA,OAAO,GAAG;MAAEI,OAAO,EAAE;IAAG,CAAC;;IAEzB;IACA;IACA,IAAIC,kBAAkB;;IAEtB;IACA,IAAIH,WAAW,EAAE;MACfG,kBAAkB,GAAGC,eAAI,CAACC,OAAO,CAACL,WAAW,EAAE,UAAU,CAAC;IAC5D;IAEA,IAAIG,kBAAkB,EAAE;MACtB;MACA,IAAI,CAACG,aAAE,CAACC,UAAU,CAACJ,kBAAkB,CAAC,EAAE;QACtCA,kBAAkB,GAAGC,eAAI,CAACC,OAAO,CAACL,WAAW,EAAE,aAAa,CAAC;MAC/D;;MAEA;MACA,IAAI,CAACM,aAAE,CAACC,UAAU,CAACJ,kBAAkB,CAAC,EAAE;QACtCA,kBAAkB,GAAGC,eAAI,CAACC,OAAO,CAACL,WAAW,EAAE,iBAAiB,CAAC;MACnE;;MAEA;MACA;MACA,IAAIM,aAAE,CAACC,UAAU,CAACJ,kBAAkB,CAAC,EAAE;QACrCL,OAAO,CAACU,OAAO,GAAGL,kBAAkB;MACtC;IACF;;IAEA;IACA;IACA,IAAI,CAACL,OAAO,CAACU,OAAO,EAAE;MAAA,IAAAC,IAAA,EAAAC,mBAAA;MACpB,MAAM;QAAEC,yBAAyB;QAAE,GAAGC;MAAc,CAAC,GAAGX,OAAO;;MAE/D;MACA,MAAMY,UAAU,IAAAJ,IAAA,IAAAC,mBAAA,GACdI,sBAAW,CAACC,MAAM,CAACf,WAAW,EAAE,mBAAmB,CAAC,cAAAU,mBAAA,cAAAA,mBAAA,GACpDI,sBAAW,CAACC,MAAM,CAACf,WAAW,EAAE,iCAAiC,CAAC,cAAAS,IAAA,cAAAA,IAAA,GAClElB,OAAO,CAACc,OAAO,CAAC,mBAAmB,CAAC;MAEtCP,OAAO,CAACkB,OAAO,GAAG,CAChB,CACEzB,OAAO,CAACsB,UAAU,CAAC,EACnB;QACE;QACAI,UAAU,EAAE,WAAW;QACvB,GAAGL,aAAa;QAChBM,4BAA4B,EAAEP,yBAAyB;QACvDQ,kBAAkB,EAAElB,OAAO,CAACkB;MAC9B,CAAC,CACF,CACF;IACH;IAEA,OAAOrB,OAAO;EAChB,CAAC;AACH,CAAC,EAAG;;AAEJ;AACA;AACA;AACA;AACO,SAASsB,cAAcA,CAC5BC,QAAgB,EAChBpB,OAAgC,EAChCC,OAAqB,GAAG,EAAE,EAC1B;EACA,MAAMJ,OAAO,GAAGD,UAAU,CAACI,OAAO,CAACD,WAAW,EAAEC,OAAO,CAAC;EAExD,MAAMqB,WAAW,GAAG;IAClBC,OAAO,EAAE,OAAOtB,OAAO,CAACuB,mBAAmB,KAAK,SAAS,GAAGvB,OAAO,CAACuB,mBAAmB,GAAG,IAAI;IAC9FC,IAAI,EAAE,KAAK;IACXJ,QAAQ;IACRK,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,MAAW,GAAG;IAAE,GAAG7B,OAAO;IAAE,GAAGwB;EAAY,CAAC;;EAElD;EACA,MAAMM,YAAqC,GAAG,EAAE;;EAEhD;EACA,IAAI3B,OAAO,CAAC4B,cAAc,EAAE;IAC1B,MAAMC,oBAAoB,GAAG,IAAAhB,sBAAW,EACtCb,OAAO,CAACD,WAAW,EACnB,2CAA2C,CAC5C;IACD4B,YAAY,CAACG,IAAI,CAACD,oBAAoB,CAAC;EACzC;EAEAH,MAAM,CAACzB,OAAO,GAAG0B,YAAY,CAACI,MAAM,CAACL,MAAM,CAACzB,OAAO,EAAEA,OAAO,CAAC;EAE7D,IAAID,OAAO,CAACgC,GAAG,IAAIhC,OAAO,CAACiC,GAAG,EAAE;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,iCAAiC,GAAGd,QAAQ,CAACe,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAEjF,IAAID,iCAAiC,EAAE;MACrC,IAAI,CAACR,MAAM,CAACzB,OAAO,EAAE;QACnByB,MAAM,CAACzB,OAAO,GAAG,EAAE;MACrB;MACA;MACA;MACAyB,MAAM,CAACzB,OAAO,CAAC6B,IAAI,CAACjB,sBAAW,CAACC,MAAM,CAACd,OAAO,CAACD,WAAW,EAAE,qBAAqB,CAAC,CAAC;IACrF;EACF;EAEA,OAAO;IAAE,GAAGF,OAAO;IAAE,GAAG6B;EAAO,CAAC;AAClC"}