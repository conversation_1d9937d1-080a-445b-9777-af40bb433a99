{"version": 3, "file": "PlatformUtils.js", "sourceRoot": "", "sources": ["../src/PlatformUtils.ts"], "names": [], "mappings": "AAAA,OAAO,UAAU,MAAM,aAAa,CAAC;AACrC,OAAO,SAAS,MAAM,gBAAgB,CAAC;AAEvC,OAAO,KAAK,UAAU,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD,wFAAwF;AACxF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC;AAEvD,yFAAyF;AACzF,sFAAsF;AACtF,0BAA0B;AAC1B,MAAM,CAAC,MAAM,wBAAwB,GACnC,CAAC,cAAc;IACf,CAAC,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS;IAC3C,kGAAkG;IAClG,sCAAsC;IACtC,CAAC,kBAAkB,CAAC,WAAW,EAAE,qBAAqB,CAAC;AAEzD,MAAM,CAAC,MAAM,2BAA2B,GAAG,cAAc,IAAI,wBAAwB,CAAC;AAEtF,4EAA4E;AAC5E,MAAM,CAAC,MAAM,2BAA2B,GAAG,CAAC,cAAc,IAAI,CAAC,wBAAwB,CAAC;AAExF,gFAAgF;AAChF,iEAAiE;AACjE,MAAM,UAAU,cAAc;IAC5B,OAAO,kBAAkB,CAAC,WAAW,EAAE,WAAW,IAAI,EAAE,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,OAAO,SAAS,CAAC,sBAAsB,IAAI,EAAE,CAAC;AAChD,CAAC;AAED,MAAM,UAAU,YAAY;IAC1B,OAAO,SAAS,CAAC,uBAAuB,CAAC;AAC3C,CAAC;AAED,yCAAyC;AACzC,MAAM,CAAC,MAAM,eAAe,GAAG,SAAS,CAAC,aAAa;IACpD,CAAC,CAAC,kBAAkB,CAAC,SAAS,CAAC,aAAa,CAAC;IAC7C,CAAC,CAAC,IAAI,CAAC;AAET,4EAA4E;AAC5E,MAAM,CAAC,KAAK,UAAU,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IACvD,IAAI,cAAc,EAAE;QAClB,OAAO,wBAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACxD;IAED,OAAO,0BAA0B,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACrD,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,wBAAwB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAC3D,MAAM,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,QAAQ,GAAG,GAAG,UAAU,CAAC,cAAc,iBAAiB,WAAW,IAAI,IAAI,EAAE,CAAC;IACpF,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE;QACvD,GAAG,EAAE,IAAI;KACV,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE;QAChE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,UAAU,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE;YAC5D,GAAG,EAAE,IAAI;SACV,CAAC,CAAC;QACH,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,8BAA8B,IAAI,IAAI,IAAI,IAAI;gBAC5C,cAAc,GAAG,GAAG;gBACpB,4BAA4B,CAC/B,CAAC;SACH;KACF;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,0BAA0B,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI;IACvD,uEAAuE;IACvE,0CAA0C;IAC1C,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;QAC7B,OAAO,GAAG,CAAC;KACZ;IAED,MAAM,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,QAAQ,GAAG,GAAG,UAAU,CAAC,cAAc,iBAAiB,WAAW,IAAI,IAAI,EAAE,CAAC;IAEpF,4EAA4E;IAC5E,yCAAyC;IACzC,MAAM,UAAU,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC9C,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import computeMd5 from 'blueimp-md5';\nimport Constants from 'expo-constants';\nimport { Manifest } from 'expo-constants/build/Constants.types';\nimport * as FileSystem from 'expo-file-system';\nimport { NativeModulesProxy } from 'expo-modules-core';\n\nimport { getManifestBaseUrl } from './AssetUris';\n\n// Constants.appOwnership is only available in managed apps (Expo client and standalone)\nexport const IS_MANAGED_ENV = !!Constants.appOwnership;\n\n// In the future (SDK38+) expo-updates is likely to be used in managed apps, so we decide\n// that you are in a bare app with updates if you're not in a managed app and you have\n// local assets available.\nexport const IS_BARE_ENV_WITH_UPDATES =\n  !IS_MANAGED_ENV &&\n  !!NativeModulesProxy.ExpoUpdates?.isEnabled &&\n  // if expo-updates is installed but we're running directly from the embedded bundle, we don't want\n  // to override the AssetSourceResolver\n  !NativeModulesProxy.ExpoUpdates?.isUsingEmbeddedAssets;\n\nexport const IS_ENV_WITH_UPDATES_ENABLED = IS_MANAGED_ENV || IS_BARE_ENV_WITH_UPDATES;\n\n// If it's not managed or bare w/ updates, then it must be bare w/o updates!\nexport const IS_BARE_ENV_WITHOUT_UPDATES = !IS_MANAGED_ENV && !IS_BARE_ENV_WITH_UPDATES;\n\n// Get the localAssets property from the ExpoUpdates native module so that we do\n// not need to include expo-updates as a dependency of expo-asset\nexport function getLocalAssets() {\n  return NativeModulesProxy.ExpoUpdates?.localAssets ?? {};\n}\n\nexport function getManifest(): { [key: string]: any } {\n  return Constants.__unsafeNoWarnManifest ?? {};\n}\n\nexport function getManifest2(): Manifest | undefined {\n  return Constants.__unsafeNoWarnManifest2;\n}\n\n// Compute manifest base URL if available\nexport const manifestBaseUrl = Constants.experienceUrl\n  ? getManifestBaseUrl(Constants.experienceUrl)\n  : null;\n\n// TODO: how should this behave in bare app with updates? re: hashAssetFiles\nexport async function downloadAsync(uri, hash, type, name): Promise<string> {\n  if (IS_MANAGED_ENV) {\n    return _downloadAsyncManagedEnv(uri, hash, type, name);\n  }\n\n  return _downloadAsyncUnmanagedEnv(uri, hash, type);\n}\n\n/**\n * Check if the file exists on disk already, perform integrity check if so.\n * Otherwise, download it.\n */\nasync function _downloadAsyncManagedEnv(uri, hash, type, name): Promise<string> {\n  const cacheFileId = hash || computeMd5(uri);\n  const localUri = `${FileSystem.cacheDirectory}ExponentAsset-${cacheFileId}.${type}`;\n  const fileInfo = await FileSystem.getInfoAsync(localUri, {\n    md5: true,\n  });\n  if (!fileInfo.exists || (hash !== null && fileInfo.md5 !== hash)) {\n    const { md5 } = await FileSystem.downloadAsync(uri, localUri, {\n      md5: true,\n    });\n    if (hash !== null && md5 !== hash) {\n      throw new Error(\n        `Downloaded file for asset '${name}.${type}' ` +\n          `Located at ${uri} ` +\n          `failed MD5 integrity check`\n      );\n    }\n  }\n  return localUri;\n}\n\n/**\n * Just download the asset, don't perform integrity check because we don't have\n * the hash to compare it with (we don't have hashAssetFiles plugin). Hash is\n * only used for the file name.\n */\nasync function _downloadAsyncUnmanagedEnv(uri, hash, type): Promise<string> {\n  // TODO: does this make sense to bail out if it's already at a file URL\n  // because it's already available locally?\n  if (uri.startsWith('file://')) {\n    return uri;\n  }\n\n  const cacheFileId = hash || computeMd5(uri);\n  const localUri = `${FileSystem.cacheDirectory}ExponentAsset-${cacheFileId}.${type}`;\n\n  // We don't check the FileSystem for an existing version of the asset and we\n  // also don't perform an integrity check!\n  await FileSystem.downloadAsync(uri, localUri);\n  return localUri;\n}\n"]}