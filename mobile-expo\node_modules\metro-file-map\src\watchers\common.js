/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

/**
 * Originally vendored from
 * https://github.com/amasad/sane/blob/64ff3a870c42e84f744086884bf55a4f9c22d376/src/common.js
 */

"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true,
});
exports.assignOptions =
  exports.DELETE_EVENT =
  exports.CHANGE_EVENT =
  exports.ALL_EVENT =
  exports.ADD_EVENT =
    void 0;
exports.isIncluded = isIncluded;
exports.recReaddir = recReaddir;
exports.typeFromStat = typeFromStat;
// $FlowFixMe[untyped-import] - Write libdefs for `anymatch`
const anymatch = require("anymatch");
// $FlowFixMe[untyped-import] - Write libdefs for `micromatch`
const micromatch = require("micromatch");
const platform = require("os").platform();
const path = require("path");
// $FlowFixMe[untyped-import] - Write libdefs for `walker`
const walker = require("walker");

/**
 * Constants
 */
const CHANGE_EVENT = "change";
exports.CHANGE_EVENT = CHANGE_EVENT;
const DELETE_EVENT = "delete";
exports.DELETE_EVENT = DELETE_EVENT;
const ADD_EVENT = "add";
exports.ADD_EVENT = ADD_EVENT;
const ALL_EVENT = "all";
exports.ALL_EVENT = ALL_EVENT;
/**
 * Assigns options to the watcher.
 *
 * @param {NodeWatcher|PollWatcher|WatchmanWatcher} watcher
 * @param {?object} opts
 * @return {boolean}
 * @public
 */
const assignOptions = function (watcher, opts) {
  watcher.globs = opts.glob ?? [];
  watcher.dot = opts.dot ?? false;
  watcher.ignored = opts.ignored ?? false;
  watcher.watchmanDeferStates = opts.watchmanDeferStates;
  if (!Array.isArray(watcher.globs)) {
    watcher.globs = [watcher.globs];
  }
  watcher.doIgnore =
    opts.ignored != null && opts.ignored !== false
      ? anymatch(opts.ignored)
      : () => false;
  if (opts.watchman == true && opts.watchmanPath != null) {
    watcher.watchmanPath = opts.watchmanPath;
  }
  return opts;
};

/**
 * Checks a file relative path against the globs array.
 */
exports.assignOptions = assignOptions;
function isIncluded(type, globs, dot, doIgnore, relativePath) {
  if (doIgnore(relativePath)) {
    return false;
  }
  // For non-regular files or if there are no glob matchers, just respect the
  // `dot` option to filter dotfiles if dot === false.
  if (globs.length === 0 || type !== "f") {
    return dot || micromatch.some(relativePath, "**/*");
  }
  return micromatch.some(relativePath, globs, {
    dot,
  });
}

/**
 * Traverse a directory recursively calling `callback` on every directory.
 */
function recReaddir(
  dir,
  dirCallback,
  fileCallback,
  symlinkCallback,
  endCallback,
  errorCallback,
  ignored
) {
  walker(dir)
    .filterDir((currentDir) => !anymatch(ignored, currentDir))
    .on("dir", normalizeProxy(dirCallback))
    .on("file", normalizeProxy(fileCallback))
    .on("symlink", normalizeProxy(symlinkCallback))
    .on("error", errorCallback)
    .on("end", () => {
      if (platform === "win32") {
        setTimeout(endCallback, 1000);
      } else {
        endCallback();
      }
    });
}

/**
 * Returns a callback that when called will normalize a path and call the
 * original callback
 */
function normalizeProxy(callback) {
  return (filepath, stats) => callback(path.normalize(filepath), stats);
}
function typeFromStat(stat) {
  // Note: These tests are not mutually exclusive - a symlink passes isFile
  if (stat.isSymbolicLink()) {
    return "l";
  }
  if (stat.isDirectory()) {
    return "d";
  }
  if (stat.isFile()) {
    return "f"; // "Regular" file
  }

  return null;
}
